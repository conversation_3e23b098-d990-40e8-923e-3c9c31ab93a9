/*!

  =========================================================
  * Paper Dashboard PRO - V1.2.1
  =========================================================

  * Product Page: https://www.creative-tim.com/product/paper-dashboard-pro
  * Available with purchase of license from https://www.creative-tim.com/product/paper-dashboard-pro
  * Copyright 2017 Creative Tim (https://www.creative-tim.com)
  * License Creative Tim (https://www.creative-tim.com/license)

  =========================================================

*/
/*      light colors - used for select dropdown         */
/*           Social buttons            */
.ct-blue {
  stroke: #7A9E9F !important;
}

.ct-azure {
  stroke: #68B3C8 !important;
}

.ct-green {
  stroke: #7AC29A !important;
}

.ct-orange {
  stroke: #F3BB45 !important;
}

.ct-red {
  stroke: #EB5E28 !important;
}

/*!
Animate.css - http://daneden.me/animate
Licensed under the MIT license - http://opensource.org/licenses/MIT

Copyright (c) 2015 Daniel Eden
*/
.animated {
  -webkit-animation-duration: 1s;
  animation-duration: 1s;
  -webkit-animation-fill-mode: both;
  animation-fill-mode: both;
}

.animated.infinite {
  -webkit-animation-iteration-count: infinite;
  animation-iteration-count: infinite;
}

.animated.hinge {
  -webkit-animation-duration: 2s;
  animation-duration: 2s;
}

.animated.bounceIn,
.animated.bounceOut {
  -webkit-animation-duration: .75s;
  animation-duration: .75s;
}

.animated.flipOutX,
.animated.flipOutY {
  -webkit-animation-duration: .75s;
  animation-duration: .75s;
}

@-webkit-keyframes shake {
  from, to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  10%, 30%, 50%, 70%, 90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }
  20%, 40%, 60%, 80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}
@keyframes shake {
  from, to {
    -webkit-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
  }
  10%, 30%, 50%, 70%, 90% {
    -webkit-transform: translate3d(-10px, 0, 0);
    transform: translate3d(-10px, 0, 0);
  }
  20%, 40%, 60%, 80% {
    -webkit-transform: translate3d(10px, 0, 0);
    transform: translate3d(10px, 0, 0);
  }
}
.shake {
  -webkit-animation-name: shake;
  animation-name: shake;
}

@-webkit-keyframes fadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
@keyframes fadeInDown {
  from {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
  to {
    opacity: 1;
    -webkit-transform: none;
    transform: none;
  }
}
.fadeInDown {
  -webkit-animation-name: fadeInDown;
  animation-name: fadeInDown;
}

@-webkit-keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}
.fadeOut {
  -webkit-animation-name: fadeOut;
  animation-name: fadeOut;
}

@-webkit-keyframes fadeOutDown {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}
@keyframes fadeOutDown {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, 100%, 0);
    transform: translate3d(0, 100%, 0);
  }
}
.fadeOutDown {
  -webkit-animation-name: fadeOutDown;
  animation-name: fadeOutDown;
}

@-webkit-keyframes fadeOutUp {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}
@keyframes fadeOutUp {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
    -webkit-transform: translate3d(0, -100%, 0);
    transform: translate3d(0, -100%, 0);
  }
}
.fadeOutUp {
  -webkit-animation-name: fadeOutUp;
  animation-name: fadeOutUp;
}

body.swal2-in {
  overflow-y: hidden;
}

body.swal2-iosfix {
  position: fixed;
  left: 0;
  right: 0;
}

.swal2-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-align: center;
  -ms-flex-align: center;
  align-items: center;
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  padding: 10px;
  background-color: transparent;
  z-index: 1060;
}

.swal2-container:not(.swal2-in) {
  pointer-events: none;
}

.swal2-container.swal2-fade {
  -webkit-transition: background-color .1s;
  transition: background-color .1s;
}

.swal2-container.swal2-in {
  background-color: rgba(0, 0, 0, 0.4);
}

.swal2-modal {
  background-color: #FFFFFF;
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  border-radius: 5px;
  box-sizing: border-box;
  text-align: center;
  margin: auto;
  overflow-x: hidden;
  overflow-y: auto;
  display: none;
  position: relative;
}
.swal2-modal button {
  margin: 0 5px;
}
.swal2-modal .form-control {
  max-width: 300px;
  margin: 0 auto;
}

.swal2-modal:focus {
  outline: none;
}

.swal2-modal.swal2-loading {
  overflow-y: hidden;
}

.swal2-modal .swal2-title {
  color: #595959;
  font-size: 30px;
  text-align: center;
  font-weight: 600;
  text-transform: none;
  position: relative;
  margin: 0;
  padding: 0;
  line-height: 60px;
  display: block;
}

.swal2-modal .swal2-spacer {
  height: 10px;
  color: transparent;
  border: 0;
}

.swal2-modal .swal2-styled {
  border: 0;
  border-radius: 3px;
  box-shadow: none;
  color: #fff;
  cursor: pointer;
  font-size: 17px;
  font-weight: 500;
  margin: 0 5px;
  padding: 10px 32px;
}

.swal2-modal .swal2-styled:not(.swal2-loading)[disabled] {
  opacity: .4;
  cursor: no-drop;
}

.swal2-modal .swal2-styled.swal2-loading {
  box-sizing: border-box;
  border: 4px solid transparent;
  border-color: transparent;
  width: 40px;
  height: 40px;
  padding: 0;
  margin: -2px 30px;
  vertical-align: top;
  background-color: transparent !important;
  color: transparent;
  cursor: default;
  border-radius: 100%;
  -webkit-animation: rotate-loading 1.5s linear 0s infinite normal;
  animation: rotate-loading 1.5s linear 0s infinite normal;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.swal2-modal :not(.swal2-styled).swal2-loading::after {
  display: inline-block;
  content: '';
  margin-left: 5px;
  vertical-align: -1px;
  height: 6px;
  width: 6px;
  border: 3px solid #999999;
  border-right-color: transparent;
  border-radius: 50%;
  -webkit-animation: rotate-loading 1.5s linear 0s infinite normal;
  animation: rotate-loading 1.5s linear 0s infinite normal;
}

.swal2-modal .swal2-image {
  margin: 20px auto;
  max-width: 100%;
}

.swal2-modal .swal2-close {
  font-size: 36px;
  line-height: 36px;
  font-family: serif;
  position: absolute;
  top: 5px;
  right: 13px;
  cursor: pointer;
  color: #cccccc;
  -webkit-transition: color .1s ease;
  transition: color .1s ease;
}

.swal2-modal .swal2-close:hover {
  color: #d55;
}

.swal2-modal > .swal2-input,
.swal2-modal > .swal2-file,
.swal2-modal > .swal2-textarea,
.swal2-modal > .swal2-select,
.swal2-modal > .swal2-radio,
.swal2-modal > .swal2-checkbox {
  display: none;
}

.swal2-modal .swal2-content {
  font-size: 18px;
  text-align: center;
  font-weight: 300;
  position: relative;
  float: none;
  margin: 0;
  padding: 0;
  line-height: normal;
  color: #545454;
}

.swal2-modal .swal2-input,
.swal2-modal .swal2-file,
.swal2-modal .swal2-textarea,
.swal2-modal .swal2-select,
.swal2-modal .swal2-radio,
.swal2-modal .swal2-checkbox {
  margin: 20px auto;
}

.swal2-modal .swal2-input,
.swal2-modal .swal2-file,
.swal2-modal .swal2-textarea {
  width: 100%;
  box-sizing: border-box;
  border-radius: 3px;
  border: 1px solid #d9d9d9;
  font-size: 18px;
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06);
  -webkit-transition: border-color box-shadow .3s;
  transition: border-color box-shadow .3s;
}

.swal2-modal .swal2-input.swal2-inputerror,
.swal2-modal .swal2-file.swal2-inputerror,
.swal2-modal .swal2-textarea.swal2-inputerror {
  border-color: #f06e57;
}

.swal2-modal .swal2-input:focus,
.swal2-modal .swal2-file:focus,
.swal2-modal .swal2-textarea:focus {
  outline: none;
  box-shadow: 0 0 3px #c4e6f5;
  border: 1px solid #b4dbed;
}

.swal2-modal .swal2-input:focus::-webkit-input-placeholder,
.swal2-modal .swal2-file:focus::-webkit-input-placeholder,
.swal2-modal .swal2-textarea:focus::-webkit-input-placeholder {
  -webkit-transition: opacity .3s .03s ease;
  transition: opacity .3s .03s ease;
  opacity: .8;
}

.swal2-modal .swal2-input:focus::-moz-placeholder,
.swal2-modal .swal2-file:focus::-moz-placeholder,
.swal2-modal .swal2-textarea:focus::-moz-placeholder {
  -webkit-transition: opacity .3s .03s ease;
  transition: opacity .3s .03s ease;
  opacity: .8;
}

.swal2-modal .swal2-input:focus:-ms-input-placeholder,
.swal2-modal .swal2-file:focus:-ms-input-placeholder,
.swal2-modal .swal2-textarea:focus:-ms-input-placeholder {
  -webkit-transition: opacity .3s .03s ease;
  transition: opacity .3s .03s ease;
  opacity: .8;
}

.swal2-modal .swal2-input:focus::placeholder,
.swal2-modal .swal2-file:focus::placeholder,
.swal2-modal .swal2-textarea:focus::placeholder {
  -webkit-transition: opacity .3s .03s ease;
  transition: opacity .3s .03s ease;
  opacity: .8;
}

.swal2-modal .swal2-input::-webkit-input-placeholder,
.swal2-modal .swal2-file::-webkit-input-placeholder,
.swal2-modal .swal2-textarea::-webkit-input-placeholder {
  color: #e6e6e6;
}

.swal2-modal .swal2-input::-moz-placeholder,
.swal2-modal .swal2-file::-moz-placeholder,
.swal2-modal .swal2-textarea::-moz-placeholder {
  color: #e6e6e6;
}

.swal2-modal .swal2-input:-ms-input-placeholder,
.swal2-modal .swal2-file:-ms-input-placeholder,
.swal2-modal .swal2-textarea:-ms-input-placeholder {
  color: #e6e6e6;
}

.swal2-modal .swal2-input::placeholder,
.swal2-modal .swal2-file::placeholder,
.swal2-modal .swal2-textarea::placeholder {
  color: #e6e6e6;
}

.swal2-modal .swal2-range input {
  float: left;
  width: 80%;
}

.swal2-modal .swal2-range output {
  float: right;
  width: 20%;
  font-size: 20px;
  font-weight: 600;
  text-align: center;
}

.swal2-modal .swal2-range input,
.swal2-modal .swal2-range output {
  height: 43px;
  line-height: 43px;
  vertical-align: middle;
  margin: 20px auto;
  padding: 0;
}

.swal2-modal .swal2-input {
  height: 43px;
  padding: 0 12px;
}

.swal2-modal .swal2-input[type='number'] {
  max-width: 150px;
}

.swal2-modal .swal2-file {
  font-size: 20px;
}

.swal2-modal .swal2-textarea {
  height: 108px;
  padding: 12px;
}

.swal2-modal .swal2-select {
  color: #545454;
  font-size: inherit;
  padding: 5px 10px;
  min-width: 40%;
  max-width: 100%;
}

.swal2-modal .swal2-radio {
  border: 0;
}

.swal2-modal .swal2-radio label:not(:first-child) {
  margin-left: 20px;
}

.swal2-modal .swal2-radio input,
.swal2-modal .swal2-radio span {
  vertical-align: middle;
}

.swal2-modal .swal2-radio input {
  margin: 0 3px 0 0;
}

.swal2-modal .swal2-checkbox {
  color: #545454;
}

.swal2-modal .swal2-checkbox input,
.swal2-modal .swal2-checkbox span {
  vertical-align: middle;
}

.swal2-modal .swal2-validationerror {
  background-color: #f0f0f0;
  margin: 0 -20px;
  overflow: hidden;
  padding: 10px;
  color: gray;
  font-size: 16px;
  font-weight: 300;
  display: none;
}

.swal2-modal .swal2-validationerror::before {
  content: '!';
  display: inline-block;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #ea7d7d;
  color: #fff;
  line-height: 24px;
  text-align: center;
  margin-right: 10px;
}

@supports (-ms-accelerator: true) {
  .swal2-range input {
    width: 100% !important;
  }

  .swal2-range output {
    display: none;
  }
}
@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {
  .swal2-range input {
    width: 100% !important;
  }

  .swal2-range output {
    display: none;
  }
}
.swal2-icon {
  width: 80px;
  height: 80px;
  border: 4px solid transparent;
  border-radius: 50%;
  margin: 20px auto 30px;
  padding: 0;
  position: relative;
  box-sizing: content-box;
  cursor: default;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.swal2-icon.swal2-error {
  border-color: #f27474;
}

.swal2-icon.swal2-error .x-mark {
  position: relative;
  display: block;
}

.swal2-icon.swal2-error .line {
  position: absolute;
  height: 5px;
  width: 47px;
  background-color: #f27474;
  display: block;
  top: 37px;
  border-radius: 2px;
}

.swal2-icon.swal2-error .line.left {
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
  left: 17px;
}

.swal2-icon.swal2-error .line.right {
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  right: 16px;
}

.swal2-icon.swal2-warning .body {
  position: absolute;
  width: 5px;
  height: 47px;
  left: 50%;
  top: 10px;
  border-radius: 2px;
  margin-left: -2px;
  background-color: #FFA534;
}

.swal2-icon.swal2-warning .dot {
  position: absolute;
  width: 7px;
  height: 7px;
  border-radius: 50%;
  margin-left: -3px;
  left: 50%;
  bottom: 10px;
  background-color: #FFA534;
}

.swal2-icon.swal2-warning {
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  color: #F3BB45;
  border-color: #F3BB45;
  font-size: 60px;
  line-height: 80px;
  text-align: center;
}

.swal2-icon.swal2-info {
  font-family: 'Open Sans', sans-serif;
  color: #68B3C8;
  border-color: #68B3C8;
  font-size: 60px;
  line-height: 80px;
  text-align: center;
}

.swal2-icon.swal2-question {
  font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
  color: #87adbd;
  border-color: #c9dae1;
  font-size: 60px;
  line-height: 80px;
  text-align: center;
}

.swal2-icon.swal2-success {
  border-color: #7AC29A;
}

.swal2-icon.swal2-success::before, .swal2-icon.swal2-success::after {
  content: '';
  border-radius: 50%;
  position: absolute;
  width: 60px;
  height: 120px;
  background: #fff;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

.swal2-icon.swal2-success::before {
  border-radius: 120px 0 0 120px;
  top: -7px;
  left: -33px;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  -webkit-transform-origin: 60px 60px;
  transform-origin: 60px 60px;
}

.swal2-icon.swal2-success::after {
  border-radius: 0 120px 120px 0;
  top: -11px;
  left: 30px;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
  -webkit-transform-origin: 0 60px;
  transform-origin: 0 60px;
}

.swal2-icon.swal2-success .placeholder {
  width: 80px;
  height: 80px;
  border: 4px solid rgba(165, 220, 134, 0.2);
  border-radius: 50%;
  box-sizing: content-box;
  position: absolute;
  left: -4px;
  top: -4px;
  z-index: 2;
}

.swal2-icon.swal2-success .fix {
  width: 7px;
  height: 90px;
  background-color: #fff;
  position: absolute;
  left: 28px;
  top: 8px;
  z-index: 1;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.swal2-icon.swal2-success .line {
  height: 5px;
  background-color: #a5dc86;
  display: block;
  border-radius: 2px;
  position: absolute;
  z-index: 2;
}

.swal2-icon.swal2-success .line.tip {
  width: 25px;
  left: 14px;
  top: 46px;
  -webkit-transform: rotate(45deg);
  transform: rotate(45deg);
}

.swal2-icon.swal2-success .line.long {
  width: 47px;
  right: 8px;
  top: 38px;
  -webkit-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

.swal2-progresssteps {
  font-weight: 600;
  margin: 0 0 20px;
  padding: 0;
}

.swal2-progresssteps li {
  display: inline-block;
  position: relative;
}

.swal2-progresssteps .swal2-progresscircle {
  background: #3085d6;
  border-radius: 2em;
  color: #fff;
  height: 2em;
  line-height: 2em;
  text-align: center;
  width: 2em;
  z-index: 20;
}

.swal2-progresssteps .swal2-progresscircle:first-child {
  margin-left: 0;
}

.swal2-progresssteps .swal2-progresscircle:last-child {
  margin-right: 0;
}

.swal2-progresssteps .swal2-progresscircle.swal2-activeprogressstep {
  background: #3085d6;
}

.swal2-progresssteps .swal2-progresscircle.swal2-activeprogressstep ~ .swal2-progresscircle {
  background: #add8e6;
}

.swal2-progresssteps .swal2-progresscircle.swal2-activeprogressstep ~ .swal2-progressline {
  background: #add8e6;
}

.swal2-progresssteps .swal2-progressline {
  background: #3085d6;
  height: .4em;
  margin: 0 -1px;
  z-index: 10;
}

[class^='swal2'] {
  -webkit-tap-highlight-color: transparent;
}

@-webkit-keyframes showSweetAlert {
  0% {
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
  }
  45% {
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }
  80% {
    -webkit-transform: scale(0.95);
    transform: scale(0.95);
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@keyframes showSweetAlert {
  0% {
    -webkit-transform: scale(0.7);
    transform: scale(0.7);
  }
  45% {
    -webkit-transform: scale(1.05);
    transform: scale(1.05);
  }
  80% {
    -webkit-transform: scale(0.95);
    transform: scale(0.95);
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
@-webkit-keyframes hideSweetAlert {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    opacity: 0;
  }
}
@keyframes hideSweetAlert {
  0% {
    -webkit-transform: scale(1);
    transform: scale(1);
    opacity: 1;
  }
  100% {
    -webkit-transform: scale(0.5);
    transform: scale(0.5);
    opacity: 0;
  }
}
.swal2-show {
  -webkit-animation: showSweetAlert 0.3s;
  animation: showSweetAlert 0.3s;
}

.swal2-show.swal2-noanimation {
  -webkit-animation: none;
  animation: none;
}

.swal2-hide {
  -webkit-animation: hideSweetAlert 0.15s forwards;
  animation: hideSweetAlert 0.15s forwards;
}

.swal2-hide.swal2-noanimation {
  -webkit-animation: none;
  animation: none;
}

@-webkit-keyframes animate-success-tip {
  0% {
    width: 0;
    left: 1px;
    top: 19px;
  }
  54% {
    width: 0;
    left: 1px;
    top: 19px;
  }
  70% {
    width: 50px;
    left: -8px;
    top: 37px;
  }
  84% {
    width: 17px;
    left: 21px;
    top: 48px;
  }
  100% {
    width: 25px;
    left: 14px;
    top: 45px;
  }
}
@keyframes animate-success-tip {
  0% {
    width: 0;
    left: 1px;
    top: 19px;
  }
  54% {
    width: 0;
    left: 1px;
    top: 19px;
  }
  70% {
    width: 50px;
    left: -8px;
    top: 37px;
  }
  84% {
    width: 17px;
    left: 21px;
    top: 48px;
  }
  100% {
    width: 25px;
    left: 14px;
    top: 45px;
  }
}
@-webkit-keyframes animate-success-long {
  0% {
    width: 0;
    right: 46px;
    top: 54px;
  }
  65% {
    width: 0;
    right: 46px;
    top: 54px;
  }
  84% {
    width: 55px;
    right: 0;
    top: 35px;
  }
  100% {
    width: 47px;
    right: 8px;
    top: 38px;
  }
}
@keyframes animate-success-long {
  0% {
    width: 0;
    right: 46px;
    top: 54px;
  }
  65% {
    width: 0;
    right: 46px;
    top: 54px;
  }
  84% {
    width: 55px;
    right: 0;
    top: 35px;
  }
  100% {
    width: 47px;
    right: 8px;
    top: 38px;
  }
}
@-webkit-keyframes rotatePlaceholder {
  0% {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
  }
  5% {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
  }
  12% {
    -webkit-transform: rotate(-405deg);
    transform: rotate(-405deg);
  }
  100% {
    -webkit-transform: rotate(-405deg);
    transform: rotate(-405deg);
  }
}
@keyframes rotatePlaceholder {
  0% {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
  }
  5% {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
  }
  12% {
    -webkit-transform: rotate(-405deg);
    transform: rotate(-405deg);
  }
  100% {
    -webkit-transform: rotate(-405deg);
    transform: rotate(-405deg);
  }
}
.animate-success-tip {
  -webkit-animation: animate-success-tip 0.75s;
  animation: animate-success-tip 0.75s;
}

.animate-success-long {
  -webkit-animation: animate-success-long 0.75s;
  animation: animate-success-long 0.75s;
}

.swal2-success.animate::after {
  -webkit-animation: rotatePlaceholder 4.25s ease-in;
  animation: rotatePlaceholder 4.25s ease-in;
}

@-webkit-keyframes animate-error-icon {
  0% {
    -webkit-transform: rotateX(100deg);
    transform: rotateX(100deg);
    opacity: 0;
  }
  100% {
    -webkit-transform: rotateX(0deg);
    transform: rotateX(0deg);
    opacity: 1;
  }
}
@keyframes animate-error-icon {
  0% {
    -webkit-transform: rotateX(100deg);
    transform: rotateX(100deg);
    opacity: 0;
  }
  100% {
    -webkit-transform: rotateX(0deg);
    transform: rotateX(0deg);
    opacity: 1;
  }
}
.animate-error-icon {
  -webkit-animation: animate-error-icon 0.5s;
  animation: animate-error-icon 0.5s;
}

@-webkit-keyframes animate-x-mark {
  0% {
    -webkit-transform: scale(0.4);
    transform: scale(0.4);
    margin-top: 26px;
    opacity: 0;
  }
  50% {
    -webkit-transform: scale(0.4);
    transform: scale(0.4);
    margin-top: 26px;
    opacity: 0;
  }
  80% {
    -webkit-transform: scale(1.15);
    transform: scale(1.15);
    margin-top: -6px;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    margin-top: 0;
    opacity: 1;
  }
}
@keyframes animate-x-mark {
  0% {
    -webkit-transform: scale(0.4);
    transform: scale(0.4);
    margin-top: 26px;
    opacity: 0;
  }
  50% {
    -webkit-transform: scale(0.4);
    transform: scale(0.4);
    margin-top: 26px;
    opacity: 0;
  }
  80% {
    -webkit-transform: scale(1.15);
    transform: scale(1.15);
    margin-top: -6px;
  }
  100% {
    -webkit-transform: scale(1);
    transform: scale(1);
    margin-top: 0;
    opacity: 1;
  }
}
.animate-x-mark {
  -webkit-animation: animate-x-mark 0.5s;
  animation: animate-x-mark 0.5s;
}

@-webkit-keyframes pulse-warning {
  0% {
    border-color: #FAE6A4;
  }
  100% {
    border-color: #FFA534;
  }
}
@keyframes pulse-warning {
  0% {
    border-color: #FAE6A4;
  }
  100% {
    border-color: #FFA534;
  }
}
.pulse-warning {
  -webkit-animation: pulse-warning 0.75s infinite alternate;
  animation: pulse-warning 0.75s infinite alternate;
}

@-webkit-keyframes rotate-loading {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
@keyframes rotate-loading {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }
  100% {
    -webkit-transform: rotate(360deg);
    transform: rotate(360deg);
  }
}
/**
 * <AUTHOR> wen <<EMAIL>>
 * version: 1.8.1
 * https://github.com/wenzhixin/bootstrap-table/
 */
.bootstrap-table .table {
  margin-bottom: 0 !important;
  border-bottom: 1px solid #cfcfca;
  border-collapse: collapse !important;
  border-radius: 1px;
}

.bootstrap-table .table,
.bootstrap-table .table > tbody > tr > th,
.bootstrap-table .table > tfoot > tr > th,
.bootstrap-table .table > thead > tr > td,
.bootstrap-table .table > tbody > tr > td,
.bootstrap-table .table > tfoot > tr > td {
  padding: 8px !important;
}

.bootstrap-table .table > tbody > .selected {
  background-color: rgba(122, 158, 159, 0.2);
}

.bootstrap-table .table.table-no-bordered > thead > tr > th,
.bootstrap-table .table.table-no-bordered > tbody > tr > td {
  border-right: 2px solid transparent;
}

.fixed-table-container {
  position: relative;
  clear: both;
}

.fixed-table-container.table-no-bordered {
  border: 1px solid transparent;
}

.fixed-table-footer,
.fixed-table-header {
  overflow: hidden;
}

.fixed-table-footer {
  border-top: 1px solid #cfcfca;
}

.fixed-table-body {
  height: 100%;
  overflow: auto;
}

.fixed-table-container table {
  width: 100%;
}

.fixed-table-container thead th {
  height: 0;
  padding: 0;
  margin: 0;
}

.fixed-table-container thead th:first-child {
  border-left: none;
}

.fixed-table-container thead th .th-inner {
  padding: 0 8px;
  line-height: 24px;
  vertical-align: top;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.fixed-table-container thead th .sortable {
  cursor: pointer;
  background-repeat: no-repeat;
  padding-right: 30px;
}

.fixed-table-container thead th .both {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAQAAADYWf5HAAAAkElEQVQoz7X QMQ5AQBCF4dWQSJxC5wwax1Cq1e7BAdxD5SL+Tq/QCM1oNiJidwox0355mXnG/DrEtIQ6azioNZQxI0ykPhTQIwhCR+BmBYtlK7kLJYwWCcJA9M4qdrZrd8pPjZWPtOqdRQy320YSV17OatFC4euts6z39GYMKRPCTKY9UnPQ6P+GtMRfGtPnBCiqhAeJPmkqAAAAAElFTkSuQmCC");
  background-position: right 2px;
}

.fixed-table-container thead th .asc {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAG2YAABzjgAA+swAAIT6AAB5gQAA/RMAADBtAAASKQ0eJk4AAACASURBVHja7NKhDcJQFIbRV1MSEqaoYwYMYyARR6HZowzAHqguUolHYEgxD9MmTYPpA1JEb/KZK476Q4wxfKvwc6x/WKJE3v+nYkc8cfgIwxpXRNQokjBkOLdQ1wlZCrbFbYDdsRmFYYVqAHVdsBiD7dHg8aYGu2l2NmMz9s/YawDpU8qkYQTHqgAAAABJRU5ErkJggg==");
  background-position: right 5px;
}

.fixed-table-container thead th .desc {
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABMAAAATCAYAAAByUDbMAAAACXBIWXMAAAsTAAALEwEAmpwYAAAAIGNIUk0AAG2YAABzjgAA+swAAIT6AAB5gQAA/RMAADBtAAASKQ0eJk4AAACFSURBVHja7NKhDcJQFIbRWwMJCVPgmKGGMZCIo9DsAQOwRxWLIPEIDCnmYR4JaSp4DYKQis/8NznqRkopvlWM2Ij9BYY1Wtx7arEpwaZokHo6Yf4xFhGBGrcOdMUqIqIUq3DoYEdUxVgGFzhn6ILl61aMZXCLB3bv+1Bsgj1mg7CfeNrnAMXSyqQtsCNeAAAAAElFTkSuQmCC");
  background-position: right 0px;
}

.fixed-table-container th.detail {
  width: 30px;
}

.fixed-table-container tbody tr:first-child td {
  border-top: none;
}

.fixed-table-container tbody td:first-child {
  border-left: none;
}

/* the same color with .active */
.fixed-table-container tbody .selected td {
  background-color: rgba(245, 245, 245, 0.34);
}

.fixed-table-container .bs-checkbox {
  text-align: center;
}

.fixed-table-container .bs-checkbox .th-inner {
  padding: 8px 0;
}

.fixed-table-container input[type="radio"],
.fixed-table-container input[type="checkbox"] {
  margin: 0 auto !important;
  cursor: pointer;
}

.fixed-table-container .no-records-found {
  text-align: center;
  background-color: #FFFFFF !important;
  height: 340px;
  border-radius: 10px;
  width: 100%;
  vertical-align: middle;
}

.fixed-table-container .no-records-found td {
  font-weight: 300;
  font-size: 1.5em;
}

.fixed-table-pagination div.pagination,
.fixed-table-pagination .pagination-detail {
  margin-top: 10px;
  margin-bottom: 10px;
}

.fixed-table-pagination div.pagination .pagination {
  margin: 0;
}

.fixed-table-pagination .pagination a {
  padding: 6px 12px;
  line-height: 1.428571429;
}

.fixed-table-pagination .pagination-info {
  line-height: 34px;
}

.fixed-table-pagination .btn-group {
  position: relative;
  display: inline-block;
  vertical-align: middle;
}

.fixed-table-pagination .page-list {
  display: inline-block;
}

.fixed-table-toolbar {
  padding: 5px 0;
}

.fixed-table-toolbar .columns-left {
  margin-right: 5px;
}

.fixed-table-toolbar .columns-right {
  margin-left: 5px;
}

.fixed-table-toolbar .columns .btn {
  margin: 0 2px;
  border-radius: 30px;
}

.columns-right .btn-group .btn {
  margin-right: 0;
}

.columns-left .btn:first-child {
  margin-left: 0;
}

.pull-right .pagination .page-last a {
  margin-right: 0;
}

.pull-left .pagination .page-first a {
  margin-left: 0;
}

.bootstrap-table .td-actions .th-inner {
  padding-right: 17px;
}

.fixed-table-toolbar .columns label {
  display: block;
  padding: 10px 20px;
  border-bottom: 1px solid #E3E3E3;
  clear: both;
  margin-bottom: 0;
  font-weight: normal;
  line-height: 1.428571429;
}

.fixed-table-toolbar .columns li:last-child label {
  border-bottom: none;
}

.fixed-table-toolbar .bars,
.fixed-table-toolbar .search,
.fixed-table-toolbar .columns {
  position: relative;
  margin-top: 10px;
  margin-bottom: 10px;
  line-height: 34px;
}

.fixed-table-toolbar .search {
  margin-left: 0;
}

.fixed-table-pagination li.disabled a {
  pointer-events: none;
  cursor: default;
}

.fixed-table-loading {
  display: none;
  position: absolute;
  top: 42px;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 99;
  background-color: #FFFFFF;
  text-align: center;
}

.fixed-table-body .card-view .title {
  font-weight: bold;
  display: inline-block;
  min-width: 30%;
  text-align: left !important;
}

/* support bootstrap 2 */
.fixed-table-body thead th .th-inner {
  box-sizing: border-box;
}

.table th, .table td {
  vertical-align: middle;
  box-sizing: border-box;
}

.fixed-table-toolbar .btn-group > .btn-group {
  display: inline-block;
  margin-left: -1px !important;
}

.fixed-table-toolbar .dropdown-menu label {
  cursor: pointer;
}

.fixed-table-toolbar .btn-group > .btn-group > .btn {
  border-radius: 0;
}

.fixed-table-toolbar .btn-group > .btn-group:first-child > .btn {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.fixed-table-toolbar .btn-group > .btn-group:last-child > .btn {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

/* support bootstrap 3 */
.bootstrap-table .table thead > tr > th {
  padding: 0;
  margin: 0;
}

.pull-right .dropdown-menu {
  right: 0;
  left: auto;
}

/* calculate scrollbar width */
p.fixed-table-scroll-inner {
  width: 100%;
  height: 200px;
}

div.fixed-table-scroll-outer {
  top: 0;
  left: 0;
  visibility: hidden;
  width: 200px;
  height: 150px;
  overflow: hidden;
}

.bootstrap-table .fixed-table-pagination:after {
  display: table;
  content: "";
  clear: both;
}
.bootstrap-table .fixed-table-pagination .page-list .btn {
  border-radius: 30px;
}
.bootstrap-table .fixed-table-pagination .page-list .btn-group {
  margin-right: 5px;
}
.bootstrap-table .fixed-table-pagination div.pagination,
.bootstrap-table .fixed-table-pagination .pagination-detail {
  margin-top: 15px;
  margin-bottom: 15px;
}
.bootstrap-table .table.table-striped > thead > tr > td, .bootstrap-table .table.table-striped > tbody > tr > td, .bootstrap-table .table.table-striped > tfoot > tr > td {
  border: none;
}

table.dataTable {
  clear: both;
  margin-top: 6px !important;
  margin-bottom: 6px !important;
  max-width: none !important;
  border-collapse: separate !important;
}

table.dataTable td,
table.dataTable th {
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
}

table.dataTable td.dataTables_empty,
table.dataTable th.dataTables_empty {
  text-align: center;
}

table.dataTable.nowrap th,
table.dataTable.nowrap td {
  white-space: nowrap;
}

div.dataTables_wrapper div.dataTables_length label {
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
}

div.dataTables_wrapper div.dataTables_length select {
  width: 75px;
  display: inline-block;
}

div.dataTables_wrapper div.dataTables_filter {
  text-align: right;
}

div.dataTables_wrapper div.dataTables_filter label {
  font-weight: normal;
  white-space: nowrap;
  text-align: left;
}

div.dataTables_wrapper div.dataTables_filter input {
  margin-left: 0.5em;
  display: inline-block;
  width: auto;
}

div.dataTables_wrapper div.dataTables_info {
  padding-top: 8px;
  white-space: nowrap;
}

div.dataTables_wrapper div.dataTables_paginate {
  margin: 0;
  white-space: nowrap;
  text-align: right;
}

div.dataTables_wrapper div.dataTables_paginate ul.pagination {
  margin: 2px 0;
  white-space: nowrap;
}

div.dataTables_wrapper div.dataTables_processing {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 200px;
  margin-left: -100px;
  margin-top: -26px;
  text-align: center;
  padding: 1em 0;
}

table.dataTable thead > tr > th.sorting_asc, table.dataTable thead > tr > th.sorting_desc, table.dataTable thead > tr > th.sorting,
table.dataTable thead > tr > td.sorting_asc,
table.dataTable thead > tr > td.sorting_desc,
table.dataTable thead > tr > td.sorting {
  padding-right: 30px;
}

table.dataTable thead > tr > th:active,
table.dataTable thead > tr > td:active {
  outline: none;
}

table.dataTable thead .sorting,
table.dataTable thead .sorting_asc,
table.dataTable thead .sorting_desc,
table.dataTable thead .sorting_asc_disabled,
table.dataTable thead .sorting_desc_disabled {
  cursor: pointer;
  position: relative;
}

table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:after,
table.dataTable thead .sorting_asc_disabled:after,
table.dataTable thead .sorting_desc_disabled:after {
  position: absolute;
  bottom: 8px;
  right: 8px;
  display: block;
  font-family: 'Glyphicons Halflings';
  opacity: 0.5;
}

table.dataTable thead .sorting:after {
  opacity: 0.2;
  content: "\e150";
  /* sort */
}

table.dataTable thead .sorting_asc:after {
  content: "\e155";
  /* sort-by-attributes */
}

table.dataTable thead .sorting_desc:after {
  content: "\e156";
  /* sort-by-attributes-alt */
}

table.dataTable thead .sorting_asc_disabled:after,
table.dataTable thead .sorting_desc_disabled:after {
  color: #eee;
}

div.dataTables_scrollHead table.dataTable {
  margin-bottom: 0 !important;
}

div.dataTables_scrollBody table {
  border-top: none;
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

div.dataTables_scrollBody table thead .sorting:after,
div.dataTables_scrollBody table thead .sorting_asc:after,
div.dataTables_scrollBody table thead .sorting_desc:after {
  display: none;
}

div.dataTables_scrollBody table tbody tr:first-child th,
div.dataTables_scrollBody table tbody tr:first-child td {
  border-top: none;
}

div.dataTables_scrollFoot table {
  margin-top: 0 !important;
  border-top: none;
}

@media screen and (max-width: 767px) {
  div.dataTables_wrapper div.dataTables_length,
  div.dataTables_wrapper div.dataTables_filter,
  div.dataTables_wrapper div.dataTables_info,
  div.dataTables_wrapper div.dataTables_paginate {
    text-align: center;
  }
}
table.dataTable.table-condensed > thead > tr > th {
  padding-right: 20px;
}

table.dataTable.table-condensed .sorting:after,
table.dataTable.table-condensed .sorting_asc:after,
table.dataTable.table-condensed .sorting_desc:after {
  top: 6px;
  right: 6px;
}

table.table-bordered.dataTable th,
table.table-bordered.dataTable td {
  border-left-width: 0;
}

table.table-bordered.dataTable th:last-child, table.table-bordered.dataTable th:last-child,
table.table-bordered.dataTable td:last-child,
table.table-bordered.dataTable td:last-child {
  border-right-width: 0;
}

table.table-bordered.dataTable tbody th,
table.table-bordered.dataTable tbody td {
  border-bottom-width: 0;
}

div.dataTables_scrollHead table.table-bordered {
  border-bottom-width: 0;
}

div.table-responsive > div.dataTables_wrapper > div.row {
  margin: 0;
}

div.table-responsive > div.dataTables_wrapper > div.row > div[class^="col-"]:first-child {
  padding-left: 0;
}

div.table-responsive > div.dataTables_wrapper > div.row > div[class^="col-"]:last-child {
  padding-right: 0;
}

table.dataTable thead .sorting:after,
table.dataTable thead .sorting_asc:after,
table.dataTable thead .sorting_desc:after,
table.dataTable thead .sorting_asc_disabled:after,
table.dataTable thead .sorting_desc_disabled:after {
  position: relative;
  display: inline-block;
  bottom: 0px;
  right: -7px;
  font-family: 'FontAwesome';
  opacity: 0.8;
}
table.dataTable thead .disabled-sorting.sorting:after,
table.dataTable thead .disabled-sorting.sorting_asc:after,
table.dataTable thead .disabled-sorting.sorting_desc:after,
table.dataTable thead .disabled-sorting.sorting_asc_disabled:after,
table.dataTable thead .disabled-sorting.sorting_desc_disabled:after {
  display: none;
}
table.dataTable thead .sorting:after {
  opacity: 0.4;
  content: "\f0dc";
}
table.dataTable thead .sorting_asc:after {
  content: "\f0de";
  top: 3px;
}
table.dataTable thead .sorting_desc:after {
  content: "\f0dd";
  top: -3px;
}
table.dataTable .table-hover > tbody > tr:hover {
  background-color: #e8e7df;
}
table.dataTable .table > thead > tr > th,
table.dataTable .table > tbody > tr > th,
table.dataTable .table > tfoot > tr > th,
table.dataTable .table > thead > tr > td,
table.dataTable .table > tbody > tr > td,
table.dataTable .table > tfoot > tr > td {
  padding: 8px !important;
  outline: 0;
}
table.dataTable .btn.btn-icon {
  margin: 0 3px;
  padding: 5px 8px;
}

.dataTables_paginate a {
  outline: 0;
}

table.dataTable.dtr-inline.collapsed > tbody > tr > td.child,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.child,
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dataTables_empty {
  cursor: default !important;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dataTables_empty:before {
  display: none !important;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td:first-child,
table.dataTable.dtr-inline.collapsed > tbody > tr > th:first-child {
  position: relative;
  padding-left: 30px;
  cursor: pointer;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td:first-child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th:first-child:before {
  top: 50%;
  margin-top: -9px;
  left: 4px;
  height: 18px;
  width: 18px;
  display: block;
  position: absolute;
  color: #518607;
  border: 0px solid white;
  border-radius: 14px;
  box-shadow: 0 0 3px #444;
  box-sizing: content-box;
  text-align: center;
  font-family: 'Courier New', Courier, monospace;
  line-height: 18px;
  content: '+';
  background-color: #FFF;
}
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td:first-child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th:first-child:before {
  content: '-';
  color: #ED362C;
}
table.dataTable.dtr-inline.collapsed > tbody > tr.child td:before {
  display: none;
}

table.dataTable.dtr-inline.collapsed.compact > tbody > tr > td:first-child,
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > th:first-child {
  padding-left: 27px;
}
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > td:first-child:before,
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > th:first-child:before {
  top: 5px;
  left: 4px;
  height: 14px;
  width: 14px;
  border-radius: 14px;
  line-height: 14px;
  text-indent: 3px;
}

table.dataTable.dtr-column > tbody > tr > td.control,
table.dataTable.dtr-column > tbody > tr > th.control {
  position: relative;
  cursor: pointer;
}
table.dataTable.dtr-column > tbody > tr > td.control:before,
table.dataTable.dtr-column > tbody > tr > th.control:before {
  top: 50%;
  left: 50%;
  height: 16px;
  width: 16px;
  margin-top: -10px;
  margin-left: -10px;
  display: block;
  position: absolute;
  color: white;
  border: 2px solid white;
  border-radius: 14px;
  box-shadow: 0 0 3px #444;
  box-sizing: content-box;
  text-align: center;
  font-family: 'Courier New', Courier, monospace;
  line-height: 14px;
  content: '+';
  background-color: #31b131;
}
table.dataTable.dtr-column > tbody > tr.parent td.control:before,
table.dataTable.dtr-column > tbody > tr.parent th.control:before {
  content: '-';
  background-color: #d33333;
}

table.dataTable > tbody > tr.child {
  padding: 0.5em 1em;
}
table.dataTable > tbody > tr.child:hover {
  background: transparent !important;
}
table.dataTable > tbody > tr.child ul {
  display: inline-block;
  list-style-type: none;
  margin: 0;
  padding: 0;
}
table.dataTable > tbody > tr.child ul li {
  border-bottom: 1px solid #efefef;
  padding: 0.5em 0;
}
table.dataTable > tbody > tr.child ul li:first-child {
  padding-top: 0;
}
table.dataTable > tbody > tr.child ul li:last-child {
  border-bottom: none;
}
table.dataTable > tbody > tr.child span.dtr-title {
  display: inline-block;
  min-width: 75px;
  font-weight: bold;
}

div.dtr-modal {
  position: fixed;
  box-sizing: border-box;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 100;
  padding: 10em 1em;
}
div.dtr-modal div.dtr-modal-display {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 50%;
  height: 50%;
  overflow: auto;
  margin: auto;
  z-index: 102;
  overflow: auto;
  background-color: #f5f5f7;
  border: 1px solid black;
  border-radius: 0.5em;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.6);
}
div.dtr-modal div.dtr-modal-content {
  position: relative;
  padding: 1em;
}
div.dtr-modal div.dtr-modal-close {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 22px;
  height: 22px;
  border: 1px solid #eaeaea;
  background-color: #f9f9f9;
  text-align: center;
  border-radius: 3px;
  cursor: pointer;
  z-index: 12;
}
div.dtr-modal div.dtr-modal-close:hover {
  background-color: #eaeaea;
}
div.dtr-modal div.dtr-modal-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 101;
  background: rgba(0, 0, 0, 0.6);
}

@media screen and (max-width: 767px) {
  div.dtr-modal div.dtr-modal-display {
    width: 95%;
  }
}
/*!
 * FullCalendar v3.1.0 Stylesheet
 * Docs & License: http://fullcalendar.io/
 * (c) 2016 Adam Shaw
 */
.fc {
  direction: ltr;
  text-align: left;
}

.fc-rtl {
  text-align: right;
}

body .fc {
  /* extra precedence to overcome jqui */
  font-size: 1em;
}

/* Colors
--------------------------------------------------------------------------------------------------*/
.fc-unthemed th,
.fc-unthemed td,
.fc-unthemed thead,
.fc-unthemed tbody,
.fc-unthemed .fc-divider,
.fc-unthemed .fc-row,
.fc-unthemed .fc-popover {
  border-color: #ddd;
}

.fc-unthemed .fc-popover {
  background-color: #FFFFFF;
}

.fc-unthemed .fc-divider,
.fc-unthemed .fc-popover .fc-header {
  background: #E3E3E3;
}

.fc-unthemed .fc-popover .fc-header .fc-close {
  color: #666666;
}

.fc-unthemed .fc-today {
  background: #F5F5F5;
}

.fc-highlight {
  /* when user is selecting cells */
  background: #bce8f1;
  opacity: .3;
  filter: alpha(opacity=30);
  /* for IE */
}

.fc-bgevent {
  /* default look for background events */
  background: #8fdf82;
  opacity: .3;
  filter: alpha(opacity=30);
  /* for IE */
}

.fc-nonbusiness {
  /* default look for non-business-hours areas */
  /* will inherit .fc-bgevent's styles */
  background: #d7d7d7;
}

/* Icons (inline elements with styled text that mock arrow icons)
--------------------------------------------------------------------------------------------------*/
.fc-icon {
  display: inline-block;
  width: 1em;
  height: 1em;
  line-height: 1em;
  font-size: 1em;
  text-align: center;
  overflow: hidden;
  font-family: "Courier New", Courier, monospace;
  /* don't allow browser text-selection */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/*
Acceptable font-family overrides for individual icons:
	"Arial", sans-serif
	"Times New Roman", serif

NOTE: use percentage font sizes or else old IE chokes
*/
.fc-icon:after {
  position: relative;
  margin: 0 -1em;
  /* ensures character will be centered, regardless of width */
}

.fc-icon-left-single-arrow:after {
  content: "\02039";
  font-weight: bold;
  font-size: 200%;
  top: -7%;
  left: 3%;
}

.fc-icon-right-single-arrow:after {
  content: "\0203A";
  font-weight: bold;
  font-size: 200%;
  top: -7%;
  left: -3%;
}

.fc-icon-left-double-arrow:after {
  content: "\000AB";
  font-size: 160%;
  top: -7%;
}

.fc-icon-right-double-arrow:after {
  content: "\000BB";
  font-size: 160%;
  top: -7%;
}

.fc-icon-left-triangle:after {
  content: "\25C4";
  font-size: 125%;
  top: 3%;
  left: -2%;
}

.fc-icon-right-triangle:after {
  content: "\25BA";
  font-size: 125%;
  top: 3%;
  left: 2%;
}

.fc-icon-down-triangle:after {
  content: "\25BC";
  font-size: 125%;
  top: 2%;
}

.fc-icon-x:after {
  content: "\000D7";
  font-size: 200%;
  top: 6%;
}

/* Buttons (styled <button> tags, normalized to work cross-browser)
--------------------------------------------------------------------------------------------------*/
.fc button {
  border-radius: 20px;
  box-sizing: border-box;
  border-width: 2px;
  background-color: transparent;
  font-size: 12px;
  font-weight: 500;
  padding: 7px 18px;
  border-color: #66615B;
  color: #66615B;
  -webkit-transition: all 150ms linear;
  -moz-transition: all 150ms linear;
  -o-transition: all 150ms linear;
  -ms-transition: all 150ms linear;
  transition: all 150ms linear;
}
.fc button:hover, .fc button:focus, .fc button:active, .fc button.active, .fc button:active:focus, .fc button:active:hover, .open > .fc button.dropdown-toggle, .open > .fc button.dropdown-toggle:focus, .open > .fc button.dropdown-toggle:hover {
  background-color: #66615B;
  color: rgba(255, 255, 255, 0.85);
  border-color: #66615B;
}
.fc button:hover .caret, .fc button:focus .caret, .fc button:active .caret, .fc button.active .caret, .fc button:active:focus .caret, .fc button:active:hover .caret, .open > .fc button.dropdown-toggle .caret, .open > .fc button.dropdown-toggle:focus .caret, .open > .fc button.dropdown-toggle:hover .caret {
  border-top-color: rgba(255, 255, 255, 0.85);
}
.fc button.disabled, .fc button.disabled:hover, .fc button.disabled:focus, .fc button.disabled.focus, .fc button.disabled:active, .fc button.disabled.active, .fc button:disabled, .fc button:disabled:hover, .fc button:disabled:focus, .fc button:disabled.focus, .fc button:disabled:active, .fc button:disabled.active, .fc button[disabled], .fc button[disabled]:hover, .fc button[disabled]:focus, .fc button[disabled].focus, .fc button[disabled]:active, .fc button[disabled].active, fieldset[disabled] .fc button, fieldset[disabled] .fc button:hover, fieldset[disabled] .fc button:focus, fieldset[disabled] .fc button.focus, fieldset[disabled] .fc button:active, fieldset[disabled] .fc button.active {
  background-color: transparent;
  border-color: #66615B;
}
.fc button.btn-fill {
  color: #FFFFFF;
  background-color: #66615B;
  opacity: 1;
  filter: alpha(opacity=100);
}
.fc button.btn-fill:hover, .fc button.btn-fill:focus, .fc button.btn-fill:active, .fc button.btn-fill.active, .open > .fc button.btn-fill.dropdown-toggle {
  background-color: #484541;
  color: #FFFFFF;
  border-color: #484541;
}
.fc button.btn-fill .caret {
  border-top-color: #FFFFFF;
}
.fc button.btn-simple:hover, .fc button.btn-simple:focus, .fc button.btn-simple:active, .fc button.btn-simple.active, .open > .fc button.btn-simple.dropdown-toggle {
  background-color: transparent;
  color: #484541;
}
.fc button.btn-simple .caret {
  border-top-color: #FFFFFF;
}
.fc button .caret {
  border-top-color: #66615B;
}
.fc button:hover, .fc button:focus {
  outline: 0 !important;
}
.fc button:active, .fc button.active, .open > .fc button.dropdown-toggle {
  -webkit-box-shadow: none;
  box-shadow: none;
  outline: 0 !important;
}
.fc button.btn-icon {
  border-radius: 25px;
  padding: 6px 10px;
}
.fc button.btn-icon i {
  margin-right: 0px;
}

/* Firefox has an annoying inner border */
.fc button::-moz-focus-inner {
  margin: 0;
  padding: 0;
}

.fc-state-default {
  /* non-theme */
  border: 1px solid;
}

/* icons in buttons */
.fc button .fc-icon {
  /* non-theme */
  position: relative;
  top: -0.05em;
  /* seems to be a good adjustment across browsers */
  margin: 0 .2em;
  vertical-align: middle;
}

/*
  button states
  borrowed from twitter bootstrap (http://twitter.github.com/bootstrap/)
*/
.fc-state-hover,
.fc-state-active,
.fc-state-disabled {
  color: #333333;
  background-color: #e6e6e6;
}

.fc button[disabled],
.fc button[disabled]:focus,
.fc button[disabled]:hover {
  background-color: #E3E3E3;
  border-color: #E3E3E3;
  cursor: default;
  opacity: 0.65;
  filter: alpha(opacity=65);
  color: #66615b;
}

.fc-state-hover {
  background-color: #F5F5F5;
}

.fc .fc-state-active,
.fc .fc-state-active:focus,
.fc .fc-state-active:hover,
.fc .fc-state-active:active:focus,
.fc .fc-state-active:active:hover,
.fc .fc-state-active:active {
  background-color: #66615B;
  color: #FFFFFF;
}

/* Buttons Groups
--------------------------------------------------------------------------------------------------*/
.fc-button-group {
  display: inline-block;
}

/*
every button that is not first in a button group should scootch over one pixel and cover the
previous button's border...
*/
.fc .fc-button-group > * {
  /* extra precedence b/c buttons have margin set to zero */
  float: left;
  margin: 0 2px 0 0;
}

.fc .fc-button-group > :first-child {
  /* same */
  margin-left: 0;
}

/* Popover
--------------------------------------------------------------------------------------------------*/
.fc-popover {
  position: absolute;
  -webkit-box-shadow: 0 2px rgba(17, 16, 15, 0.1), 0 2px 10px rgba(17, 16, 15, 0.1);
  box-shadow: 0 2px rgba(17, 16, 15, 0.1), 0 2px 10px rgba(17, 16, 15, 0.1);
}

.fc-popover .fc-header {
  /* TODO: be more consistent with fc-head/fc-body */
  padding: 2px 4px;
}

.fc-popover .fc-header .fc-title {
  margin: 0 2px;
}

.fc-popover .fc-header .fc-close {
  cursor: pointer;
}

.fc-ltr .fc-popover .fc-header .fc-title,
.fc-rtl .fc-popover .fc-header .fc-close {
  float: left;
}

.fc-rtl .fc-popover .fc-header .fc-title,
.fc-ltr .fc-popover .fc-header .fc-close {
  float: right;
}

/* unthemed */
.fc-unthemed .fc-popover {
  border-width: 1px;
  border-style: solid;
}

.fc-unthemed .fc-popover .fc-header .fc-close {
  font-size: .9em;
  margin-top: 2px;
}

/* jqui themed */
.fc-popover > .ui-widget-header + .ui-widget-content {
  border-top: 0;
  /* where they meet, let the header have the border */
}

/* Misc Reusable Components
--------------------------------------------------------------------------------------------------*/
.fc-divider {
  border-style: solid;
  border-width: 1px;
}

hr.fc-divider {
  height: 0;
  margin: 0;
  padding: 0 0 2px;
  /* height is unreliable across browsers, so use padding */
  border-width: 1px 0;
}

.fc-clear {
  clear: both;
}

.fc-bg,
.fc-bgevent-skeleton,
.fc-highlight-skeleton,
.fc-helper-skeleton {
  /* these element should always cling to top-left/right corners */
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.fc-bg {
  bottom: 0;
  /* strech bg to bottom edge */
}

.fc-bg table {
  height: 100%;
  /* strech bg to bottom edge */
}

/* Tables
--------------------------------------------------------------------------------------------------*/
.fc table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  border-spacing: 0;
  font-size: 1em;
  /* normalize cross-browser */
}

.fc th {
  text-align: right;
  border-bottom-width: 1px;
  font-size: 0.9em;
  text-transform: uppercase;
  color: #9A9A9A;
  font-weight: 400;
  padding-bottom: 3px;
}

.fc td {
  border-style: solid;
  border-width: 1px;
  padding: 0;
  vertical-align: top;
}

.fc td.fc-today {
  border-style: double;
  /* overcome neighboring borders */
}

.fc .fc-widget-header {
  border: 0;
}

.fc .fc-head .fc-widget-header {
  margin-top: 15px;
}

/* Fake Table Rows
--------------------------------------------------------------------------------------------------*/
.fc .fc-row {
  /* extra precedence to overcome themes w/ .ui-widget-content forcing a 1px border */
  /* no visible border by default. but make available if need be (scrollbar width compensation) */
  border-style: solid;
  border-width: 0;
}

.fc-row table {
  /* don't put left/right border on anything within a fake row.
     the outer tbody will worry about this */
  border-left: 0 hidden transparent;
  border-right: 0 hidden transparent;
  /* no bottom borders on rows */
  border-bottom: 0 hidden transparent;
}

.fc-row:first-child table {
  border-top: 0 hidden transparent;
  /* no top border on first row */
}

/* Day Row (used within the header and the DayGrid)
--------------------------------------------------------------------------------------------------*/
.fc-row {
  position: relative;
}

.fc-row .fc-bg {
  z-index: 1;
}

/* highlighting cells & background event skeleton */
.fc-row .fc-bgevent-skeleton,
.fc-row .fc-highlight-skeleton {
  bottom: 0;
  /* stretch skeleton to bottom of row */
}

.fc-row .fc-bgevent-skeleton table,
.fc-row .fc-highlight-skeleton table {
  height: 100%;
  /* stretch skeleton to bottom of row */
}

.fc-row .fc-highlight-skeleton td,
.fc-row .fc-bgevent-skeleton td {
  border-color: transparent;
}

.fc-row .fc-bgevent-skeleton {
  z-index: 2;
}

.fc-row .fc-highlight-skeleton {
  z-index: 3;
}

/*
row content (which contains day/week numbers and events) as well as "helper" (which contains
temporary rendered events).
*/
.fc-row .fc-content-skeleton {
  position: relative;
  z-index: 4;
  padding-bottom: 2px;
  /* matches the space above the events */
}

.fc-row .fc-helper-skeleton {
  z-index: 5;
}

.fc-row .fc-content-skeleton td,
.fc-row .fc-helper-skeleton td {
  /* see-through to the background below */
  background: none;
  /* in case <td>s are globally styled */
  border-color: transparent;
  /* don't put a border between events and/or the day number */
  border-bottom: 0;
}

.fc-row .fc-content-skeleton tbody td,
.fc-row .fc-helper-skeleton tbody td {
  /* don't put a border between event cells */
  border-top: 0;
}

/* Scrolling Container
--------------------------------------------------------------------------------------------------*/
.fc-scroller {
  /* this class goes on elements for guaranteed vertical scrollbars */
  overflow-y: scroll;
  overflow-x: hidden;
}

.fc-scroller > * {
  /* we expect an immediate inner element */
  position: relative;
  /* re-scope all positions */
  width: 100%;
  /* hack to force re-sizing this inner element when scrollbars appear/disappear */
  overflow: hidden;
  /* don't let negative margins or absolute positioning create further scroll */
}

/* Global Event Styles
--------------------------------------------------------------------------------------------------*/
.fc-event {
  position: relative;
  /* for resize handle and other inner positioning */
  display: block;
  /* make the <a> tag block */
  font-size: .85em;
  line-height: 1.3;
  border-radius: 2px;
  background-color: rgba(122, 158, 159, 0.2);
  /* default BACKGROUND color */
  font-weight: normal;
  /* undo jqui's ui-widget-header bold */
}
.fc-event.event-azure {
  background-color: rgba(104, 179, 200, 0.2);
}
.fc-event.event-green {
  background-color: rgba(122, 194, 154, 0.2);
}
.fc-event.event-orange {
  background-color: rgba(243, 187, 69, 0.2);
}
.fc-event.event-red {
  background-color: rgba(235, 94, 40, 0.2);
}

/* overpower some of bootstrap's and jqui's styles on <a> tags */
.fc-event,
.fc-event:hover,
.ui-widget .fc-event {
  color: #333333;
  /* default TEXT color */
  text-decoration: none;
  /* if <a> has an href */
}

.fc-event[href],
.fc-event.fc-draggable {
  cursor: pointer;
  /* give events with links and draggable events a hand mouse pointer */
}

.fc-not-allowed,
.fc-not-allowed .fc-event {
  /* to override an event's custom cursor */
  cursor: not-allowed;
}

.fc-event .fc-bg {
  /* the generic .fc-bg already does position */
  z-index: 1;
  background: #FFFFFF;
  opacity: .25;
  filter: alpha(opacity=25);
  /* for IE */
}

.fc-event .fc-content {
  position: relative;
  z-index: 2;
}

.fc-event .fc-resizer {
  position: absolute;
  z-index: 3;
}

/* Horizontal Events
--------------------------------------------------------------------------------------------------*/
/* events that are continuing to/from another week. kill rounded corners and butt up against edge */
.fc-ltr .fc-h-event.fc-not-start,
.fc-rtl .fc-h-event.fc-not-end {
  margin-left: 0;
  border-left-width: 0;
  padding-left: 1px;
  /* replace the border with padding */
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.fc-ltr .fc-h-event.fc-not-end,
.fc-rtl .fc-h-event.fc-not-start {
  margin-right: 0;
  border-right-width: 0;
  padding-right: 1px;
  /* replace the border with padding */
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

/* resizer */
.fc-h-event .fc-resizer {
  /* positioned it to overcome the event's borders */
  top: -1px;
  bottom: -1px;
  left: -1px;
  right: -1px;
  width: 5px;
}

/* left resizer  */
.fc-ltr .fc-h-event .fc-start-resizer,
.fc-ltr .fc-h-event .fc-start-resizer:before,
.fc-ltr .fc-h-event .fc-start-resizer:after,
.fc-rtl .fc-h-event .fc-end-resizer,
.fc-rtl .fc-h-event .fc-end-resizer:before,
.fc-rtl .fc-h-event .fc-end-resizer:after {
  right: auto;
  /* ignore the right and only use the left */
  cursor: w-resize;
}

/* right resizer */
.fc-ltr .fc-h-event .fc-end-resizer,
.fc-ltr .fc-h-event .fc-end-resizer:before,
.fc-ltr .fc-h-event .fc-end-resizer:after,
.fc-rtl .fc-h-event .fc-start-resizer,
.fc-rtl .fc-h-event .fc-start-resizer:before,
.fc-rtl .fc-h-event .fc-start-resizer:after {
  left: auto;
  /* ignore the left and only use the right */
  cursor: e-resize;
}

/* DayGrid events
----------------------------------------------------------------------------------------------------
We use the full "fc-day-grid-event" class instead of using descendants because the event won't
be a descendant of the grid when it is being dragged.
*/
.fc-day-grid-event {
  margin: 2px 5px 0;
  /* spacing between events and edges */
  padding: 1px 2px;
}

.fc-day-grid-event .fc-content {
  /* force events to be one-line tall */
  white-space: nowrap;
  overflow: hidden;
}

.fc-day-grid-event .fc-time {
  font-weight: bold;
}

.fc-day-grid-event .fc-resizer {
  /* enlarge the default hit area */
  left: -3px;
  right: -3px;
  width: 7px;
}

/* Event Limiting
--------------------------------------------------------------------------------------------------*/
/* "more" link that represents hidden events */
a.fc-more {
  margin: 1px 3px;
  font-size: .85em;
  cursor: pointer;
  text-decoration: none;
}

a.fc-more:hover {
  text-decoration: underline;
}

.fc-limited {
  /* rows and cells that are hidden because of a "more" link */
  display: none;
}

/* popover that appears when "more" link is clicked */
.fc-day-grid .fc-row {
  z-index: 1;
  /* make the "more" popover one higher than this */
}

.fc-more-popover {
  z-index: 2;
  width: 220px;
}

.fc-more-popover .fc-event-container {
  padding: 10px;
}

/* Toolbar
--------------------------------------------------------------------------------------------------*/
.fc-toolbar {
  text-align: center;
  margin-bottom: 1em;
}

.fc-toolbar .fc-left {
  float: left;
  min-width: 260px;
}

.fc-toolbar .fc-right {
  float: right;
}

.fc-toolbar .fc-center {
  display: inline-block;
}

/* the things within each left/right/center section */
.fc .fc-toolbar > * > * {
  /* extra precedence to override button border margins */
  float: left;
  margin-left: .75em;
}

/* the first thing within each left/center/right section */
.fc .fc-toolbar > * > :first-child {
  /* extra precedence to override button border margins */
  margin-left: 0;
}

/* title text */
.fc-toolbar h2 {
  margin: 0;
}

/* button layering (for border precedence) */
.fc-toolbar button {
  position: relative;
}

.fc-toolbar .fc-state-hover,
.fc-toolbar .ui-state-hover {
  z-index: 2;
}

.fc-toolbar .fc-state-down {
  z-index: 3;
}

.fc-toolbar .fc-state-active,
.fc-toolbar .ui-state-active {
  z-index: 4;
}

.fc-toolbar button:focus {
  z-index: 5;
}

/* View Structure
--------------------------------------------------------------------------------------------------*/
/* undo twitter bootstrap's box-sizing rules. normalizes positioning techniques */
/* don't do this for the toolbar because we'll want bootstrap to style those buttons as some pt */
.fc-view-container *,
.fc-view-container *:before,
.fc-view-container *:after {
  -webkit-box-sizing: content-box;
  -moz-box-sizing: content-box;
  box-sizing: content-box;
}

.fc-view,
.fc-view > table {
  /* so dragged elements can be above the view's main element */
  position: relative;
  z-index: 1;
}

/* BasicView
--------------------------------------------------------------------------------------------------*/
/* day row structure */
.fc-basicWeek-view .fc-content-skeleton,
.fc-basicDay-view .fc-content-skeleton {
  /* we are sure there are no day numbers in these views, so... */
  padding-top: 1px;
  /* add a pixel to make sure there are 2px padding above events */
  padding-bottom: 1em;
  /* ensure a space at bottom of cell for user selecting/clicking */
}

.fc-basic-view .fc-body .fc-row {
  min-height: 4em;
  /* ensure that all rows are at least this tall */
}

/* a "rigid" row will take up a constant amount of height because content-skeleton is absolute */
.fc-row.fc-rigid {
  overflow: hidden;
}

.fc-row.fc-rigid .fc-content-skeleton {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

/* week and day number styling */
.fc-basic-view .fc-week-number,
.fc-basic-view .fc-day-number {
  padding: 0 2px;
}

.fc-basic-view td.fc-week-number span,
.fc-basic-view td.fc-day-number {
  padding: 8px;
}

.fc-basic-view .fc-week-number {
  text-align: center;
}

.fc-basic-view .fc-week-number span {
  /* work around the way we do column resizing and ensure a minimum width */
  display: inline-block;
  min-width: 1.25em;
}

.fc-ltr .fc-basic-view .fc-day-number {
  text-align: right;
}

.fc-rtl .fc-basic-view .fc-day-number {
  text-align: left;
}

.fc-day-number.fc-other-month {
  opacity: 0.3;
  filter: alpha(opacity=30);
  /* for IE */
  /* opacity with small font can sometimes look too faded
     might want to set the 'color' property instead
     making day-numbers bold also fixes the problem */
}

/* AgendaView all-day area
--------------------------------------------------------------------------------------------------*/
.fc-agenda-view .fc-day-grid {
  position: relative;
  z-index: 2;
  /* so the "more.." popover will be over the time grid */
}

.fc-agenda-view .fc-day-grid .fc-row {
  min-height: 3em;
  /* all-day section will never get shorter than this */
}

.fc-agenda-view .fc-day-grid .fc-row .fc-content-skeleton {
  padding-top: 1px;
  /* add a pixel to make sure there are 2px padding above events */
  padding-bottom: 1em;
  /* give space underneath events for clicking/selecting days */
}

/* TimeGrid axis running down the side (for both the all-day area and the slot area)
--------------------------------------------------------------------------------------------------*/
.fc .fc-axis {
  /* .fc to overcome default cell styles */
  vertical-align: middle;
  padding: 0 4px;
  white-space: nowrap;
}

.fc-ltr .fc-axis {
  text-align: right;
}

.fc-rtl .fc-axis {
  text-align: left;
}

.ui-widget td.fc-axis {
  font-weight: normal;
  /* overcome jqui theme making it bold */
}

/* TimeGrid Structure
--------------------------------------------------------------------------------------------------*/
.fc-time-grid-container,
.fc-time-grid {
  /* so slats/bg/content/etc positions get scoped within here */
  position: relative;
  z-index: 1;
}

.fc-time-grid {
  min-height: 100%;
  /* so if height setting is 'auto', .fc-bg stretches to fill height */
}

.fc-time-grid table {
  /* don't put outer borders on slats/bg/content/etc */
  border: 0 hidden transparent;
}

.fc-time-grid > .fc-bg {
  z-index: 1;
}

.fc-time-grid .fc-slats,
.fc-time-grid > hr {
  /* the <hr> AgendaView injects when grid is shorter than scroller */
  position: relative;
  z-index: 2;
}

.fc-time-grid .fc-bgevent-skeleton,
.fc-time-grid .fc-content-skeleton {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
}

.fc-time-grid .fc-bgevent-skeleton {
  z-index: 3;
}

.fc-time-grid .fc-highlight-skeleton {
  z-index: 4;
}

.fc-time-grid .fc-content-skeleton {
  z-index: 5;
}

.fc-time-grid .fc-helper-skeleton {
  z-index: 6;
}

/* TimeGrid Slats (lines that run horizontally)
--------------------------------------------------------------------------------------------------*/
.fc-time-grid .fc-slats td {
  height: 1.5em;
  border-bottom: 0;
  /* each cell is responsible for its top border */
}

.fc-time-grid .fc-slats .fc-minor td {
  border-top-style: dotted;
}

.fc-time-grid .fc-slats .ui-widget-content {
  /* for jqui theme */
  background: none;
  /* see through to fc-bg */
}

/* TimeGrid Highlighting Slots
--------------------------------------------------------------------------------------------------*/
.fc-time-grid .fc-highlight-container {
  /* a div within a cell within the fc-highlight-skeleton */
  position: relative;
  /* scopes the left/right of the fc-highlight to be in the column */
}

.fc-time-grid .fc-highlight {
  position: absolute;
  left: 0;
  right: 0;
  /* top and bottom will be in by JS */
}

/* TimeGrid Event Containment
--------------------------------------------------------------------------------------------------*/
.fc-time-grid .fc-event-container,
.fc-time-grid .fc-bgevent-container {
  /* a div within a cell within the fc-bgevent-skeleton */
  position: relative;
}

.fc-ltr .fc-time-grid .fc-event-container {
  /* space on the sides of events for LTR (default) */
  margin: 0 2.5% 0 2px;
}

.fc-rtl .fc-time-grid .fc-event-container {
  /* space on the sides of events for RTL */
  margin: 0 2px 0 2.5%;
}

.fc-time-grid .fc-event,
.fc-time-grid .fc-bgevent {
  position: absolute;
  z-index: 1;
  /* scope inner z-index's */
}

.fc-time-grid .fc-bgevent {
  /* background events always span full width */
  left: 0;
  right: 0;
}

/* Generic Vertical Event
--------------------------------------------------------------------------------------------------*/
.fc-v-event.fc-not-start {
  /* events that are continuing from another day */
  /* replace space made by the top border with padding */
  border-top-width: 0;
  padding-top: 1px;
  /* remove top rounded corners */
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.fc-v-event.fc-not-end {
  /* replace space made by the top border with padding */
  border-bottom-width: 0;
  padding-bottom: 1px;
  /* remove bottom rounded corners */
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

/* TimeGrid Event Styling
----------------------------------------------------------------------------------------------------
We use the full "fc-time-grid-event" class instead of using descendants because the event won't
be a descendant of the grid when it is being dragged.
*/
.fc-time-grid-event {
  overflow: hidden;
  /* don't let the bg flow over rounded corners */
}

.fc-time-grid-event .fc-time,
.fc-time-grid-event .fc-title {
  padding: 0 1px;
}

.fc-time-grid-event .fc-time {
  font-size: .85em;
  white-space: nowrap;
}

/* short mode, where time and title are on the same line */
.fc-time-grid-event.fc-short .fc-content {
  /* don't wrap to second line (now that contents will be inline) */
  white-space: nowrap;
}

.fc-time-grid-event.fc-short .fc-time,
.fc-time-grid-event.fc-short .fc-title {
  /* put the time and title on the same line */
  display: inline-block;
  vertical-align: top;
}

.fc-time-grid-event.fc-short .fc-time span {
  display: none;
  /* don't display the full time text... */
}

.fc-time-grid-event.fc-short .fc-time:before {
  content: attr(data-start);
  /* ...instead, display only the start time */
}

.fc-time-grid-event.fc-short .fc-time:after {
  content: "\000A0-\000A0";
  /* seperate with a dash, wrapped in nbsp's */
}

.fc-time-grid-event.fc-short .fc-title {
  font-size: .85em;
  /* make the title text the same size as the time */
  padding: 0;
  /* undo padding from above */
}

/* resizer */
.fc-time-grid-event .fc-resizer {
  left: 0;
  right: 0;
  bottom: 0;
  height: 8px;
  overflow: hidden;
  line-height: 8px;
  font-size: 11px;
  font-family: monospace;
  text-align: center;
  cursor: s-resize;
}

.fc-time-grid-event .fc-resizer:after {
  content: "=";
}

.card-calendar .content {
  padding: 0 !important;
}
.card-calendar .fc-toolbar {
  padding-top: 20px;
  padding-left: 20px;
  padding-right: 20px;
}
.card-calendar .fc td:first-child {
  border-left: 0;
}
.card-calendar .fc td:last-child {
  border-right: 0;
}
.card-calendar .fc-basic-view td:last-child.fc-week-number span,
.card-calendar .fc-basic-view td:last-child.fc-day-number {
  padding-right: 20px;
}
.card-calendar .fc .fc-day-header:last-child {
  padding-right: 15px;
}
.card-calendar .fc .fc-row:last-child td {
  border-bottom: 0;
}
.card-calendar .fc .fc-body .fc-widget-content {
  border-bottom: 0;
}

.ct-label {
  fill: rgba(0, 0, 0, 0.4);
  color: rgba(0, 0, 0, 0.4);
  font-size: 0.9em;
  line-height: 1;
}

.ct-chart-line .ct-label,
.ct-chart-bar .ct-label {
  display: block;
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}

.ct-label.ct-horizontal.ct-start {
  -webkit-box-align: flex-end;
  -webkit-align-items: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  -webkit-box-pack: flex-start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: flex-start;
  justify-content: flex-start;
  text-align: left;
  text-anchor: start;
}

.ct-label.ct-horizontal.ct-end {
  -webkit-box-align: flex-start;
  -webkit-align-items: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  -webkit-box-pack: flex-start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: flex-start;
  justify-content: flex-start;
  text-align: left;
  text-anchor: start;
}

.ct-label.ct-vertical.ct-start {
  -webkit-box-align: flex-end;
  -webkit-align-items: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  -webkit-box-pack: flex-end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: flex-end;
  justify-content: flex-end;
  text-align: right;
  text-anchor: end;
}

.ct-label.ct-vertical.ct-end {
  -webkit-box-align: flex-end;
  -webkit-align-items: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  -webkit-box-pack: flex-start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: flex-start;
  justify-content: flex-start;
  text-align: left;
  text-anchor: start;
}

.ct-chart-bar .ct-label.ct-horizontal.ct-start {
  -webkit-box-align: flex-end;
  -webkit-align-items: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  text-align: center;
  text-anchor: start;
}

.ct-chart-bar .ct-label.ct-horizontal.ct-end {
  -webkit-box-align: flex-start;
  -webkit-align-items: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -ms-flex-pack: center;
  justify-content: center;
  text-align: center;
  text-anchor: start;
}

.ct-chart-bar.ct-horizontal-bars .ct-label.ct-horizontal.ct-start {
  -webkit-box-align: flex-end;
  -webkit-align-items: flex-end;
  -ms-flex-align: flex-end;
  align-items: flex-end;
  -webkit-box-pack: flex-start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: flex-start;
  justify-content: flex-start;
  text-align: left;
  text-anchor: start;
}

.ct-chart-bar.ct-horizontal-bars .ct-label.ct-horizontal.ct-end {
  -webkit-box-align: flex-start;
  -webkit-align-items: flex-start;
  -ms-flex-align: flex-start;
  align-items: flex-start;
  -webkit-box-pack: flex-start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: flex-start;
  justify-content: flex-start;
  text-align: left;
  text-anchor: start;
}

.ct-chart-bar.ct-horizontal-bars .ct-label.ct-vertical.ct-start {
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: flex-end;
  -webkit-justify-content: flex-end;
  -ms-flex-pack: flex-end;
  justify-content: flex-end;
  text-align: right;
  text-anchor: end;
}

.ct-chart-bar.ct-horizontal-bars .ct-label.ct-vertical.ct-end {
  -webkit-box-align: center;
  -webkit-align-items: center;
  -ms-flex-align: center;
  align-items: center;
  -webkit-box-pack: flex-start;
  -webkit-justify-content: flex-start;
  -ms-flex-pack: flex-start;
  justify-content: flex-start;
  text-align: left;
  text-anchor: end;
}

.ct-grid {
  stroke: rgba(0, 0, 0, 0.2);
  stroke-width: 1px;
  stroke-dasharray: 2px;
}

.ct-point {
  stroke-width: 10px;
  stroke-linecap: round;
}

.ct-line {
  fill: none;
  stroke-width: 4px;
}

.ct-area {
  stroke: none;
  fill-opacity: 0.7;
}

.ct-bar {
  fill: none;
  stroke-width: 10px;
}

.ct-slice-donut {
  fill: none;
  stroke-width: 60px;
}

.ct-series-a .ct-point, .ct-series-a .ct-line, .ct-series-a .ct-bar, .ct-series-a .ct-slice-donut {
  stroke: #68B3C8;
}
.ct-series-a .ct-slice-pie, .ct-series-a .ct-area {
  fill: #68B3C8;
}

.ct-series-b .ct-point, .ct-series-b .ct-line, .ct-series-b .ct-bar, .ct-series-b .ct-slice-donut {
  stroke: #F3BB45;
}
.ct-series-b .ct-slice-pie, .ct-series-b .ct-area {
  fill: #F3BB45;
}

.ct-series-c .ct-point, .ct-series-c .ct-line, .ct-series-c .ct-bar, .ct-series-c .ct-slice-donut {
  stroke: #EB5E28;
}
.ct-series-c .ct-slice-pie, .ct-series-c .ct-area {
  fill: #EB5E28;
}

.ct-series-d .ct-point, .ct-series-d .ct-line, .ct-series-d .ct-bar, .ct-series-d .ct-slice-donut {
  stroke: #7AC29A;
}
.ct-series-d .ct-slice-pie, .ct-series-d .ct-area {
  fill: #7AC29A;
}

.ct-series-e .ct-point, .ct-series-e .ct-line, .ct-series-e .ct-bar, .ct-series-e .ct-slice-donut {
  stroke: #7A9E9F;
}
.ct-series-e .ct-slice-pie, .ct-series-e .ct-area {
  fill: #7A9E9F;
}

.ct-series-f .ct-point, .ct-series-f .ct-line, .ct-series-f .ct-bar, .ct-series-f .ct-slice-donut {
  stroke: rgba(104, 179, 200, 0.8);
}
.ct-series-f .ct-slice-pie, .ct-series-f .ct-area {
  fill: rgba(104, 179, 200, 0.8);
}

.ct-series-g .ct-point, .ct-series-g .ct-line, .ct-series-g .ct-bar, .ct-series-g .ct-slice-donut {
  stroke: rgba(122, 194, 154, 0.8);
}
.ct-series-g .ct-slice-pie, .ct-series-g .ct-area {
  fill: rgba(122, 194, 154, 0.8);
}

.ct-series-h .ct-point, .ct-series-h .ct-line, .ct-series-h .ct-bar, .ct-series-h .ct-slice-donut {
  stroke: rgba(243, 187, 69, 0.8);
}
.ct-series-h .ct-slice-pie, .ct-series-h .ct-area {
  fill: rgba(243, 187, 69, 0.8);
}

.ct-series-i .ct-point, .ct-series-i .ct-line, .ct-series-i .ct-bar, .ct-series-i .ct-slice-donut {
  stroke: rgba(235, 94, 40, 0.8);
}
.ct-series-i .ct-slice-pie, .ct-series-i .ct-area {
  fill: rgba(235, 94, 40, 0.8);
}

.ct-series-j .ct-point, .ct-series-j .ct-line, .ct-series-j .ct-bar, .ct-series-j .ct-slice-donut {
  stroke: rgba(122, 158, 159, 0.8);
}
.ct-series-j .ct-slice-pie, .ct-series-j .ct-area {
  fill: rgba(122, 158, 159, 0.8);
}

.ct-series-k .ct-point, .ct-series-k .ct-line, .ct-series-k .ct-bar, .ct-series-k .ct-slice-donut {
  stroke: rgba(104, 179, 200, 0.6);
}
.ct-series-k .ct-slice-pie, .ct-series-k .ct-area {
  fill: rgba(104, 179, 200, 0.6);
}

.ct-series-l .ct-point, .ct-series-l .ct-line, .ct-series-l .ct-bar, .ct-series-l .ct-slice-donut {
  stroke: rgba(122, 194, 154, 0.6);
}
.ct-series-l .ct-slice-pie, .ct-series-l .ct-area {
  fill: rgba(122, 194, 154, 0.6);
}

.ct-series-m .ct-point, .ct-series-m .ct-line, .ct-series-m .ct-bar, .ct-series-m .ct-slice-donut {
  stroke: rgba(243, 187, 69, 0.6);
}
.ct-series-m .ct-slice-pie, .ct-series-m .ct-area {
  fill: rgba(243, 187, 69, 0.6);
}

.ct-series-n .ct-point, .ct-series-n .ct-line, .ct-series-n .ct-bar, .ct-series-n .ct-slice-donut {
  stroke: rgba(235, 94, 40, 0.6);
}
.ct-series-n .ct-slice-pie, .ct-series-n .ct-area {
  fill: rgba(235, 94, 40, 0.6);
}

.ct-series-o .ct-point, .ct-series-o .ct-line, .ct-series-o .ct-bar, .ct-series-o .ct-slice-donut {
  stroke: rgba(122, 158, 159, 0.6);
}
.ct-series-o .ct-slice-pie, .ct-series-o .ct-area {
  fill: rgba(122, 158, 159, 0.6);
}

.ct-square {
  display: block;
  position: relative;
  width: 100%;
}
.ct-square:before {
  display: block;
  float: left;
  content: "";
  width: 0;
  height: 0;
  padding-bottom: 100%;
}
.ct-square:after {
  content: "";
  display: table;
  clear: both;
}
.ct-square > svg {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.ct-minor-second {
  display: block;
  position: relative;
  width: 100%;
}
.ct-minor-second:before {
  display: block;
  float: left;
  content: "";
  width: 0;
  height: 0;
  padding-bottom: 93.75%;
}
.ct-minor-second:after {
  content: "";
  display: table;
  clear: both;
}
.ct-minor-second > svg {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.ct-major-second {
  display: block;
  position: relative;
  width: 100%;
}
.ct-major-second:before {
  display: block;
  float: left;
  content: "";
  width: 0;
  height: 0;
  padding-bottom: 88.88889%;
}
.ct-major-second:after {
  content: "";
  display: table;
  clear: both;
}
.ct-major-second > svg {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.ct-minor-third {
  display: block;
  position: relative;
  width: 100%;
}
.ct-minor-third:before {
  display: block;
  float: left;
  content: "";
  width: 0;
  height: 0;
  padding-bottom: 83.33333%;
}
.ct-minor-third:after {
  content: "";
  display: table;
  clear: both;
}
.ct-minor-third > svg {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.ct-major-third {
  display: block;
  position: relative;
  width: 100%;
}
.ct-major-third:before {
  display: block;
  float: left;
  content: "";
  width: 0;
  height: 0;
  padding-bottom: 80%;
}
.ct-major-third:after {
  content: "";
  display: table;
  clear: both;
}
.ct-major-third > svg {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.ct-perfect-fourth {
  display: block;
  position: relative;
  width: 100%;
}
.ct-perfect-fourth:before {
  display: block;
  float: left;
  content: "";
  width: 0;
  height: 0;
  padding-bottom: 75%;
}
.ct-perfect-fourth:after {
  content: "";
  display: table;
  clear: both;
}
.ct-perfect-fourth > svg {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.ct-perfect-fifth {
  display: block;
  position: relative;
  width: 100%;
}
.ct-perfect-fifth:before {
  display: block;
  float: left;
  content: "";
  width: 0;
  height: 0;
  padding-bottom: 66.66667%;
}
.ct-perfect-fifth:after {
  content: "";
  display: table;
  clear: both;
}
.ct-perfect-fifth > svg {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.ct-minor-sixth {
  display: block;
  position: relative;
  width: 100%;
}
.ct-minor-sixth:before {
  display: block;
  float: left;
  content: "";
  width: 0;
  height: 0;
  padding-bottom: 62.5%;
}
.ct-minor-sixth:after {
  content: "";
  display: table;
  clear: both;
}
.ct-minor-sixth > svg {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.ct-golden-section {
  display: block;
  position: relative;
  width: 100%;
}
.ct-golden-section:before {
  display: block;
  float: left;
  content: "";
  width: 0;
  height: 0;
  padding-bottom: 61.8047%;
}
.ct-golden-section:after {
  content: "";
  display: table;
  clear: both;
}
.ct-golden-section > svg {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.ct-major-sixth {
  display: block;
  position: relative;
  width: 100%;
}
.ct-major-sixth:before {
  display: block;
  float: left;
  content: "";
  width: 0;
  height: 0;
  padding-bottom: 60%;
}
.ct-major-sixth:after {
  content: "";
  display: table;
  clear: both;
}
.ct-major-sixth > svg {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.ct-minor-seventh {
  display: block;
  position: relative;
  width: 100%;
}
.ct-minor-seventh:before {
  display: block;
  float: left;
  content: "";
  width: 0;
  height: 0;
  padding-bottom: 56.25%;
}
.ct-minor-seventh:after {
  content: "";
  display: table;
  clear: both;
}
.ct-minor-seventh > svg {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.ct-major-seventh {
  display: block;
  position: relative;
  width: 100%;
}
.ct-major-seventh:before {
  display: block;
  float: left;
  content: "";
  width: 0;
  height: 0;
  padding-bottom: 53.33333%;
}
.ct-major-seventh:after {
  content: "";
  display: table;
  clear: both;
}
.ct-major-seventh > svg {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.ct-octave {
  display: block;
  position: relative;
  width: 100%;
}
.ct-octave:before {
  display: block;
  float: left;
  content: "";
  width: 0;
  height: 0;
  padding-bottom: 50%;
}
.ct-octave:after {
  content: "";
  display: table;
  clear: both;
}
.ct-octave > svg {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.ct-major-tenth {
  display: block;
  position: relative;
  width: 100%;
}
.ct-major-tenth:before {
  display: block;
  float: left;
  content: "";
  width: 0;
  height: 0;
  padding-bottom: 40%;
}
.ct-major-tenth:after {
  content: "";
  display: table;
  clear: both;
}
.ct-major-tenth > svg {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.ct-major-eleventh {
  display: block;
  position: relative;
  width: 100%;
}
.ct-major-eleventh:before {
  display: block;
  float: left;
  content: "";
  width: 0;
  height: 0;
  padding-bottom: 37.5%;
}
.ct-major-eleventh:after {
  content: "";
  display: table;
  clear: both;
}
.ct-major-eleventh > svg {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.ct-major-twelfth {
  display: block;
  position: relative;
  width: 100%;
}
.ct-major-twelfth:before {
  display: block;
  float: left;
  content: "";
  width: 0;
  height: 0;
  padding-bottom: 33.33333%;
}
.ct-major-twelfth:after {
  content: "";
  display: table;
  clear: both;
}
.ct-major-twelfth > svg {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.ct-double-octave {
  display: block;
  position: relative;
  width: 100%;
}
.ct-double-octave:before {
  display: block;
  float: left;
  content: "";
  width: 0;
  height: 0;
  padding-bottom: 25%;
}
.ct-double-octave:after {
  content: "";
  display: table;
  clear: both;
}
.ct-double-octave > svg {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
}

.ct-blue {
  stroke: #7A9E9F !important;
}

.ct-azure {
  stroke: #68B3C8 !important;
}

.ct-green {
  stroke: #7AC29A !important;
}

.ct-orange {
  stroke: #F3BB45 !important;
}

.ct-red {
  stroke: #EB5E28 !important;
}

/* perfect-scrollbar v0.6.10 */
.ps-container {
  -ms-touch-action: none;
  touch-action: none;
  overflow: hidden !important;
  -ms-overflow-style: none;
}

@supports (-ms-overflow-style: none) {
  .ps-container {
    overflow: auto !important;
  }
}
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
  .ps-container {
    overflow: auto !important;
  }
}
.ps-container.ps-active-x > .ps-scrollbar-x-rail,
.ps-container.ps-active-y > .ps-scrollbar-y-rail {
  display: block;
  background-color: transparent;
}

.ps-container.ps-in-scrolling {
  pointer-events: none;
}

.ps-container.ps-in-scrolling.ps-x > .ps-scrollbar-x-rail {
  background-color: #E3E3E3;
  opacity: 0.9;
}

.ps-container.ps-in-scrolling.ps-x > .ps-scrollbar-x-rail > .ps-scrollbar-x {
  background-color: #9A9A9A;
}

.ps-container.ps-in-scrolling.ps-y > .ps-scrollbar-y-rail {
  background-color: #E3E3E3;
  opacity: 0.9;
}

.ps-container.ps-in-scrolling.ps-y > .ps-scrollbar-y-rail > .ps-scrollbar-y {
  background-color: #9A9A9A;
}

.ps-container > .ps-scrollbar-x-rail {
  display: none;
  position: absolute;
  /* please don't change 'position' */
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  opacity: 0;
  -webkit-transition: background-color .2s linear, opacity .2s linear;
  -moz-transition: background-color .2s linear, opacity .2s linear;
  -o-transition: background-color .2s linear, opacity .2s linear;
  transition: background-color .2s linear, opacity .2s linear;
  bottom: 3px;
  /* there must be 'bottom' for ps-scrollbar-x-rail */
  height: 8px;
}

.ps-container > .ps-scrollbar-x-rail > .ps-scrollbar-x {
  position: absolute;
  /* please don't change 'position' */
  background-color: #AAAAAA;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -webkit-transition: background-color .2s linear;
  -moz-transition: background-color .2s linear;
  -o-transition: background-color .2s linear;
  transition: background-color .2s linear;
  bottom: 0;
  /* there must be 'bottom' for ps-scrollbar-x */
  height: 8px;
}

.ps-container > .ps-scrollbar-y-rail {
  display: none;
  position: absolute;
  /* please don't change 'position' */
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  opacity: 0;
  -webkit-transition: background-color .2s linear, opacity .2s linear;
  -moz-transition: background-color .2s linear, opacity .2s linear;
  -o-transition: background-color .2s linear, opacity .2s linear;
  transition: background-color .2s linear, opacity .2s linear;
  right: 3px;
  z-index: 1042;
  /* there must be 'right' for ps-scrollbar-y-rail */
  width: 8px;
}

.ps-container > .ps-scrollbar-y-rail > .ps-scrollbar-y {
  position: absolute;
  /* please don't change 'position' */
  background-color: #AAAAAA;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  border-radius: 4px;
  -webkit-transition: background-color .2s linear;
  -moz-transition: background-color .2s linear;
  -o-transition: background-color .2s linear;
  transition: background-color .2s linear;
  right: 0;
  /* there must be 'right' for ps-scrollbar-y */
  width: 8px;
}

.ps-container:hover.ps-in-scrolling {
  pointer-events: none;
}

.ps-container:hover.ps-in-scrolling.ps-x > .ps-scrollbar-x-rail {
  background-color: #E3E3E3;
  opacity: 0.9;
}

.ps-container:hover.ps-in-scrolling.ps-x > .ps-scrollbar-x-rail > .ps-scrollbar-x {
  background-color: #9A9A9A;
}

.ps-container:hover.ps-in-scrolling.ps-y > .ps-scrollbar-y-rail {
  background-color: #E3E3E3;
  opacity: 0.9;
}

.ps-container:hover.ps-in-scrolling.ps-y > .ps-scrollbar-y-rail > .ps-scrollbar-y {
  background-color: #9A9A9A;
}

.ps-container:hover > .ps-scrollbar-x-rail,
.ps-container:hover > .ps-scrollbar-y-rail {
  opacity: 0.6;
}

.ps-container:hover > .ps-scrollbar-x-rail:hover {
  background-color: #E3E3E3;
  opacity: 0.9;
}

.ps-container:hover > .ps-scrollbar-x-rail:hover > .ps-scrollbar-x {
  background-color: #9A9A9A;
}

.ps-container:hover > .ps-scrollbar-y-rail:hover {
  background-color: #E3E3E3;
  opacity: 0.9;
}

.ps-container:hover > .ps-scrollbar-y-rail:hover > .ps-scrollbar-y {
  background-color: #9A9A9A;
}

svg {
  touch-action: none;
}

.jvectormap-container {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  touch-action: none;
}

.jvectormap-tip {
  position: absolute;
  display: none;
  border: solid 1px #CDCDCD;
  border-radius: 3px;
  background: #292929;
  color: white;
  font-family: sans-serif, Verdana;
  font-size: smaller;
  padding: 3px;
}

.jvectormap-zoomin, .jvectormap-zoomout, .jvectormap-goback {
  position: absolute;
  left: 10px;
  border-radius: 3px;
  background: #292929;
  padding: 3px;
  color: white;
  cursor: pointer;
  line-height: 10px;
  text-align: center;
  box-sizing: content-box;
}

.jvectormap-zoomin, .jvectormap-zoomout {
  width: 10px;
  height: 10px;
}

.jvectormap-zoomin {
  top: 10px;
}

.jvectormap-zoomout {
  top: 30px;
}

.jvectormap-goback {
  bottom: 10px;
  z-index: 1000;
  padding: 6px;
}

.jvectormap-spinner {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: center no-repeat url(data:image/gif;base64,R0lGODlhIAAgAPMAAP///wAAAMbGxoSEhLa2tpqamjY2NlZWVtjY2OTk5Ly8vB4eHgQEBAAAAAAAAAAAACH/C05FVFNDQVBFMi4wAwEAAAAh/hpDcmVhdGVkIHdpdGggYWpheGxvYWQuaW5mbwAh+QQJCgAAACwAAAAAIAAgAAAE5xDISWlhperN52JLhSSdRgwVo1ICQZRUsiwHpTJT4iowNS8vyW2icCF6k8HMMBkCEDskxTBDAZwuAkkqIfxIQyhBQBFvAQSDITM5VDW6XNE4KagNh6Bgwe60smQUB3d4Rz1ZBApnFASDd0hihh12BkE9kjAJVlycXIg7CQIFA6SlnJ87paqbSKiKoqusnbMdmDC2tXQlkUhziYtyWTxIfy6BE8WJt5YJvpJivxNaGmLHT0VnOgSYf0dZXS7APdpB309RnHOG5gDqXGLDaC457D1zZ/V/nmOM82XiHRLYKhKP1oZmADdEAAAh+QQJCgAAACwAAAAAIAAgAAAE6hDISWlZpOrNp1lGNRSdRpDUolIGw5RUYhhHukqFu8DsrEyqnWThGvAmhVlteBvojpTDDBUEIFwMFBRAmBkSgOrBFZogCASwBDEY/CZSg7GSE0gSCjQBMVG023xWBhklAnoEdhQEfyNqMIcKjhRsjEdnezB+A4k8gTwJhFuiW4dokXiloUepBAp5qaKpp6+Ho7aWW54wl7obvEe0kRuoplCGepwSx2jJvqHEmGt6whJpGpfJCHmOoNHKaHx61WiSR92E4lbFoq+B6QDtuetcaBPnW6+O7wDHpIiK9SaVK5GgV543tzjgGcghAgAh+QQJCgAAACwAAAAAIAAgAAAE7hDISSkxpOrN5zFHNWRdhSiVoVLHspRUMoyUakyEe8PTPCATW9A14E0UvuAKMNAZKYUZCiBMuBakSQKG8G2FzUWox2AUtAQFcBKlVQoLgQReZhQlCIJesQXI5B0CBnUMOxMCenoCfTCEWBsJColTMANldx15BGs8B5wlCZ9Po6OJkwmRpnqkqnuSrayqfKmqpLajoiW5HJq7FL1Gr2mMMcKUMIiJgIemy7xZtJsTmsM4xHiKv5KMCXqfyUCJEonXPN2rAOIAmsfB3uPoAK++G+w48edZPK+M6hLJpQg484enXIdQFSS1u6UhksENEQAAIfkECQoAAAAsAAAAACAAIAAABOcQyEmpGKLqzWcZRVUQnZYg1aBSh2GUVEIQ2aQOE+G+cD4ntpWkZQj1JIiZIogDFFyHI0UxQwFugMSOFIPJftfVAEoZLBbcLEFhlQiqGp1Vd140AUklUN3eCA51C1EWMzMCezCBBmkxVIVHBWd3HHl9JQOIJSdSnJ0TDKChCwUJjoWMPaGqDKannasMo6WnM562R5YluZRwur0wpgqZE7NKUm+FNRPIhjBJxKZteWuIBMN4zRMIVIhffcgojwCF117i4nlLnY5ztRLsnOk+aV+oJY7V7m76PdkS4trKcdg0Zc0tTcKkRAAAIfkECQoAAAAsAAAAACAAIAAABO4QyEkpKqjqzScpRaVkXZWQEximw1BSCUEIlDohrft6cpKCk5xid5MNJTaAIkekKGQkWyKHkvhKsR7ARmitkAYDYRIbUQRQjWBwJRzChi9CRlBcY1UN4g0/VNB0AlcvcAYHRyZPdEQFYV8ccwR5HWxEJ02YmRMLnJ1xCYp0Y5idpQuhopmmC2KgojKasUQDk5BNAwwMOh2RtRq5uQuPZKGIJQIGwAwGf6I0JXMpC8C7kXWDBINFMxS4DKMAWVWAGYsAdNqW5uaRxkSKJOZKaU3tPOBZ4DuK2LATgJhkPJMgTwKCdFjyPHEnKxFCDhEAACH5BAkKAAAALAAAAAAgACAAAATzEMhJaVKp6s2nIkolIJ2WkBShpkVRWqqQrhLSEu9MZJKK9y1ZrqYK9WiClmvoUaF8gIQSNeF1Er4MNFn4SRSDARWroAIETg1iVwuHjYB1kYc1mwruwXKC9gmsJXliGxc+XiUCby9ydh1sOSdMkpMTBpaXBzsfhoc5l58Gm5yToAaZhaOUqjkDgCWNHAULCwOLaTmzswadEqggQwgHuQsHIoZCHQMMQgQGubVEcxOPFAcMDAYUA85eWARmfSRQCdcMe0zeP1AAygwLlJtPNAAL19DARdPzBOWSm1brJBi45soRAWQAAkrQIykShQ9wVhHCwCQCACH5BAkKAAAALAAAAAAgACAAAATrEMhJaVKp6s2nIkqFZF2VIBWhUsJaTokqUCoBq+E71SRQeyqUToLA7VxF0JDyIQh/MVVPMt1ECZlfcjZJ9mIKoaTl1MRIl5o4CUKXOwmyrCInCKqcWtvadL2SYhyASyNDJ0uIiRMDjI0Fd30/iI2UA5GSS5UDj2l6NoqgOgN4gksEBgYFf0FDqKgHnyZ9OX8HrgYHdHpcHQULXAS2qKpENRg7eAMLC7kTBaixUYFkKAzWAAnLC7FLVxLWDBLKCwaKTULgEwbLA4hJtOkSBNqITT3xEgfLpBtzE/jiuL04RGEBgwWhShRgQExHBAAh+QQJCgAAACwAAAAAIAAgAAAE7xDISWlSqerNpyJKhWRdlSAVoVLCWk6JKlAqAavhO9UkUHsqlE6CwO1cRdCQ8iEIfzFVTzLdRAmZX3I2SfZiCqGk5dTESJeaOAlClzsJsqwiJwiqnFrb2nS9kmIcgEsjQydLiIlHehhpejaIjzh9eomSjZR+ipslWIRLAgMDOR2DOqKogTB9pCUJBagDBXR6XB0EBkIIsaRsGGMMAxoDBgYHTKJiUYEGDAzHC9EACcUGkIgFzgwZ0QsSBcXHiQvOwgDdEwfFs0sDzt4S6BK4xYjkDOzn0unFeBzOBijIm1Dgmg5YFQwsCMjp1oJ8LyIAACH5BAkKAAAALAAAAAAgACAAAATwEMhJaVKp6s2nIkqFZF2VIBWhUsJaTokqUCoBq+E71SRQeyqUToLA7VxF0JDyIQh/MVVPMt1ECZlfcjZJ9mIKoaTl1MRIl5o4CUKXOwmyrCInCKqcWtvadL2SYhyASyNDJ0uIiUd6GGl6NoiPOH16iZKNlH6KmyWFOggHhEEvAwwMA0N9GBsEC6amhnVcEwavDAazGwIDaH1ipaYLBUTCGgQDA8NdHz0FpqgTBwsLqAbWAAnIA4FWKdMLGdYGEgraigbT0OITBcg5QwPT4xLrROZL6AuQAPUS7bxLpoWidY0JtxLHKhwwMJBTHgPKdEQAACH5BAkKAAAALAAAAAAgACAAAATrEMhJaVKp6s2nIkqFZF2VIBWhUsJaTokqUCoBq+E71SRQeyqUToLA7VxF0JDyIQh/MVVPMt1ECZlfcjZJ9mIKoaTl1MRIl5o4CUKXOwmyrCInCKqcWtvadL2SYhyASyNDJ0uIiUd6GAULDJCRiXo1CpGXDJOUjY+Yip9DhToJA4RBLwMLCwVDfRgbBAaqqoZ1XBMHswsHtxtFaH1iqaoGNgAIxRpbFAgfPQSqpbgGBqUD1wBXeCYp1AYZ19JJOYgH1KwA4UBvQwXUBxPqVD9L3sbp2BNk2xvvFPJd+MFCN6HAAIKgNggY0KtEBAAh+QQJCgAAACwAAAAAIAAgAAAE6BDISWlSqerNpyJKhWRdlSAVoVLCWk6JKlAqAavhO9UkUHsqlE6CwO1cRdCQ8iEIfzFVTzLdRAmZX3I2SfYIDMaAFdTESJeaEDAIMxYFqrOUaNW4E4ObYcCXaiBVEgULe0NJaxxtYksjh2NLkZISgDgJhHthkpU4mW6blRiYmZOlh4JWkDqILwUGBnE6TYEbCgevr0N1gH4At7gHiRpFaLNrrq8HNgAJA70AWxQIH1+vsYMDAzZQPC9VCNkDWUhGkuE5PxJNwiUK4UfLzOlD4WvzAHaoG9nxPi5d+jYUqfAhhykOFwJWiAAAIfkECQoAAAAsAAAAACAAIAAABPAQyElpUqnqzaciSoVkXVUMFaFSwlpOCcMYlErAavhOMnNLNo8KsZsMZItJEIDIFSkLGQoQTNhIsFehRww2CQLKF0tYGKYSg+ygsZIuNqJksKgbfgIGepNo2cIUB3V1B3IvNiBYNQaDSTtfhhx0CwVPI0UJe0+bm4g5VgcGoqOcnjmjqDSdnhgEoamcsZuXO1aWQy8KAwOAuTYYGwi7w5h+Kr0SJ8MFihpNbx+4Erq7BYBuzsdiH1jCAzoSfl0rVirNbRXlBBlLX+BP0XJLAPGzTkAuAOqb0WT5AH7OcdCm5B8TgRwSRKIHQtaLCwg1RAAAOwAAAAAAAAAAAA==);
}

.jvectormap-legend-title {
  font-weight: bold;
  font-size: 14px;
  text-align: center;
}

.jvectormap-legend-cnt {
  position: absolute;
}

.jvectormap-legend-cnt-h {
  bottom: 0;
  right: 0;
}

.jvectormap-legend-cnt-v {
  top: 0;
  right: 0;
}

.jvectormap-legend {
  background: black;
  color: white;
  border-radius: 3px;
}

.jvectormap-legend-cnt-h .jvectormap-legend {
  float: left;
  margin: 0 10px 10px 0;
  padding: 3px 3px 1px 3px;
}

.jvectormap-legend-cnt-h .jvectormap-legend .jvectormap-legend-tick {
  float: left;
}

.jvectormap-legend-cnt-v .jvectormap-legend {
  margin: 10px 10px 0 0;
  padding: 3px;
}

.jvectormap-legend-cnt-h .jvectormap-legend-tick {
  width: 40px;
}

.jvectormap-legend-cnt-h .jvectormap-legend-tick-sample {
  height: 15px;
}

.jvectormap-legend-cnt-v .jvectormap-legend-tick-sample {
  height: 20px;
  width: 20px;
  display: inline-block;
  vertical-align: middle;
}

.jvectormap-legend-tick-text {
  font-size: 12px;
}

.jvectormap-legend-cnt-h .jvectormap-legend-tick-text {
  text-align: center;
}

.jvectormap-legend-cnt-v .jvectormap-legend-tick-text {
  display: inline-block;
  vertical-align: middle;
  line-height: 20px;
  padding-left: 3px;
}

/*!
 * Bootstrap-select v1.12.1 (http://silviomoreto.github.io/bootstrap-select)
 *
 * Copyright 2013-2016 bootstrap-select
 * Licensed under MIT (https://github.com/silviomoreto/bootstrap-select/blob/master/LICENSE)
 */
select.bs-select-hidden,
select.selectpicker {
  display: none !important;
}

.bootstrap-select {
  width: 220px \0;
  /*IE9 and below*/
}
.bootstrap-select > .dropdown-toggle {
  width: 100%;
  padding-right: 25px;
  z-index: 1;
}
.bootstrap-select > select {
  position: absolute !important;
  bottom: 0;
  left: 50%;
  display: block !important;
  width: 0.5px !important;
  height: 100% !important;
  padding: 0 !important;
  opacity: 0 !important;
  border: none;
}
.bootstrap-select > select.mobile-device {
  top: 0;
  left: 0;
  display: block !important;
  width: 100% !important;
  z-index: 2;
}
.has-error .bootstrap-select .dropdown-toggle, .error .bootstrap-select .dropdown-toggle {
  border-color: #b94a48;
}
.bootstrap-select.fit-width {
  width: auto !important;
}
.bootstrap-select:not([class*="col-"]):not([class*="form-control"]):not(.input-group-btn) {
  width: 100%;
  margin-bottom: 10px;
}

.bootstrap-select.form-control {
  margin-bottom: 0;
  padding: 0;
  border: none;
}
.bootstrap-select.form-control:not([class*="col-"]) {
  width: 100%;
}
.bootstrap-select.form-control.input-group-btn {
  z-index: auto;
}
.bootstrap-select.form-control.input-group-btn:not(:first-child):not(:last-child) > .btn {
  border-radius: 0;
}

.bootstrap-select.btn-group:not(.input-group-btn), .bootstrap-select.btn-group[class*="col-"] {
  float: none;
  display: inline-block;
  margin-left: 0;
}
.bootstrap-select.btn-group.dropdown-menu-right, .bootstrap-select.btn-group[class*="col-"].dropdown-menu-right, .row .bootstrap-select.btn-group[class*="col-"].dropdown-menu-right {
  float: right;
}
.form-inline .bootstrap-select.btn-group, .form-horizontal .bootstrap-select.btn-group, .form-group .bootstrap-select.btn-group {
  margin-bottom: 0;
}
.form-group-lg .bootstrap-select.btn-group.form-control, .form-group-sm .bootstrap-select.btn-group.form-control {
  padding: 0;
}
.form-inline .bootstrap-select.btn-group .form-control {
  width: 100%;
}
.bootstrap-select.btn-group.disabled,
.bootstrap-select.btn-group > .disabled {
  cursor: not-allowed;
}
.bootstrap-select.btn-group.disabled:focus,
.bootstrap-select.btn-group > .disabled:focus {
  outline: none !important;
}
.bootstrap-select.btn-group.bs-container {
  position: absolute;
  height: 0 !important;
  padding: 0 !important;
}
.bootstrap-select.btn-group.bs-container .dropdown-menu {
  z-index: 1060;
}
.bootstrap-select.btn-group .dropdown-toggle .filter-option {
  display: inline-block;
  overflow: hidden;
  width: 100%;
  text-align: left;
}
.bootstrap-select.btn-group .dropdown-toggle .caret {
  position: absolute;
  top: 50%;
  right: 12px;
  margin-top: -2px;
  vertical-align: middle;
}
.bootstrap-select.btn-group[class*="col-"] .dropdown-toggle {
  width: 100%;
}
.bootstrap-select.btn-group .dropdown-menu {
  min-width: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.bootstrap-select.btn-group .dropdown-menu.inner {
  position: static;
  float: none;
  border: 0;
  padding: 0;
  margin: 0;
  border-radius: 0;
  box-shadow: none;
  max-height: 200px;
  overflow-y: scroll;
}
.bootstrap-select.btn-group .dropdown-menu li {
  position: relative;
}
.bootstrap-select.btn-group .dropdown-menu li.active small {
  color: #fff;
}
.bootstrap-select.btn-group .dropdown-menu li.disabled a {
  cursor: not-allowed;
}
.bootstrap-select.btn-group .dropdown-menu li a {
  cursor: pointer;
  user-select: none;
}
.bootstrap-select.btn-group .dropdown-menu li a.opt {
  position: relative;
  padding-left: 2.25em;
}
.bootstrap-select.btn-group .dropdown-menu li a span.check-mark {
  display: none;
}
.bootstrap-select.btn-group .dropdown-menu li a span.text {
  display: inline-block;
}
.bootstrap-select.btn-group .dropdown-menu li small {
  padding-left: 0.5em;
}
.bootstrap-select.btn-group .dropdown-menu .notify {
  position: absolute;
  bottom: 5px;
  width: 96%;
  margin: 0 2%;
  min-height: 26px;
  padding: 3px 5px;
  background: whitesmoke;
  border: 1px solid #e3e3e3;
  -webkit-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.05);
  pointer-events: none;
  opacity: 0.9;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.bootstrap-select.btn-group .no-results {
  padding: 3px;
  background: #f5f5f5;
  margin: 0 5px;
  white-space: nowrap;
}
.bootstrap-select.btn-group.fit-width .dropdown-toggle .filter-option {
  position: static;
}
.bootstrap-select.btn-group.fit-width .dropdown-toggle .caret {
  position: static;
  top: auto;
  margin-top: -1px;
}
.bootstrap-select.btn-group.show-tick .dropdown-menu li.selected a span.check-mark {
  position: absolute;
  display: inline-block;
  right: 15px;
  margin-top: 5px;
}
.bootstrap-select.btn-group.show-tick .dropdown-menu li a span.text {
  margin-right: 34px;
}

.bootstrap-select.show-menu-arrow.open > .dropdown-toggle {
  z-index: 1061;
}
.bootstrap-select.show-menu-arrow .dropdown-toggle:before {
  content: '';
  border-left: 7px solid transparent;
  border-right: 7px solid transparent;
  border-bottom: 7px solid rgba(204, 204, 204, 0.2);
  position: absolute;
  bottom: -4px;
  left: 9px;
  display: none;
}
.bootstrap-select.show-menu-arrow .dropdown-toggle:after {
  content: '';
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid white;
  position: absolute;
  bottom: -4px;
  left: 10px;
  display: none;
}
.bootstrap-select.show-menu-arrow.dropup .dropdown-toggle:before {
  bottom: auto;
  top: -3px;
  border-top: 7px solid rgba(204, 204, 204, 0.2);
  border-bottom: 0;
}
.bootstrap-select.show-menu-arrow.dropup .dropdown-toggle:after {
  bottom: auto;
  top: -3px;
  border-top: 6px solid white;
  border-bottom: 0;
}
.bootstrap-select.show-menu-arrow.pull-right .dropdown-toggle:before {
  right: 12px;
  left: auto;
}
.bootstrap-select.show-menu-arrow.pull-right .dropdown-toggle:after {
  right: 13px;
  left: auto;
}
.bootstrap-select.show-menu-arrow.open > .dropdown-toggle:before, .bootstrap-select.show-menu-arrow.open > .dropdown-toggle:after {
  display: block;
}

.bs-searchbox,
.bs-actionsbox,
.bs-donebutton {
  padding: 4px 8px;
}

.bs-actionsbox {
  width: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.bs-actionsbox .btn-group button {
  width: 50%;
}

.bs-donebutton {
  float: left;
  width: 100%;
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.bs-donebutton .btn-group button {
  width: 100%;
}

.bs-searchbox + .bs-actionsbox {
  padding: 0 8px 4px;
}
.bs-searchbox .form-control {
  margin-bottom: 0;
  width: 100%;
  float: none;
}

/*!
 * Datetimepicker for Bootstrap 3
 * ! version : 4.7.14
 * https://github.com/Eonasdan/bootstrap-datetimepicker/
 */
.sr-only, .bootstrap-datetimepicker-widget .btn[data-action="incrementHours"]::after, .bootstrap-datetimepicker-widget .btn[data-action="incrementMinutes"]::after, .bootstrap-datetimepicker-widget .btn[data-action="decrementHours"]::after, .bootstrap-datetimepicker-widget .btn[data-action="decrementMinutes"]::after, .bootstrap-datetimepicker-widget .btn[data-action="showHours"]::after, .bootstrap-datetimepicker-widget .btn[data-action="showMinutes"]::after, .bootstrap-datetimepicker-widget .btn[data-action="togglePeriod"]::after, .bootstrap-datetimepicker-widget .btn[data-action="clear"]::after, .bootstrap-datetimepicker-widget .btn[data-action="today"]::after, .bootstrap-datetimepicker-widget .picker-switch::after, .bootstrap-datetimepicker-widget table th.prev::after, .bootstrap-datetimepicker-widget table th.next::after {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}

.bootstrap-datetimepicker-widget {
  list-style: none;
}
.bootstrap-datetimepicker-widget a .btn:hover {
  background-color: transparent;
}
.bootstrap-datetimepicker-widget.dropdown-menu {
  padding: 4px;
  width: 19em;
}
@media (min-width: 768px) {
  .bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs {
    width: 38em;
  }
}
@media (min-width: 992px) {
  .bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs {
    width: 38em;
  }
}
@media (min-width: 1200px) {
  .bootstrap-datetimepicker-widget.dropdown-menu.timepicker-sbs {
    width: 38em;
  }
}
.bootstrap-datetimepicker-widget.dropdown-menu.bottom:before, .bootstrap-datetimepicker-widget.dropdown-menu.bottom:after {
  right: auto;
  left: 12px;
}
.bootstrap-datetimepicker-widget.dropdown-menu.top {
  margin-top: auto;
  margin-bottom: -20px;
}
.bootstrap-datetimepicker-widget.dropdown-menu.top.open {
  margin-top: auto;
  margin-bottom: 5px;
}
.bootstrap-datetimepicker-widget.dropdown-menu.pull-right:before {
  left: auto;
  right: 6px;
}
.bootstrap-datetimepicker-widget.dropdown-menu.pull-right:after {
  left: auto;
  right: 7px;
}
.bootstrap-datetimepicker-widget .list-unstyled {
  margin: 0;
}
.bootstrap-datetimepicker-widget a[data-action] {
  padding: 6px 0;
  border-width: 0;
}
.bootstrap-datetimepicker-widget a[data-action]:hover {
  background-color: transparent;
}
.bootstrap-datetimepicker-widget a[data-action]:active {
  box-shadow: none;
}
.bootstrap-datetimepicker-widget .timepicker-hour, .bootstrap-datetimepicker-widget .timepicker-minute, .bootstrap-datetimepicker-widget .timepicker-second {
  width: 40px;
  height: 40px;
  line-height: 40px;
  font-weight: 300;
  font-size: 1.5em;
  margin: 3px;
  border-radius: 50%;
}
.bootstrap-datetimepicker-widget button[data-action] {
  width: 38px;
  height: 38px;
  padding: 0;
}
.bootstrap-datetimepicker-widget .btn[data-action="incrementHours"]::after {
  content: "Increment Hours";
}
.bootstrap-datetimepicker-widget .btn[data-action="incrementMinutes"]::after {
  content: "Increment Minutes";
}
.bootstrap-datetimepicker-widget .btn[data-action="decrementHours"]::after {
  content: "Decrement Hours";
}
.bootstrap-datetimepicker-widget .btn[data-action="decrementMinutes"]::after {
  content: "Decrement Minutes";
}
.bootstrap-datetimepicker-widget .btn[data-action="showHours"]::after {
  content: "Show Hours";
}
.bootstrap-datetimepicker-widget .btn[data-action="showMinutes"]::after {
  content: "Show Minutes";
}
.bootstrap-datetimepicker-widget .btn[data-action="togglePeriod"]::after {
  content: "Toggle AM/PM";
}
.bootstrap-datetimepicker-widget .btn[data-action="clear"]::after {
  content: "Clear the picker";
}
.bootstrap-datetimepicker-widget .btn[data-action="today"]::after {
  content: "Set the date to today";
}
.bootstrap-datetimepicker-widget .picker-switch {
  text-align: center;
  border-radius: 4px;
}
.bootstrap-datetimepicker-widget .picker-switch::after {
  content: "Toggle Date and Time Screens";
}
.bootstrap-datetimepicker-widget .picker-switch td {
  padding: 0;
  margin: 0;
  height: auto;
  width: auto;
  line-height: inherit;
}
.bootstrap-datetimepicker-widget .picker-switch td span {
  line-height: 2.5;
  height: 2.5em;
  width: 100%;
  border-radius: 4px;
  margin: 2px 0px !important;
}
.bootstrap-datetimepicker-widget table {
  width: 100%;
  margin: 0;
}
.bootstrap-datetimepicker-widget table td > div, .bootstrap-datetimepicker-widget table th > div {
  text-align: center;
}
.bootstrap-datetimepicker-widget table th {
  height: 20px;
  line-height: 20px;
  width: 20px;
}
.bootstrap-datetimepicker-widget table th.picker-switch {
  width: 145px;
}
.bootstrap-datetimepicker-widget table th.disabled, .bootstrap-datetimepicker-widget table th.disabled:hover {
  background: none;
  color: #cfcfca;
  cursor: not-allowed;
}
.bootstrap-datetimepicker-widget table th.prev span, .bootstrap-datetimepicker-widget table th.next span {
  border-radius: 4px;
  height: 27px;
  width: 27px;
  line-height: 28px;
  font-size: 12px;
  border-radius: 50%;
  text-align: center;
}
.bootstrap-datetimepicker-widget table th.prev::after {
  content: "Previous Month";
}
.bootstrap-datetimepicker-widget table th.next::after {
  content: "Next Month";
}
.bootstrap-datetimepicker-widget table th.dow {
  text-align: center;
  border-bottom: 1px solid #E3E3E3;
  font-size: 12px;
  text-transform: uppercase;
  color: #9A9A9A;
  font-weight: 400;
  padding-bottom: 5px;
  padding-top: 10px;
}
.bootstrap-datetimepicker-widget table thead tr:first-child th {
  cursor: pointer;
}
.bootstrap-datetimepicker-widget table thead tr:first-child th:hover span, .bootstrap-datetimepicker-widget table thead tr:first-child th.picker-switch:hover {
  background: #E3E3E3;
}
.bootstrap-datetimepicker-widget table td > div {
  border-radius: 4px;
  height: 54px;
  line-height: 54px;
  width: 54px;
  text-align: center;
}
.bootstrap-datetimepicker-widget table td.cw > div {
  font-size: .8em;
  height: 20px;
  line-height: 20px;
  color: #cfcfca;
}
.bootstrap-datetimepicker-widget table td.day > div {
  height: 30px;
  line-height: 30px;
  width: 30px;
  text-align: center;
  padding: 0px;
  border-radius: 50%;
  margin: 0 auto;
  z-index: -1;
  position: relative;
}
.bootstrap-datetimepicker-widget table td.minute > div, .bootstrap-datetimepicker-widget table td.hour > div {
  border-radius: 50%;
}
.bootstrap-datetimepicker-widget table td.day:hover > div, .bootstrap-datetimepicker-widget table td.hour:hover > div, .bootstrap-datetimepicker-widget table td.minute:hover > div, .bootstrap-datetimepicker-widget table td.second:hover > div {
  background: #E3E3E3;
  cursor: pointer;
}
.bootstrap-datetimepicker-widget table td.old > div, .bootstrap-datetimepicker-widget table td.new > div {
  color: #cfcfca;
}
.bootstrap-datetimepicker-widget table td.today > div:before {
  content: '';
  display: inline-block;
  border: 0 0 7px 7px solid transparent;
  border-bottom-color: #68B3C8;
  border-top-color: rgba(0, 0, 0, 0.2);
  position: absolute;
  bottom: 4px;
  right: 4px;
}
.bootstrap-datetimepicker-widget table td.active > div, .bootstrap-datetimepicker-widget table td.active:hover > div {
  background-color: #68B3C8;
  color: #FFFFFF;
}
.bootstrap-datetimepicker-widget table td.active.today:before > div {
  border-bottom-color: #FFFFFF;
}
.bootstrap-datetimepicker-widget table td.disabled > div, .bootstrap-datetimepicker-widget table td.disabled:hover > div {
  background: none;
  color: #cfcfca;
  cursor: not-allowed;
}
.bootstrap-datetimepicker-widget table td span {
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  margin: 3px 3px;
  cursor: pointer;
  border-radius: 50%;
  text-align: center;
}
.bootstrap-datetimepicker-widget table td span:hover {
  background: #E3E3E3;
}
.bootstrap-datetimepicker-widget table td span.active {
  background-color: #68B3C8;
  color: #FFFFFF;
}
.bootstrap-datetimepicker-widget table td span.old {
  color: #cfcfca;
}
.bootstrap-datetimepicker-widget table td span.disabled, .bootstrap-datetimepicker-widget table td span.disabled:hover {
  background: none;
  color: #cfcfca;
  cursor: not-allowed;
}
.bootstrap-datetimepicker-widget .timepicker-picker span,
.bootstrap-datetimepicker-widget .timepicker-hours span,
.bootstrap-datetimepicker-widget .timepicker-minutes span {
  border-radius: 50% !important;
}
.bootstrap-datetimepicker-widget.usetwentyfour td.hour {
  height: 27px;
  line-height: 27px;
}

.input-group.date .input-group-addon {
  cursor: pointer;
}

.table-condensed > tbody > tr > td,
.table-condensed > tbody > tr > th,
.table-condensed > tfoot > tr > td,
.table-condensed > tfoot > tr > th,
.table-condensed > thead > tr > td,
.table-condensed > thead > tr > th {
  padding: 1px;
  text-align: center;
  z-index: 1;
  cursor: pointer;
}

/*! nouislider - 9.1.0 - 2016-12-10 16:00:32 */
/* Functional styling;
 * These styles are required for noUiSlider to function.
 * You don't need to change these rules to apply your design.
 */
.noUi-target,
.noUi-target * {
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
  -webkit-user-select: none;
  -ms-touch-action: none;
  touch-action: none;
  -ms-user-select: none;
  -moz-user-select: none;
  user-select: none;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.noUi-target {
  position: relative;
  direction: ltr;
}

.noUi-base {
  width: 100%;
  height: 100%;
  position: relative;
  z-index: 1;
  /* Fix 401 */
}

.noUi-connect {
  position: absolute;
  right: 0;
  top: 0;
  left: 0;
  bottom: 0;
}

.noUi-origin {
  position: absolute;
  height: 0;
  width: 0;
}

.noUi-handle {
  position: relative;
  z-index: 1;
}

.noUi-state-tap .noUi-connect,
.noUi-state-tap .noUi-origin {
  -webkit-transition: top 0.3s, right 0.3s, bottom 0.3s, left 0.3s;
  transition: top 0.3s, right 0.3s, bottom 0.3s, left 0.3s;
}

.noUi-state-drag * {
  cursor: inherit !important;
}

/* Painting and performance;
 * Browsers can paint handles in their own layer.
 */
.noUi-base,
.noUi-handle {
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
}

/* Slider size and handle placement;
 */
.noUi-horizontal {
  height: 8px;
}

.noUi-horizontal .noUi-handle {
  background: white;
  /* Old browsers */
  background: -moz-linear-gradient(top, white 0%, #f1f1f2 100%);
  /* FF3.6+ */
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, white), color-stop(100%, #f1f1f2));
  /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(top, white 0%, #f1f1f2 100%);
  /* Chrome10+,Safari5.1+ */
  background: -o-linear-gradient(top, white 0%, #f1f1f2 100%);
  /* Opera 11.10+ */
  background: -ms-linear-gradient(top, white 0%, #f1f1f2 100%);
  /* IE10+ */
  background: linear-gradient(to bottom, white 0%, #f1f1f2 100%);
  /* W3C */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='$white-color', endColorstr='#f1f1f2',GradientType=0 );
  /* IE6-9 */
  border-radius: 50%;
  box-shadow: 0 1px 1px #FFFFFF inset, 0 1px 2px rgba(0, 0, 0, 0.4);
  height: 15px;
  width: 15px;
  cursor: pointer;
  margin-left: -10px;
  margin-top: -4px;
}

.noUi-vertical {
  width: 18px;
}

.noUi-vertical .noUi-handle {
  width: 28px;
  height: 34px;
  left: -6px;
  top: -17px;
}

/* Styling;
 */
.noUi-target {
  background-color: #E5E5E5;
  border-radius: 3px;
}

.noUi-connect {
  background: #3FB8AF;
  border-radius: 3px;
  -webkit-transition: background 450ms;
  transition: background 450ms;
}

/* Handles and cursors;
 */
.noUi-draggable {
  cursor: ew-resize;
}

.noUi-vertical .noUi-draggable {
  cursor: ns-resize;
}

.noUi-handle {
  border-radius: 3px;
  background: #FFF;
  cursor: default;
  box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #EBEBEB, 0 3px 6px -3px #BBB;
}

.noUi-active {
  box-shadow: inset 0 0 1px #FFF, inset 0 1px 7px #DDD, 0 3px 6px -3px #BBB;
}

/* Handle stripes;
 */
/* Disabled state;
 */
[disabled] .noUi-connect {
  background: #B8B8B8;
}

[disabled].noUi-target,
[disabled].noUi-handle,
[disabled] .noUi-handle {
  cursor: not-allowed;
}

/* Base;
 *
 */
.noUi-pips,
.noUi-pips * {
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

.noUi-pips {
  position: absolute;
  color: #999;
}

/* Values;
 *
 */
.noUi-value {
  position: absolute;
  text-align: center;
}

.noUi-value-sub {
  color: #ccc;
  font-size: 10px;
}

/* Markings;
 *
 */
.noUi-marker {
  position: absolute;
  background: #CCC;
}

.noUi-marker-sub {
  background: #AAA;
}

.noUi-marker-large {
  background: #AAA;
}

/* Horizontal layout;
 *
 */
.noUi-pips-horizontal {
  padding: 10px 0;
  height: 80px;
  top: 100%;
  left: 0;
  width: 100%;
}

.noUi-value-horizontal {
  -webkit-transform: translate3d(-50%, 50%, 0);
  transform: translate3d(-50%, 50%, 0);
}

.noUi-marker-horizontal.noUi-marker {
  margin-left: -1px;
  width: 2px;
  height: 5px;
}

.noUi-marker-horizontal.noUi-marker-sub {
  height: 10px;
}

.noUi-marker-horizontal.noUi-marker-large {
  height: 15px;
}

/* Vertical layout;
 *
 */
.noUi-pips-vertical {
  padding: 0 10px;
  height: 100%;
  top: 0;
  left: 100%;
}

.noUi-value-vertical {
  -webkit-transform: translate3d(0, 50%, 0);
  transform: translate3d(0, 50%, 0);
  padding-left: 25px;
}

.noUi-marker-vertical.noUi-marker {
  width: 5px;
  height: 2px;
  margin-top: -1px;
}

.noUi-marker-vertical.noUi-marker-sub {
  width: 10px;
}

.noUi-marker-vertical.noUi-marker-large {
  width: 15px;
}

.noUi-tooltip {
  display: block;
  position: absolute;
  border: 1px solid #D9D9D9;
  border-radius: 3px;
  background: #fff;
  color: #000;
  padding: 5px;
  text-align: center;
}

.noUi-horizontal .noUi-tooltip {
  -webkit-transform: translate(-50%, 0);
  transform: translate(-50%, 0);
  left: 50%;
  bottom: 120%;
}

.noUi-vertical .noUi-tooltip {
  -webkit-transform: translate(0, -50%);
  transform: translate(0, -50%);
  top: 50%;
  right: 120%;
}

.slider.slider-info .noUi-connect, .slider.slider-info.noUi-connect {
  background-color: #68B3C8;
}
.slider.slider-info .noUi-handle {
  border-color: #68B3C8;
}
.slider.slider-success .noUi-connect, .slider.slider-success.noUi-connect {
  background-color: #7AC29A;
}
.slider.slider-success .noUi-handle {
  border-color: #7AC29A;
}
.slider.slider-warning .noUi-connect, .slider.slider-warning.noUi-connect {
  background-color: #F3BB45;
}
.slider.slider-warning .noUi-handle {
  border-color: #F3BB45;
}
.slider.slider-danger .noUi-connect, .slider.slider-danger.noUi-connect {
  background-color: #EB5E28;
}
.slider.slider-danger .noUi-handle {
  border-color: #EB5E28;
}

h1, .h1, h2, .h2, h3, .h3, h4, .h4, h5, .h5, h6, .h6, p, .navbar, .brand, a, .td-name, td {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  font-family: 'Ubuntu', "Helvetica", Arial, sans-serif;
}

h1, .h1, h2, .h2, h3, .h3, h4, .h4 {
  font-weight: 400;
  margin: 30px 0 15px;
}

h1, .h1 {
  font-size: 3.2em;
}

h2, .h2 {
  font-size: 2.6em;
}

h3, .h3 {
  font-size: 1.825em;
  line-height: 1.4;
  margin: 20px 0 10px;
}

h4, .h4 {
  font-size: 1.5em;
  font-weight: 600;
  line-height: 1.2em;
}

h5, .h5 {
  font-size: 1.25em;
  font-weight: 400;
  line-height: 1.4em;
  margin-bottom: 15px;
}

h6, .h6 {
  font-size: 0.9em;
  font-weight: 600;
  text-transform: uppercase;
}

p {
  font-size: 16px;
  line-height: 1.4em;
}

h1 small, h2 small, h3 small, h4 small, h5 small, h6 small, .h1 small, .h2 small, .h3 small, .h4 small, .h5 small, .h6 small, h1 .small, h2 .small, h3 .small, h4 .small, h5 .small, h6 .small, .h1 .small, .h2 .small, .h3 .small, .h4 .small, .h5 .small, .h6 .small {
  color: #9A9A9A;
  font-weight: 300;
  line-height: 1.4em;
}

h1 small, h2 small, h3 small, h1 .small, h2 .small, h3 .small {
  font-size: 60%;
}

.title-uppercase {
  text-transform: uppercase;
}

blockquote {
  font-style: italic;
}

blockquote small {
  font-style: normal;
}

.text-muted {
  color: #cfcfca;
}

.text-primary, .text-primary:hover {
  color: #5e8283;
}

.text-info, .text-info:hover {
  color: #429cb6;
}

.text-success, .text-success:hover {
  color: #54b07d;
}

.text-warning, .text-warning:hover {
  color: #f0a810;
}

.text-danger, .text-danger:hover {
  color: #c84513;
}

.glyphicon {
  line-height: 1;
}

strong {
  color: #484541;
}

.icon-primary {
  color: #7A9E9F;
}

.icon-info {
  color: #68B3C8;
}

.icon-success {
  color: #7AC29A;
}

.icon-warning {
  color: #F3BB45;
}

.icon-danger {
  color: #EB5E28;
}

.chart-legend .text-primary, .chart-legend .text-primary:hover {
  color: #7A9E9F;
}
.chart-legend .text-info, .chart-legend .text-info:hover {
  color: #68B3C8;
}
.chart-legend .text-success, .chart-legend .text-success:hover {
  color: #7AC29A;
}
.chart-legend .text-warning, .chart-legend .text-warning:hover {
  color: #F3BB45;
}
.chart-legend .text-danger, .chart-legend .text-danger:hover {
  color: #EB5E28;
}

.sidebar,
.off-canvas-sidebar {
  width: 260px;
  display: block;
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  z-index: 3;
  overflow: hidden;
}
.sidebar .sidebar-background,
.off-canvas-sidebar .sidebar-background {
  position: absolute;
  z-index: 1;
  height: 100%;
  width: 100%;
  display: block;
  top: 0;
  left: 0;
  background-size: cover;
  background-position: center center;
}
.sidebar .logo,
.off-canvas-sidebar .logo {
  padding: 18px 0px;
  margin: 0;
  box-shadow: inset -1px 0px 0px 0px #cfcfca;
  height: 75px;
  position: relative;
  z-index: 4;
  display: block;
}
.sidebar .logo a.logo-mini,
.off-canvas-sidebar .logo a.logo-mini {
  opacity: 0;
  float: left;
  width: 30px;
  text-align: center;
  margin-left: 23px;
  margin-right: 15px;
}
.sidebar .logo a.logo-normal,
.off-canvas-sidebar .logo a.logo-normal {
  display: block;
  opacity: 1;
  -webkit-transform: translate3d(0px, 0, 0);
  -moz-transform: translate3d(0px, 0, 0);
  -o-transform: translate3d(0px, 0, 0);
  -ms-transform: translate3d(0px, 0, 0);
  transform: translate3d(0px, 0, 0);
}
.sidebar .logo .simple-text,
.off-canvas-sidebar .logo .simple-text {
  text-transform: uppercase;
  padding: 4px 0px;
  display: inline-block;
  font-size: 18px;
  font-weight: 400;
  line-height: 30px;
  white-space: nowrap;
}
.sidebar .logo p,
.off-canvas-sidebar .logo p {
  float: left;
  font-size: 20px;
  margin: 10px 10px;
  line-height: 20px;
}
.sidebar .logo:before,
.off-canvas-sidebar .logo:before {
  content: "";
  position: absolute;
  bottom: 0px;
  right: 15px;
  width: calc(100% - 30px);
  height: 1px;
}
.sidebar .sidebar-wrapper,
.off-canvas-sidebar .sidebar-wrapper {
  position: relative;
  height: calc(100% - 75px);
  z-index: 4;
  box-shadow: inset -1px 0px 0px 0px #cfcfca;
  width: 260px;
}
.sidebar .sidebar-wrapper .dropdown.open .dropdown-backdrop,
.off-canvas-sidebar .sidebar-wrapper .dropdown.open .dropdown-backdrop {
  display: none !important;
}
.sidebar .sidebar-wrapper > .nav,
.off-canvas-sidebar .sidebar-wrapper > .nav {
  margin-top: 10px;
}
.sidebar .sidebar-wrapper > .nav i,
.off-canvas-sidebar .sidebar-wrapper > .nav i {
  font-size: 24px;
  float: left;
  margin-right: 15px;
  line-height: 30px;
  width: 30px;
  text-align: center;
}
.sidebar .sidebar-wrapper > .nav li > a,
.off-canvas-sidebar .sidebar-wrapper > .nav li > a {
  margin: 10px 0px 0px;
  padding-left: 25px;
  padding-right: 25px;
  opacity: .7;
  white-space: nowrap;
}
.sidebar .sidebar-wrapper > .nav li:hover > a,
.off-canvas-sidebar .sidebar-wrapper > .nav li:hover > a {
  opacity: 1;
}
.sidebar .sidebar-wrapper > .nav li.active > a,
.off-canvas-sidebar .sidebar-wrapper > .nav li.active > a {
  color: #7A9E9F;
  opacity: 1;
}
.sidebar .sidebar-wrapper > .nav li.separator,
.off-canvas-sidebar .sidebar-wrapper > .nav li.separator {
  margin: 15px 0;
}
.sidebar .sidebar-wrapper > .nav li.separator:after,
.off-canvas-sidebar .sidebar-wrapper > .nav li.separator:after {
  content: "";
  position: absolute;
  height: 1px;
  right: 10%;
  width: 80%;
  background-color: rgba(255, 255, 255, 0.3);
}
.sidebar .sidebar-wrapper > .nav li.separator + li,
.off-canvas-sidebar .sidebar-wrapper > .nav li.separator + li {
  margin-top: 31px;
}
.sidebar .sidebar-wrapper > .nav p,
.off-canvas-sidebar .sidebar-wrapper > .nav p {
  margin: 0;
  line-height: 30px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  position: relative;
  -webkit-transform: translate3d(0px, 0, 0);
  -moz-transform: translate3d(0px, 0, 0);
  -o-transform: translate3d(0px, 0, 0);
  -ms-transform: translate3d(0px, 0, 0);
  transform: translate3d(0px, 0, 0);
  opacity: 1;
  display: block;
  height: auto;
  white-space: nowrap;
}
.sidebar .sidebar-wrapper > .nav .caret,
.off-canvas-sidebar .sidebar-wrapper > .nav .caret {
  margin-top: 12px;
  position: absolute;
  right: 1px;
}
.sidebar .sidebar-wrapper > .nav [data-toggle="collapse"] ~ div > ul > li > a,
.off-canvas-sidebar .sidebar-wrapper > .nav [data-toggle="collapse"] ~ div > ul > li > a {
  margin: 0;
}
.sidebar .sidebar-wrapper > .nav [data-toggle="collapse"] ~ div > ul > li > a .sidebar-normal,
.sidebar .sidebar-wrapper .user .info [data-toggle="collapse"] ~ div > ul > li > a .sidebar-normal,
.off-canvas-sidebar .sidebar-wrapper > .nav [data-toggle="collapse"] ~ div > ul > li > a .sidebar-normal,
.off-canvas-sidebar .sidebar-wrapper .user .info [data-toggle="collapse"] ~ div > ul > li > a .sidebar-normal {
  margin: 0;
  position: relative;
  -webkit-transform: translate3d(0px, 0, 0);
  -moz-transform: translate3d(0px, 0, 0);
  -o-transform: translate3d(0px, 0, 0);
  -ms-transform: translate3d(0px, 0, 0);
  transform: translate3d(0px, 0, 0);
  opacity: 1;
  white-space: nowrap;
  display: block;
}
.sidebar .sidebar-wrapper > .nav [data-toggle="collapse"] ~ div > ul > li > a .sidebar-mini,
.sidebar .sidebar-wrapper .user .info [data-toggle="collapse"] ~ div > ul > li > a .sidebar-mini,
.off-canvas-sidebar .sidebar-wrapper > .nav [data-toggle="collapse"] ~ div > ul > li > a .sidebar-mini,
.off-canvas-sidebar .sidebar-wrapper .user .info [data-toggle="collapse"] ~ div > ul > li > a .sidebar-mini {
  text-transform: uppercase;
  float: left;
  width: 30px;
  text-align: center;
  margin-right: 15px;
  letter-spacing: 1px;
}
.sidebar .sidebar-wrapper > .nav [data-toggle="collapse"] ~ div > ul > li > a i,
.sidebar .sidebar-wrapper .user .info [data-toggle="collapse"] ~ div > ul > li > a i,
.off-canvas-sidebar .sidebar-wrapper > .nav [data-toggle="collapse"] ~ div > ul > li > a i,
.off-canvas-sidebar .sidebar-wrapper .user .info [data-toggle="collapse"] ~ div > ul > li > a i {
  font-size: 17px;
  line-height: 20px;
  width: 26px;
}
.sidebar .user,
.off-canvas-sidebar .user {
  position: relative;
  margin-top: 20px;
  padding-bottom: 20px;
}
.sidebar .user:after,
.off-canvas-sidebar .user:after {
  content: "";
  position: absolute;
  bottom: 0px;
  right: 15px;
  width: calc(100% - 30px);
  height: 1px;
  background-color: rgba(255, 255, 255, 0.3);
}
.sidebar .user .photo,
.off-canvas-sidebar .user .photo {
  width: 34px;
  height: 34px;
  overflow: hidden;
  float: left;
  margin-right: 11px;
  z-index: 5;
  border-radius: 50%;
  margin-left: 23px;
}
.sidebar .user .photo img,
.off-canvas-sidebar .user .photo img {
  width: 100%;
}
.sidebar .user .info a,
.off-canvas-sidebar .user .info a {
  padding: 7px 25px;
  white-space: nowrap;
  display: block;
  opacity: .7;
  position: relative;
}
.sidebar .user .info a:hover, .sidebar .user .info a:visited,
.off-canvas-sidebar .user .info a:hover,
.off-canvas-sidebar .user .info a:visited {
  opacity: 1;
}
.sidebar .user .info > a > span,
.off-canvas-sidebar .user .info > a > span {
  opacity: 1;
  line-height: 22px;
  display: block;
  position: relative;
}
.sidebar .user .info .caret,
.off-canvas-sidebar .user .info .caret {
  position: absolute;
  top: 11px;
  right: 0px;
}
.sidebar:after, .sidebar:before,
.off-canvas-sidebar:after,
.off-canvas-sidebar:before {
  display: block;
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 2;
  background: #FFFFFF;
}
.sidebar:after, .sidebar:before, .sidebar[data-background-color="white"]:after, .sidebar[data-background-color="white"]:before,
.off-canvas-sidebar:after,
.off-canvas-sidebar:before,
.off-canvas-sidebar[data-background-color="white"]:after,
.off-canvas-sidebar[data-background-color="white"]:before {
  background-color: #FFFFFF;
}
.sidebar .user, .sidebar[data-background-color="white"] .user,
.off-canvas-sidebar .user,
.off-canvas-sidebar[data-background-color="white"] .user {
  position: relative;
  z-index: 4;
}
.sidebar .user:after, .sidebar[data-background-color="white"] .user:after,
.off-canvas-sidebar .user:after,
.off-canvas-sidebar[data-background-color="white"] .user:after {
  background-color: rgba(102, 97, 91, 0.3);
}
.sidebar .user .info a, .sidebar[data-background-color="white"] .user .info a,
.off-canvas-sidebar .user .info a,
.off-canvas-sidebar[data-background-color="white"] .user .info a {
  color: #66615B;
}
.sidebar .logo:before, .sidebar[data-background-color="white"] .logo:before,
.off-canvas-sidebar .logo:before,
.off-canvas-sidebar[data-background-color="white"] .logo:before {
  background-color: rgba(102, 97, 91, 0.3);
}
.sidebar .logo p, .sidebar[data-background-color="white"] .logo p,
.off-canvas-sidebar .logo p,
.off-canvas-sidebar[data-background-color="white"] .logo p {
  color: #66615B;
}
.sidebar .logo .simple-text, .sidebar[data-background-color="white"] .logo .simple-text,
.off-canvas-sidebar .logo .simple-text,
.off-canvas-sidebar[data-background-color="white"] .logo .simple-text {
  color: #66615B;
}
.sidebar .nav li:not(.active) > a, .sidebar[data-background-color="white"] .nav li:not(.active) > a,
.off-canvas-sidebar .nav li:not(.active) > a,
.off-canvas-sidebar[data-background-color="white"] .nav li:not(.active) > a {
  color: #e8e8e8;
}
.sidebar .nav .divider, .sidebar[data-background-color="white"] .nav .divider,
.off-canvas-sidebar .nav .divider,
.off-canvas-sidebar[data-background-color="white"] .nav .divider {
  background-color: rgba(102, 97, 91, 0.2);
}
.sidebar .user, .sidebar[data-background-color="white"] .user,
.off-canvas-sidebar .user,
.off-canvas-sidebar[data-background-color="white"] .user {
  margin-top: 20px;
}
.sidebar .user .photo, .sidebar[data-background-color="white"] .user .photo,
.off-canvas-sidebar .user .photo,
.off-canvas-sidebar[data-background-color="white"] .user .photo {
  border: 2px solid rgba(0, 0, 0, 0.15);
}
.sidebar[data-background-color="brown"]:after, .sidebar[data-background-color="brown"]:before,
.off-canvas-sidebar[data-background-color="brown"]:after,
.off-canvas-sidebar[data-background-color="brown"]:before {
  background-color: #66615b;
}
.sidebar[data-background-color="brown"] .user,
.off-canvas-sidebar[data-background-color="brown"] .user {
  position: relative;
  z-index: 4;
}
.sidebar[data-background-color="brown"] .user:after,
.off-canvas-sidebar[data-background-color="brown"] .user:after {
  background-color: rgba(255, 255, 255, 0.3);
}
.sidebar[data-background-color="brown"] .user .info a,
.off-canvas-sidebar[data-background-color="brown"] .user .info a {
  color: #FFFFFF;
}
.sidebar[data-background-color="brown"] .logo:before,
.off-canvas-sidebar[data-background-color="brown"] .logo:before {
  background-color: rgba(255, 255, 255, 0.3);
}
.sidebar[data-background-color="brown"] .logo p,
.off-canvas-sidebar[data-background-color="brown"] .logo p {
  color: #FFFFFF;
}
.sidebar[data-background-color="brown"] .logo .simple-text,
.off-canvas-sidebar[data-background-color="brown"] .logo .simple-text {
  color: #FFFFFF;
}
.sidebar[data-background-color="brown"] .nav li:not(.active) > a,
.off-canvas-sidebar[data-background-color="brown"] .nav li:not(.active) > a {
  color: #FFFFFF;
}
.sidebar[data-background-color="brown"] .nav .divider,
.off-canvas-sidebar[data-background-color="brown"] .nav .divider {
  background-color: rgba(255, 255, 255, 0.2);
}
.sidebar[data-background-color="brown"][data-active-color="danger"] .nav li.active > a,
.off-canvas-sidebar[data-background-color="brown"][data-active-color="danger"] .nav li.active > a {
  color: #ef8157;
  opacity: 1;
}
.sidebar[data-background-color="brown"] .info a,
.off-canvas-sidebar[data-background-color="brown"] .info a {
  color: #FFFFFF;
}
.sidebar[data-background-color="brown"] .user .photo,
.off-canvas-sidebar[data-background-color="brown"] .user .photo {
  border-color: rgba(255, 255, 255, 0.3);
}
.sidebar[data-active-color="primary"] .nav li.active > a,
.off-canvas-sidebar[data-active-color="primary"] .nav li.active > a {
  color: #7A9E9F;
  opacity: 1;
}
.sidebar[data-active-color="info"] .nav li.active > a,
.off-canvas-sidebar[data-active-color="info"] .nav li.active > a {
  color: #68B3C8;
  opacity: 1;
}
.sidebar[data-active-color="success"] .nav li.active > a,
.off-canvas-sidebar[data-active-color="success"] .nav li.active > a {
  color: #7AC29A;
  opacity: 1;
}
.sidebar[data-active-color="warning"] .nav li.active > a,
.off-canvas-sidebar[data-active-color="warning"] .nav li.active > a {
  color: #F3BB45;
  opacity: 1;
}
.sidebar[data-active-color="danger"] .nav li.active > a,
.off-canvas-sidebar[data-active-color="danger"] .nav li.active > a {
  color: #EB5E28;
  opacity: 1;
}

.main-panel {
  background-color: #f4f3ef;
  overflow: auto;
  position: relative;
  z-index: 2;
  float: right;
  width: calc(100% - 260px);
  min-height: 100%;
}
.main-panel > .content {
  padding: 30px 15px 0 15px;
  min-height: calc(100% - 123px);
}
.main-panel > .content .row .col-md-12 > .title {
  margin-top: 0;
}
.main-panel > .footer {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.main-panel .navbar {
  margin-bottom: 0;
}

.sidebar,
.main-panel,
.sidebar-wrapper {
  -webkit-transition-property: top,bottom,width;
  transition-property: top,bottom, width;
  -webkit-transition-duration: .2s,.2s, .35s;
  transition-duration: .2s,.2s, .35s;
  -webkit-transition-timing-function: linear,linear,ease;
  transition-timing-function: linear,linear,ease;
  -webkit-overflow-scrolling: touch;
}

.sidebar,
.main-panel {
  max-height: 100%;
  height: 100%;
}

.perfect-scrollbar-on .sidebar .sidebar-wrapper, .sidebar .sidebar-wrapper, .perfect-scrollbar-on
.main-panel,
.main-panel {
  overflow: hidden;
}

.perfect-scrollbar-off .sidebar .sidebar-wrapper,
.perfect-scrollbar-off .main-panel {
  overflow: auto;
}

@media (min-width: 992px) {
  .sidebar-mini .sidebar,
  .sidebar-mini .sidebar .sidebar-wrapper {
    width: 80px;
  }
  .sidebar-mini .main-panel {
    width: calc(100% - 80px);
  }
  .sidebar-mini .sidebar {
    display: block;
    font-weight: 200;
    z-index: 3;
  }
  .sidebar-mini .sidebar .logo a.logo-mini {
    opacity: 1;
  }
  .sidebar-mini .sidebar .logo a.logo-normal {
    opacity: 0;
    -webkit-transform: translate3d(-25px, 0, 0);
    -moz-transform: translate3d(-25px, 0, 0);
    -o-transform: translate3d(-25px, 0, 0);
    -ms-transform: translate3d(-25px, 0, 0);
    transform: translate3d(-25px, 0, 0);
  }
  .sidebar-mini .sidebar .sidebar-wrapper > .nav [data-toggle="collapse"] ~ div > ul > li > a .sidebar-normal,
  .sidebar-mini .sidebar .sidebar-wrapper .user .info [data-toggle="collapse"] ~ div > ul > li > a .sidebar-normal,
  .sidebar-mini .sidebar .sidebar-wrapper .user .info > a > span,
  .sidebar-mini .sidebar .sidebar-wrapper > .nav li > a p {
    -webkit-transform: translate3d(-25px, 0, 0);
    -moz-transform: translate3d(-25px, 0, 0);
    -o-transform: translate3d(-25px, 0, 0);
    -ms-transform: translate3d(-25px, 0, 0);
    transform: translate3d(-25px, 0, 0);
    opacity: 0;
  }
  .sidebar-mini .sidebar:hover {
    width: 260px;
  }
  .sidebar-mini .sidebar:hover .logo a.logo-normal {
    opacity: 1;
    -webkit-transform: translate3d(0px, 0, 0);
    -moz-transform: translate3d(0px, 0, 0);
    -o-transform: translate3d(0px, 0, 0);
    -ms-transform: translate3d(0px, 0, 0);
    transform: translate3d(0px, 0, 0);
  }
  .sidebar-mini .sidebar:hover .sidebar-wrapper {
    width: 260px;
  }
  .sidebar-mini .sidebar:hover .sidebar-wrapper > .nav li > a p,
  .sidebar-mini .sidebar:hover .sidebar-wrapper > .nav [data-toggle="collapse"] ~ div > ul > li > a .sidebar-normal,
  .sidebar-mini .sidebar:hover .sidebar-wrapper .user .info [data-toggle="collapse"] ~ div > ul > li > a .sidebar-normal,
  .sidebar-mini .sidebar:hover .sidebar-wrapper .user .info > a > span {
    -webkit-transform: translate3d(0px, 0, 0);
    -moz-transform: translate3d(0px, 0, 0);
    -o-transform: translate3d(0px, 0, 0);
    -ms-transform: translate3d(0px, 0, 0);
    transform: translate3d(0px, 0, 0);
    opacity: 1;
  }
  .sidebar-mini .sidebar:hover .sidebar-wrapper > .nav li.active > a:not([data-toggle="collapse"]):before, .sidebar-mini .sidebar:hover .sidebar-wrapper > .nav li.active > a:not([data-toggle="collapse"]):after {
    opacity: 0;
  }
}
.btn,
.navbar .navbar-nav > li > a.btn {
  border-radius: 20px;
  box-sizing: border-box;
  border-width: 2px;
  background-color: transparent;
  font-size: 14px;
  font-weight: 600;
  padding: 7px 18px;
  border-color: #66615B;
  color: #66615B;
  -webkit-transition: all 150ms linear;
  -moz-transition: all 150ms linear;
  -o-transition: all 150ms linear;
  -ms-transition: all 150ms linear;
  transition: all 150ms linear;
}
.btn:hover, .btn:focus, .btn:active, .btn.active, .btn:active:focus, .btn:active:hover, .open > .btn.dropdown-toggle, .open > .btn.dropdown-toggle:focus, .open > .btn.dropdown-toggle:hover,
.navbar .navbar-nav > li > a.btn:hover,
.navbar .navbar-nav > li > a.btn:focus,
.navbar .navbar-nav > li > a.btn:active,
.navbar .navbar-nav > li > a.btn.active,
.navbar .navbar-nav > li > a.btn:active:focus,
.navbar .navbar-nav > li > a.btn:active:hover, .open >
.navbar .navbar-nav > li > a.btn.dropdown-toggle, .open >
.navbar .navbar-nav > li > a.btn.dropdown-toggle:focus, .open >
.navbar .navbar-nav > li > a.btn.dropdown-toggle:hover {
  background-color: #66615B;
  color: rgba(255, 255, 255, 0.85);
  border-color: #66615B;
}
.btn:hover .caret, .btn:focus .caret, .btn:active .caret, .btn.active .caret, .btn:active:focus .caret, .btn:active:hover .caret, .open > .btn.dropdown-toggle .caret, .open > .btn.dropdown-toggle:focus .caret, .open > .btn.dropdown-toggle:hover .caret,
.navbar .navbar-nav > li > a.btn:hover .caret,
.navbar .navbar-nav > li > a.btn:focus .caret,
.navbar .navbar-nav > li > a.btn:active .caret,
.navbar .navbar-nav > li > a.btn.active .caret,
.navbar .navbar-nav > li > a.btn:active:focus .caret,
.navbar .navbar-nav > li > a.btn:active:hover .caret, .open >
.navbar .navbar-nav > li > a.btn.dropdown-toggle .caret, .open >
.navbar .navbar-nav > li > a.btn.dropdown-toggle:focus .caret, .open >
.navbar .navbar-nav > li > a.btn.dropdown-toggle:hover .caret {
  border-top-color: rgba(255, 255, 255, 0.85);
}
.btn.disabled, .btn.disabled:hover, .btn.disabled:focus, .btn.disabled.focus, .btn.disabled:active, .btn.disabled.active, .btn:disabled, .btn:disabled:hover, .btn:disabled:focus, .btn:disabled.focus, .btn:disabled:active, .btn:disabled.active, .btn[disabled], .btn[disabled]:hover, .btn[disabled]:focus, .btn[disabled].focus, .btn[disabled]:active, .btn[disabled].active, fieldset[disabled] .btn, fieldset[disabled] .btn:hover, fieldset[disabled] .btn:focus, fieldset[disabled] .btn.focus, fieldset[disabled] .btn:active, fieldset[disabled] .btn.active,
.navbar .navbar-nav > li > a.btn.disabled,
.navbar .navbar-nav > li > a.btn.disabled:hover,
.navbar .navbar-nav > li > a.btn.disabled:focus,
.navbar .navbar-nav > li > a.btn.disabled.focus,
.navbar .navbar-nav > li > a.btn.disabled:active,
.navbar .navbar-nav > li > a.btn.disabled.active,
.navbar .navbar-nav > li > a.btn:disabled,
.navbar .navbar-nav > li > a.btn:disabled:hover,
.navbar .navbar-nav > li > a.btn:disabled:focus,
.navbar .navbar-nav > li > a.btn:disabled.focus,
.navbar .navbar-nav > li > a.btn:disabled:active,
.navbar .navbar-nav > li > a.btn:disabled.active,
.navbar .navbar-nav > li > a.btn[disabled],
.navbar .navbar-nav > li > a.btn[disabled]:hover,
.navbar .navbar-nav > li > a.btn[disabled]:focus,
.navbar .navbar-nav > li > a.btn[disabled].focus,
.navbar .navbar-nav > li > a.btn[disabled]:active,
.navbar .navbar-nav > li > a.btn[disabled].active, fieldset[disabled]
.navbar .navbar-nav > li > a.btn, fieldset[disabled]
.navbar .navbar-nav > li > a.btn:hover, fieldset[disabled]
.navbar .navbar-nav > li > a.btn:focus, fieldset[disabled]
.navbar .navbar-nav > li > a.btn.focus, fieldset[disabled]
.navbar .navbar-nav > li > a.btn:active, fieldset[disabled]
.navbar .navbar-nav > li > a.btn.active {
  background-color: transparent;
  border-color: #66615B;
}
.btn.btn-fill,
.navbar .navbar-nav > li > a.btn.btn-fill {
  color: #FFFFFF;
  background-color: #66615B;
  opacity: 1;
  filter: alpha(opacity=100);
}
.btn.btn-fill:hover, .btn.btn-fill:focus, .btn.btn-fill:active, .btn.btn-fill.active, .open > .btn.btn-fill.dropdown-toggle,
.navbar .navbar-nav > li > a.btn.btn-fill:hover,
.navbar .navbar-nav > li > a.btn.btn-fill:focus,
.navbar .navbar-nav > li > a.btn.btn-fill:active,
.navbar .navbar-nav > li > a.btn.btn-fill.active, .open >
.navbar .navbar-nav > li > a.btn.btn-fill.dropdown-toggle {
  background-color: #484541;
  color: #FFFFFF;
  border-color: #484541;
}
.btn.btn-fill .caret,
.navbar .navbar-nav > li > a.btn.btn-fill .caret {
  border-top-color: #FFFFFF;
}
.btn.btn-simple:hover, .btn.btn-simple:focus, .btn.btn-simple:active, .btn.btn-simple.active, .open > .btn.btn-simple.dropdown-toggle,
.navbar .navbar-nav > li > a.btn.btn-simple:hover,
.navbar .navbar-nav > li > a.btn.btn-simple:focus,
.navbar .navbar-nav > li > a.btn.btn-simple:active,
.navbar .navbar-nav > li > a.btn.btn-simple.active, .open >
.navbar .navbar-nav > li > a.btn.btn-simple.dropdown-toggle {
  background-color: transparent;
  color: #484541;
}
.btn.btn-simple .caret,
.navbar .navbar-nav > li > a.btn.btn-simple .caret {
  border-top-color: #FFFFFF;
}
.btn .caret,
.navbar .navbar-nav > li > a.btn .caret {
  border-top-color: #66615B;
}
.btn:hover, .btn:focus,
.navbar .navbar-nav > li > a.btn:hover,
.navbar .navbar-nav > li > a.btn:focus {
  outline: 0 !important;
}
.btn:active, .btn.active, .open > .btn.dropdown-toggle,
.navbar .navbar-nav > li > a.btn:active,
.navbar .navbar-nav > li > a.btn.active, .open >
.navbar .navbar-nav > li > a.btn.dropdown-toggle {
  -webkit-box-shadow: none;
  box-shadow: none;
  outline: 0 !important;
}
.btn.btn-icon,
.navbar .navbar-nav > li > a.btn.btn-icon {
  border-radius: 25px;
  padding: 7px 10px;
}
.btn.btn-icon i,
.navbar .navbar-nav > li > a.btn.btn-icon i {
  margin-right: 0px;
}
.btn [class*="ti-"],
.navbar .navbar-nav > li > a.btn [class*="ti-"] {
  vertical-align: middle;
}

.btn-group .btn + .btn,
.btn-group .btn + .btn-group,
.btn-group .btn-group + .btn,
.btn-group .btn-group + .btn-group {
  margin-left: -2px;
}

.navbar .navbar-nav > li > a.btn.btn-primary, .btn-primary {
  border-color: #7A9E9F;
  color: #7A9E9F;
}
.navbar .navbar-nav > li > a.btn.btn-primary:hover, .navbar .navbar-nav > li > a.btn.btn-primary:focus, .navbar .navbar-nav > li > a.btn.btn-primary:active, .navbar .navbar-nav > li > a.btn.btn-primary.active, .navbar .navbar-nav > li > a.btn.btn-primary:active:focus, .navbar .navbar-nav > li > a.btn.btn-primary:active:hover, .open > .navbar .navbar-nav > li > a.btn.btn-primary.dropdown-toggle, .open > .navbar .navbar-nav > li > a.btn.btn-primary.dropdown-toggle:focus, .open > .navbar .navbar-nav > li > a.btn.btn-primary.dropdown-toggle:hover, .btn-primary:hover, .btn-primary:focus, .btn-primary:active, .btn-primary.active, .btn-primary:active:focus, .btn-primary:active:hover, .open > .btn-primary.dropdown-toggle, .open > .btn-primary.dropdown-toggle:focus, .open > .btn-primary.dropdown-toggle:hover {
  background-color: #7A9E9F;
  color: rgba(255, 255, 255, 0.85);
  border-color: #7A9E9F;
}
.navbar .navbar-nav > li > a.btn.btn-primary:hover .caret, .navbar .navbar-nav > li > a.btn.btn-primary:focus .caret, .navbar .navbar-nav > li > a.btn.btn-primary:active .caret, .navbar .navbar-nav > li > a.btn.btn-primary.active .caret, .navbar .navbar-nav > li > a.btn.btn-primary:active:focus .caret, .navbar .navbar-nav > li > a.btn.btn-primary:active:hover .caret, .open > .navbar .navbar-nav > li > a.btn.btn-primary.dropdown-toggle .caret, .open > .navbar .navbar-nav > li > a.btn.btn-primary.dropdown-toggle:focus .caret, .open > .navbar .navbar-nav > li > a.btn.btn-primary.dropdown-toggle:hover .caret, .btn-primary:hover .caret, .btn-primary:focus .caret, .btn-primary:active .caret, .btn-primary.active .caret, .btn-primary:active:focus .caret, .btn-primary:active:hover .caret, .open > .btn-primary.dropdown-toggle .caret, .open > .btn-primary.dropdown-toggle:focus .caret, .open > .btn-primary.dropdown-toggle:hover .caret {
  border-top-color: rgba(255, 255, 255, 0.85);
}
.navbar .navbar-nav > li > a.btn.btn-primary.disabled, .navbar .navbar-nav > li > a.btn.btn-primary.disabled:hover, .navbar .navbar-nav > li > a.btn.btn-primary.disabled:focus, .navbar .navbar-nav > li > a.btn.btn-primary.disabled.focus, .navbar .navbar-nav > li > a.btn.btn-primary.disabled:active, .navbar .navbar-nav > li > a.btn.btn-primary.disabled.active, .navbar .navbar-nav > li > a.btn.btn-primary:disabled, .navbar .navbar-nav > li > a.btn.btn-primary:disabled:hover, .navbar .navbar-nav > li > a.btn.btn-primary:disabled:focus, .navbar .navbar-nav > li > a.btn.btn-primary:disabled.focus, .navbar .navbar-nav > li > a.btn.btn-primary:disabled:active, .navbar .navbar-nav > li > a.btn.btn-primary:disabled.active, .navbar .navbar-nav > li > a.btn.btn-primary[disabled], .navbar .navbar-nav > li > a.btn.btn-primary[disabled]:hover, .navbar .navbar-nav > li > a.btn.btn-primary[disabled]:focus, .navbar .navbar-nav > li > a.btn.btn-primary[disabled].focus, .navbar .navbar-nav > li > a.btn.btn-primary[disabled]:active, .navbar .navbar-nav > li > a.btn.btn-primary[disabled].active, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-primary, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-primary:hover, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-primary:focus, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-primary.focus, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-primary:active, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-primary.active, .btn-primary.disabled, .btn-primary.disabled:hover, .btn-primary.disabled:focus, .btn-primary.disabled.focus, .btn-primary.disabled:active, .btn-primary.disabled.active, .btn-primary:disabled, .btn-primary:disabled:hover, .btn-primary:disabled:focus, .btn-primary:disabled.focus, .btn-primary:disabled:active, .btn-primary:disabled.active, .btn-primary[disabled], .btn-primary[disabled]:hover, .btn-primary[disabled]:focus, .btn-primary[disabled].focus, .btn-primary[disabled]:active, .btn-primary[disabled].active, fieldset[disabled] .btn-primary, fieldset[disabled] .btn-primary:hover, fieldset[disabled] .btn-primary:focus, fieldset[disabled] .btn-primary.focus, fieldset[disabled] .btn-primary:active, fieldset[disabled] .btn-primary.active {
  background-color: transparent;
  border-color: #7A9E9F;
}
.navbar .navbar-nav > li > a.btn.btn-primary.btn-fill, .btn-primary.btn-fill {
  color: #FFFFFF;
  background-color: #7A9E9F;
  opacity: 1;
  filter: alpha(opacity=100);
}
.navbar .navbar-nav > li > a.btn.btn-primary.btn-fill:hover, .navbar .navbar-nav > li > a.btn.btn-primary.btn-fill:focus, .navbar .navbar-nav > li > a.btn.btn-primary.btn-fill:active, .navbar .navbar-nav > li > a.btn.btn-primary.btn-fill.active, .open > .navbar .navbar-nav > li > a.btn.btn-primary.btn-fill.dropdown-toggle, .btn-primary.btn-fill:hover, .btn-primary.btn-fill:focus, .btn-primary.btn-fill:active, .btn-primary.btn-fill.active, .open > .btn-primary.btn-fill.dropdown-toggle {
  background-color: #5e8283;
  color: #FFFFFF;
  border-color: #5e8283;
}
.navbar .navbar-nav > li > a.btn.btn-primary.btn-fill .caret, .btn-primary.btn-fill .caret {
  border-top-color: #FFFFFF;
}
.navbar .navbar-nav > li > a.btn.btn-primary.btn-simple:hover, .navbar .navbar-nav > li > a.btn.btn-primary.btn-simple:focus, .navbar .navbar-nav > li > a.btn.btn-primary.btn-simple:active, .navbar .navbar-nav > li > a.btn.btn-primary.btn-simple.active, .open > .navbar .navbar-nav > li > a.btn.btn-primary.btn-simple.dropdown-toggle, .btn-primary.btn-simple:hover, .btn-primary.btn-simple:focus, .btn-primary.btn-simple:active, .btn-primary.btn-simple.active, .open > .btn-primary.btn-simple.dropdown-toggle {
  background-color: transparent;
  color: #5e8283;
}
.navbar .navbar-nav > li > a.btn.btn-primary.btn-simple .caret, .btn-primary.btn-simple .caret {
  border-top-color: #FFFFFF;
}
.navbar .navbar-nav > li > a.btn.btn-primary .caret, .btn-primary .caret {
  border-top-color: #7A9E9F;
}

.navbar .navbar-nav > li > a.btn.btn-success, .btn-success {
  border-color: #7AC29A;
  color: #7AC29A;
}
.navbar .navbar-nav > li > a.btn.btn-success:hover, .navbar .navbar-nav > li > a.btn.btn-success:focus, .navbar .navbar-nav > li > a.btn.btn-success:active, .navbar .navbar-nav > li > a.btn.btn-success.active, .navbar .navbar-nav > li > a.btn.btn-success:active:focus, .navbar .navbar-nav > li > a.btn.btn-success:active:hover, .open > .navbar .navbar-nav > li > a.btn.btn-success.dropdown-toggle, .open > .navbar .navbar-nav > li > a.btn.btn-success.dropdown-toggle:focus, .open > .navbar .navbar-nav > li > a.btn.btn-success.dropdown-toggle:hover, .btn-success:hover, .btn-success:focus, .btn-success:active, .btn-success.active, .btn-success:active:focus, .btn-success:active:hover, .open > .btn-success.dropdown-toggle, .open > .btn-success.dropdown-toggle:focus, .open > .btn-success.dropdown-toggle:hover {
  background-color: #7AC29A;
  color: rgba(255, 255, 255, 0.85);
  border-color: #7AC29A;
}
.navbar .navbar-nav > li > a.btn.btn-success:hover .caret, .navbar .navbar-nav > li > a.btn.btn-success:focus .caret, .navbar .navbar-nav > li > a.btn.btn-success:active .caret, .navbar .navbar-nav > li > a.btn.btn-success.active .caret, .navbar .navbar-nav > li > a.btn.btn-success:active:focus .caret, .navbar .navbar-nav > li > a.btn.btn-success:active:hover .caret, .open > .navbar .navbar-nav > li > a.btn.btn-success.dropdown-toggle .caret, .open > .navbar .navbar-nav > li > a.btn.btn-success.dropdown-toggle:focus .caret, .open > .navbar .navbar-nav > li > a.btn.btn-success.dropdown-toggle:hover .caret, .btn-success:hover .caret, .btn-success:focus .caret, .btn-success:active .caret, .btn-success.active .caret, .btn-success:active:focus .caret, .btn-success:active:hover .caret, .open > .btn-success.dropdown-toggle .caret, .open > .btn-success.dropdown-toggle:focus .caret, .open > .btn-success.dropdown-toggle:hover .caret {
  border-top-color: rgba(255, 255, 255, 0.85);
}
.navbar .navbar-nav > li > a.btn.btn-success.disabled, .navbar .navbar-nav > li > a.btn.btn-success.disabled:hover, .navbar .navbar-nav > li > a.btn.btn-success.disabled:focus, .navbar .navbar-nav > li > a.btn.btn-success.disabled.focus, .navbar .navbar-nav > li > a.btn.btn-success.disabled:active, .navbar .navbar-nav > li > a.btn.btn-success.disabled.active, .navbar .navbar-nav > li > a.btn.btn-success:disabled, .navbar .navbar-nav > li > a.btn.btn-success:disabled:hover, .navbar .navbar-nav > li > a.btn.btn-success:disabled:focus, .navbar .navbar-nav > li > a.btn.btn-success:disabled.focus, .navbar .navbar-nav > li > a.btn.btn-success:disabled:active, .navbar .navbar-nav > li > a.btn.btn-success:disabled.active, .navbar .navbar-nav > li > a.btn.btn-success[disabled], .navbar .navbar-nav > li > a.btn.btn-success[disabled]:hover, .navbar .navbar-nav > li > a.btn.btn-success[disabled]:focus, .navbar .navbar-nav > li > a.btn.btn-success[disabled].focus, .navbar .navbar-nav > li > a.btn.btn-success[disabled]:active, .navbar .navbar-nav > li > a.btn.btn-success[disabled].active, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-success, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-success:hover, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-success:focus, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-success.focus, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-success:active, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-success.active, .btn-success.disabled, .btn-success.disabled:hover, .btn-success.disabled:focus, .btn-success.disabled.focus, .btn-success.disabled:active, .btn-success.disabled.active, .btn-success:disabled, .btn-success:disabled:hover, .btn-success:disabled:focus, .btn-success:disabled.focus, .btn-success:disabled:active, .btn-success:disabled.active, .btn-success[disabled], .btn-success[disabled]:hover, .btn-success[disabled]:focus, .btn-success[disabled].focus, .btn-success[disabled]:active, .btn-success[disabled].active, fieldset[disabled] .btn-success, fieldset[disabled] .btn-success:hover, fieldset[disabled] .btn-success:focus, fieldset[disabled] .btn-success.focus, fieldset[disabled] .btn-success:active, fieldset[disabled] .btn-success.active {
  background-color: transparent;
  border-color: #7AC29A;
}
.navbar .navbar-nav > li > a.btn.btn-success.btn-fill, .btn-success.btn-fill {
  color: #FFFFFF;
  background-color: #7AC29A;
  opacity: 1;
  filter: alpha(opacity=100);
}
.navbar .navbar-nav > li > a.btn.btn-success.btn-fill:hover, .navbar .navbar-nav > li > a.btn.btn-success.btn-fill:focus, .navbar .navbar-nav > li > a.btn.btn-success.btn-fill:active, .navbar .navbar-nav > li > a.btn.btn-success.btn-fill.active, .open > .navbar .navbar-nav > li > a.btn.btn-success.btn-fill.dropdown-toggle, .btn-success.btn-fill:hover, .btn-success.btn-fill:focus, .btn-success.btn-fill:active, .btn-success.btn-fill.active, .open > .btn-success.btn-fill.dropdown-toggle {
  background-color: #54b07d;
  color: #FFFFFF;
  border-color: #54b07d;
}
.navbar .navbar-nav > li > a.btn.btn-success.btn-fill .caret, .btn-success.btn-fill .caret {
  border-top-color: #FFFFFF;
}
.navbar .navbar-nav > li > a.btn.btn-success.btn-simple:hover, .navbar .navbar-nav > li > a.btn.btn-success.btn-simple:focus, .navbar .navbar-nav > li > a.btn.btn-success.btn-simple:active, .navbar .navbar-nav > li > a.btn.btn-success.btn-simple.active, .open > .navbar .navbar-nav > li > a.btn.btn-success.btn-simple.dropdown-toggle, .btn-success.btn-simple:hover, .btn-success.btn-simple:focus, .btn-success.btn-simple:active, .btn-success.btn-simple.active, .open > .btn-success.btn-simple.dropdown-toggle {
  background-color: transparent;
  color: #54b07d;
}
.navbar .navbar-nav > li > a.btn.btn-success.btn-simple .caret, .btn-success.btn-simple .caret {
  border-top-color: #FFFFFF;
}
.navbar .navbar-nav > li > a.btn.btn-success .caret, .btn-success .caret {
  border-top-color: #7AC29A;
}

.navbar .navbar-nav > li > a.btn.btn-info, .btn-info {
  border-color: #68B3C8;
  color: #68B3C8;
}
.navbar .navbar-nav > li > a.btn.btn-info:hover, .navbar .navbar-nav > li > a.btn.btn-info:focus, .navbar .navbar-nav > li > a.btn.btn-info:active, .navbar .navbar-nav > li > a.btn.btn-info.active, .navbar .navbar-nav > li > a.btn.btn-info:active:focus, .navbar .navbar-nav > li > a.btn.btn-info:active:hover, .open > .navbar .navbar-nav > li > a.btn.btn-info.dropdown-toggle, .open > .navbar .navbar-nav > li > a.btn.btn-info.dropdown-toggle:focus, .open > .navbar .navbar-nav > li > a.btn.btn-info.dropdown-toggle:hover, .btn-info:hover, .btn-info:focus, .btn-info:active, .btn-info.active, .btn-info:active:focus, .btn-info:active:hover, .open > .btn-info.dropdown-toggle, .open > .btn-info.dropdown-toggle:focus, .open > .btn-info.dropdown-toggle:hover {
  background-color: #68B3C8;
  color: rgba(255, 255, 255, 0.85);
  border-color: #68B3C8;
}
.navbar .navbar-nav > li > a.btn.btn-info:hover .caret, .navbar .navbar-nav > li > a.btn.btn-info:focus .caret, .navbar .navbar-nav > li > a.btn.btn-info:active .caret, .navbar .navbar-nav > li > a.btn.btn-info.active .caret, .navbar .navbar-nav > li > a.btn.btn-info:active:focus .caret, .navbar .navbar-nav > li > a.btn.btn-info:active:hover .caret, .open > .navbar .navbar-nav > li > a.btn.btn-info.dropdown-toggle .caret, .open > .navbar .navbar-nav > li > a.btn.btn-info.dropdown-toggle:focus .caret, .open > .navbar .navbar-nav > li > a.btn.btn-info.dropdown-toggle:hover .caret, .btn-info:hover .caret, .btn-info:focus .caret, .btn-info:active .caret, .btn-info.active .caret, .btn-info:active:focus .caret, .btn-info:active:hover .caret, .open > .btn-info.dropdown-toggle .caret, .open > .btn-info.dropdown-toggle:focus .caret, .open > .btn-info.dropdown-toggle:hover .caret {
  border-top-color: rgba(255, 255, 255, 0.85);
}
.navbar .navbar-nav > li > a.btn.btn-info.disabled, .navbar .navbar-nav > li > a.btn.btn-info.disabled:hover, .navbar .navbar-nav > li > a.btn.btn-info.disabled:focus, .navbar .navbar-nav > li > a.btn.btn-info.disabled.focus, .navbar .navbar-nav > li > a.btn.btn-info.disabled:active, .navbar .navbar-nav > li > a.btn.btn-info.disabled.active, .navbar .navbar-nav > li > a.btn.btn-info:disabled, .navbar .navbar-nav > li > a.btn.btn-info:disabled:hover, .navbar .navbar-nav > li > a.btn.btn-info:disabled:focus, .navbar .navbar-nav > li > a.btn.btn-info:disabled.focus, .navbar .navbar-nav > li > a.btn.btn-info:disabled:active, .navbar .navbar-nav > li > a.btn.btn-info:disabled.active, .navbar .navbar-nav > li > a.btn.btn-info[disabled], .navbar .navbar-nav > li > a.btn.btn-info[disabled]:hover, .navbar .navbar-nav > li > a.btn.btn-info[disabled]:focus, .navbar .navbar-nav > li > a.btn.btn-info[disabled].focus, .navbar .navbar-nav > li > a.btn.btn-info[disabled]:active, .navbar .navbar-nav > li > a.btn.btn-info[disabled].active, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-info, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-info:hover, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-info:focus, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-info.focus, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-info:active, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-info.active, .btn-info.disabled, .btn-info.disabled:hover, .btn-info.disabled:focus, .btn-info.disabled.focus, .btn-info.disabled:active, .btn-info.disabled.active, .btn-info:disabled, .btn-info:disabled:hover, .btn-info:disabled:focus, .btn-info:disabled.focus, .btn-info:disabled:active, .btn-info:disabled.active, .btn-info[disabled], .btn-info[disabled]:hover, .btn-info[disabled]:focus, .btn-info[disabled].focus, .btn-info[disabled]:active, .btn-info[disabled].active, fieldset[disabled] .btn-info, fieldset[disabled] .btn-info:hover, fieldset[disabled] .btn-info:focus, fieldset[disabled] .btn-info.focus, fieldset[disabled] .btn-info:active, fieldset[disabled] .btn-info.active {
  background-color: transparent;
  border-color: #68B3C8;
}
.navbar .navbar-nav > li > a.btn.btn-info.btn-fill, .btn-info.btn-fill {
  color: #FFFFFF;
  background-color: #68B3C8;
  opacity: 1;
  filter: alpha(opacity=100);
}
.navbar .navbar-nav > li > a.btn.btn-info.btn-fill:hover, .navbar .navbar-nav > li > a.btn.btn-info.btn-fill:focus, .navbar .navbar-nav > li > a.btn.btn-info.btn-fill:active, .navbar .navbar-nav > li > a.btn.btn-info.btn-fill.active, .open > .navbar .navbar-nav > li > a.btn.btn-info.btn-fill.dropdown-toggle, .btn-info.btn-fill:hover, .btn-info.btn-fill:focus, .btn-info.btn-fill:active, .btn-info.btn-fill.active, .open > .btn-info.btn-fill.dropdown-toggle {
  background-color: #429cb6;
  color: #FFFFFF;
  border-color: #429cb6;
}
.navbar .navbar-nav > li > a.btn.btn-info.btn-fill .caret, .btn-info.btn-fill .caret {
  border-top-color: #FFFFFF;
}
.navbar .navbar-nav > li > a.btn.btn-info.btn-simple:hover, .navbar .navbar-nav > li > a.btn.btn-info.btn-simple:focus, .navbar .navbar-nav > li > a.btn.btn-info.btn-simple:active, .navbar .navbar-nav > li > a.btn.btn-info.btn-simple.active, .open > .navbar .navbar-nav > li > a.btn.btn-info.btn-simple.dropdown-toggle, .btn-info.btn-simple:hover, .btn-info.btn-simple:focus, .btn-info.btn-simple:active, .btn-info.btn-simple.active, .open > .btn-info.btn-simple.dropdown-toggle {
  background-color: transparent;
  color: #429cb6;
}
.navbar .navbar-nav > li > a.btn.btn-info.btn-simple .caret, .btn-info.btn-simple .caret {
  border-top-color: #FFFFFF;
}
.navbar .navbar-nav > li > a.btn.btn-info .caret, .btn-info .caret {
  border-top-color: #68B3C8;
}

.navbar .navbar-nav > li > a.btn.btn-warning, .btn-warning {
  border-color: #F3BB45;
  color: #F3BB45;
}
.navbar .navbar-nav > li > a.btn.btn-warning:hover, .navbar .navbar-nav > li > a.btn.btn-warning:focus, .navbar .navbar-nav > li > a.btn.btn-warning:active, .navbar .navbar-nav > li > a.btn.btn-warning.active, .navbar .navbar-nav > li > a.btn.btn-warning:active:focus, .navbar .navbar-nav > li > a.btn.btn-warning:active:hover, .open > .navbar .navbar-nav > li > a.btn.btn-warning.dropdown-toggle, .open > .navbar .navbar-nav > li > a.btn.btn-warning.dropdown-toggle:focus, .open > .navbar .navbar-nav > li > a.btn.btn-warning.dropdown-toggle:hover, .btn-warning:hover, .btn-warning:focus, .btn-warning:active, .btn-warning.active, .btn-warning:active:focus, .btn-warning:active:hover, .open > .btn-warning.dropdown-toggle, .open > .btn-warning.dropdown-toggle:focus, .open > .btn-warning.dropdown-toggle:hover {
  background-color: #F3BB45;
  color: rgba(255, 255, 255, 0.85);
  border-color: #F3BB45;
}
.navbar .navbar-nav > li > a.btn.btn-warning:hover .caret, .navbar .navbar-nav > li > a.btn.btn-warning:focus .caret, .navbar .navbar-nav > li > a.btn.btn-warning:active .caret, .navbar .navbar-nav > li > a.btn.btn-warning.active .caret, .navbar .navbar-nav > li > a.btn.btn-warning:active:focus .caret, .navbar .navbar-nav > li > a.btn.btn-warning:active:hover .caret, .open > .navbar .navbar-nav > li > a.btn.btn-warning.dropdown-toggle .caret, .open > .navbar .navbar-nav > li > a.btn.btn-warning.dropdown-toggle:focus .caret, .open > .navbar .navbar-nav > li > a.btn.btn-warning.dropdown-toggle:hover .caret, .btn-warning:hover .caret, .btn-warning:focus .caret, .btn-warning:active .caret, .btn-warning.active .caret, .btn-warning:active:focus .caret, .btn-warning:active:hover .caret, .open > .btn-warning.dropdown-toggle .caret, .open > .btn-warning.dropdown-toggle:focus .caret, .open > .btn-warning.dropdown-toggle:hover .caret {
  border-top-color: rgba(255, 255, 255, 0.85);
}
.navbar .navbar-nav > li > a.btn.btn-warning.disabled, .navbar .navbar-nav > li > a.btn.btn-warning.disabled:hover, .navbar .navbar-nav > li > a.btn.btn-warning.disabled:focus, .navbar .navbar-nav > li > a.btn.btn-warning.disabled.focus, .navbar .navbar-nav > li > a.btn.btn-warning.disabled:active, .navbar .navbar-nav > li > a.btn.btn-warning.disabled.active, .navbar .navbar-nav > li > a.btn.btn-warning:disabled, .navbar .navbar-nav > li > a.btn.btn-warning:disabled:hover, .navbar .navbar-nav > li > a.btn.btn-warning:disabled:focus, .navbar .navbar-nav > li > a.btn.btn-warning:disabled.focus, .navbar .navbar-nav > li > a.btn.btn-warning:disabled:active, .navbar .navbar-nav > li > a.btn.btn-warning:disabled.active, .navbar .navbar-nav > li > a.btn.btn-warning[disabled], .navbar .navbar-nav > li > a.btn.btn-warning[disabled]:hover, .navbar .navbar-nav > li > a.btn.btn-warning[disabled]:focus, .navbar .navbar-nav > li > a.btn.btn-warning[disabled].focus, .navbar .navbar-nav > li > a.btn.btn-warning[disabled]:active, .navbar .navbar-nav > li > a.btn.btn-warning[disabled].active, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-warning, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-warning:hover, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-warning:focus, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-warning.focus, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-warning:active, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-warning.active, .btn-warning.disabled, .btn-warning.disabled:hover, .btn-warning.disabled:focus, .btn-warning.disabled.focus, .btn-warning.disabled:active, .btn-warning.disabled.active, .btn-warning:disabled, .btn-warning:disabled:hover, .btn-warning:disabled:focus, .btn-warning:disabled.focus, .btn-warning:disabled:active, .btn-warning:disabled.active, .btn-warning[disabled], .btn-warning[disabled]:hover, .btn-warning[disabled]:focus, .btn-warning[disabled].focus, .btn-warning[disabled]:active, .btn-warning[disabled].active, fieldset[disabled] .btn-warning, fieldset[disabled] .btn-warning:hover, fieldset[disabled] .btn-warning:focus, fieldset[disabled] .btn-warning.focus, fieldset[disabled] .btn-warning:active, fieldset[disabled] .btn-warning.active {
  background-color: transparent;
  border-color: #F3BB45;
}
.navbar .navbar-nav > li > a.btn.btn-warning.btn-fill, .btn-warning.btn-fill {
  color: #FFFFFF;
  background-color: #F3BB45;
  opacity: 1;
  filter: alpha(opacity=100);
}
.navbar .navbar-nav > li > a.btn.btn-warning.btn-fill:hover, .navbar .navbar-nav > li > a.btn.btn-warning.btn-fill:focus, .navbar .navbar-nav > li > a.btn.btn-warning.btn-fill:active, .navbar .navbar-nav > li > a.btn.btn-warning.btn-fill.active, .open > .navbar .navbar-nav > li > a.btn.btn-warning.btn-fill.dropdown-toggle, .btn-warning.btn-fill:hover, .btn-warning.btn-fill:focus, .btn-warning.btn-fill:active, .btn-warning.btn-fill.active, .open > .btn-warning.btn-fill.dropdown-toggle {
  background-color: #f0a810;
  color: #FFFFFF;
  border-color: #f0a810;
}
.navbar .navbar-nav > li > a.btn.btn-warning.btn-fill .caret, .btn-warning.btn-fill .caret {
  border-top-color: #FFFFFF;
}
.navbar .navbar-nav > li > a.btn.btn-warning.btn-simple:hover, .navbar .navbar-nav > li > a.btn.btn-warning.btn-simple:focus, .navbar .navbar-nav > li > a.btn.btn-warning.btn-simple:active, .navbar .navbar-nav > li > a.btn.btn-warning.btn-simple.active, .open > .navbar .navbar-nav > li > a.btn.btn-warning.btn-simple.dropdown-toggle, .btn-warning.btn-simple:hover, .btn-warning.btn-simple:focus, .btn-warning.btn-simple:active, .btn-warning.btn-simple.active, .open > .btn-warning.btn-simple.dropdown-toggle {
  background-color: transparent;
  color: #f0a810;
}
.navbar .navbar-nav > li > a.btn.btn-warning.btn-simple .caret, .btn-warning.btn-simple .caret {
  border-top-color: #FFFFFF;
}
.navbar .navbar-nav > li > a.btn.btn-warning .caret, .btn-warning .caret {
  border-top-color: #F3BB45;
}

.navbar .navbar-nav > li > a.btn.btn-danger, .btn-danger {
  border-color: #EB5E28;
  color: #EB5E28;
}
.navbar .navbar-nav > li > a.btn.btn-danger:hover, .navbar .navbar-nav > li > a.btn.btn-danger:focus, .navbar .navbar-nav > li > a.btn.btn-danger:active, .navbar .navbar-nav > li > a.btn.btn-danger.active, .navbar .navbar-nav > li > a.btn.btn-danger:active:focus, .navbar .navbar-nav > li > a.btn.btn-danger:active:hover, .open > .navbar .navbar-nav > li > a.btn.btn-danger.dropdown-toggle, .open > .navbar .navbar-nav > li > a.btn.btn-danger.dropdown-toggle:focus, .open > .navbar .navbar-nav > li > a.btn.btn-danger.dropdown-toggle:hover, .btn-danger:hover, .btn-danger:focus, .btn-danger:active, .btn-danger.active, .btn-danger:active:focus, .btn-danger:active:hover, .open > .btn-danger.dropdown-toggle, .open > .btn-danger.dropdown-toggle:focus, .open > .btn-danger.dropdown-toggle:hover {
  background-color: #EB5E28;
  color: rgba(255, 255, 255, 0.85);
  border-color: #EB5E28;
}
.navbar .navbar-nav > li > a.btn.btn-danger:hover .caret, .navbar .navbar-nav > li > a.btn.btn-danger:focus .caret, .navbar .navbar-nav > li > a.btn.btn-danger:active .caret, .navbar .navbar-nav > li > a.btn.btn-danger.active .caret, .navbar .navbar-nav > li > a.btn.btn-danger:active:focus .caret, .navbar .navbar-nav > li > a.btn.btn-danger:active:hover .caret, .open > .navbar .navbar-nav > li > a.btn.btn-danger.dropdown-toggle .caret, .open > .navbar .navbar-nav > li > a.btn.btn-danger.dropdown-toggle:focus .caret, .open > .navbar .navbar-nav > li > a.btn.btn-danger.dropdown-toggle:hover .caret, .btn-danger:hover .caret, .btn-danger:focus .caret, .btn-danger:active .caret, .btn-danger.active .caret, .btn-danger:active:focus .caret, .btn-danger:active:hover .caret, .open > .btn-danger.dropdown-toggle .caret, .open > .btn-danger.dropdown-toggle:focus .caret, .open > .btn-danger.dropdown-toggle:hover .caret {
  border-top-color: rgba(255, 255, 255, 0.85);
}
.navbar .navbar-nav > li > a.btn.btn-danger.disabled, .navbar .navbar-nav > li > a.btn.btn-danger.disabled:hover, .navbar .navbar-nav > li > a.btn.btn-danger.disabled:focus, .navbar .navbar-nav > li > a.btn.btn-danger.disabled.focus, .navbar .navbar-nav > li > a.btn.btn-danger.disabled:active, .navbar .navbar-nav > li > a.btn.btn-danger.disabled.active, .navbar .navbar-nav > li > a.btn.btn-danger:disabled, .navbar .navbar-nav > li > a.btn.btn-danger:disabled:hover, .navbar .navbar-nav > li > a.btn.btn-danger:disabled:focus, .navbar .navbar-nav > li > a.btn.btn-danger:disabled.focus, .navbar .navbar-nav > li > a.btn.btn-danger:disabled:active, .navbar .navbar-nav > li > a.btn.btn-danger:disabled.active, .navbar .navbar-nav > li > a.btn.btn-danger[disabled], .navbar .navbar-nav > li > a.btn.btn-danger[disabled]:hover, .navbar .navbar-nav > li > a.btn.btn-danger[disabled]:focus, .navbar .navbar-nav > li > a.btn.btn-danger[disabled].focus, .navbar .navbar-nav > li > a.btn.btn-danger[disabled]:active, .navbar .navbar-nav > li > a.btn.btn-danger[disabled].active, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-danger, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-danger:hover, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-danger:focus, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-danger.focus, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-danger:active, fieldset[disabled] .navbar .navbar-nav > li > a.btn.btn-danger.active, .btn-danger.disabled, .btn-danger.disabled:hover, .btn-danger.disabled:focus, .btn-danger.disabled.focus, .btn-danger.disabled:active, .btn-danger.disabled.active, .btn-danger:disabled, .btn-danger:disabled:hover, .btn-danger:disabled:focus, .btn-danger:disabled.focus, .btn-danger:disabled:active, .btn-danger:disabled.active, .btn-danger[disabled], .btn-danger[disabled]:hover, .btn-danger[disabled]:focus, .btn-danger[disabled].focus, .btn-danger[disabled]:active, .btn-danger[disabled].active, fieldset[disabled] .btn-danger, fieldset[disabled] .btn-danger:hover, fieldset[disabled] .btn-danger:focus, fieldset[disabled] .btn-danger.focus, fieldset[disabled] .btn-danger:active, fieldset[disabled] .btn-danger.active {
  background-color: transparent;
  border-color: #EB5E28;
}
.navbar .navbar-nav > li > a.btn.btn-danger.btn-fill, .btn-danger.btn-fill {
  color: #FFFFFF;
  background-color: #EB5E28;
  opacity: 1;
  filter: alpha(opacity=100);
}
.navbar .navbar-nav > li > a.btn.btn-danger.btn-fill:hover, .navbar .navbar-nav > li > a.btn.btn-danger.btn-fill:focus, .navbar .navbar-nav > li > a.btn.btn-danger.btn-fill:active, .navbar .navbar-nav > li > a.btn.btn-danger.btn-fill.active, .open > .navbar .navbar-nav > li > a.btn.btn-danger.btn-fill.dropdown-toggle, .btn-danger.btn-fill:hover, .btn-danger.btn-fill:focus, .btn-danger.btn-fill:active, .btn-danger.btn-fill.active, .open > .btn-danger.btn-fill.dropdown-toggle {
  background-color: #c84513;
  color: #FFFFFF;
  border-color: #c84513;
}
.navbar .navbar-nav > li > a.btn.btn-danger.btn-fill .caret, .btn-danger.btn-fill .caret {
  border-top-color: #FFFFFF;
}
.navbar .navbar-nav > li > a.btn.btn-danger.btn-simple:hover, .navbar .navbar-nav > li > a.btn.btn-danger.btn-simple:focus, .navbar .navbar-nav > li > a.btn.btn-danger.btn-simple:active, .navbar .navbar-nav > li > a.btn.btn-danger.btn-simple.active, .open > .navbar .navbar-nav > li > a.btn.btn-danger.btn-simple.dropdown-toggle, .btn-danger.btn-simple:hover, .btn-danger.btn-simple:focus, .btn-danger.btn-simple:active, .btn-danger.btn-simple.active, .open > .btn-danger.btn-simple.dropdown-toggle {
  background-color: transparent;
  color: #c84513;
}
.navbar .navbar-nav > li > a.btn.btn-danger.btn-simple .caret, .btn-danger.btn-simple .caret {
  border-top-color: #FFFFFF;
}
.navbar .navbar-nav > li > a.btn.btn-danger .caret, .btn-danger .caret {
  border-top-color: #EB5E28;
}

.btn-neutral {
  border-color: #FFFFFF;
  color: #FFFFFF;
}
.btn-neutral:hover, .btn-neutral:focus, .btn-neutral:active, .btn-neutral.active, .btn-neutral:active:focus, .btn-neutral:active:hover, .open > .btn-neutral.dropdown-toggle, .open > .btn-neutral.dropdown-toggle:focus, .open > .btn-neutral.dropdown-toggle:hover {
  background-color: #FFFFFF;
  color: rgba(255, 255, 255, 0.85);
  border-color: #FFFFFF;
}
.btn-neutral:hover .caret, .btn-neutral:focus .caret, .btn-neutral:active .caret, .btn-neutral.active .caret, .btn-neutral:active:focus .caret, .btn-neutral:active:hover .caret, .open > .btn-neutral.dropdown-toggle .caret, .open > .btn-neutral.dropdown-toggle:focus .caret, .open > .btn-neutral.dropdown-toggle:hover .caret {
  border-top-color: rgba(255, 255, 255, 0.85);
}
.btn-neutral.disabled, .btn-neutral.disabled:hover, .btn-neutral.disabled:focus, .btn-neutral.disabled.focus, .btn-neutral.disabled:active, .btn-neutral.disabled.active, .btn-neutral:disabled, .btn-neutral:disabled:hover, .btn-neutral:disabled:focus, .btn-neutral:disabled.focus, .btn-neutral:disabled:active, .btn-neutral:disabled.active, .btn-neutral[disabled], .btn-neutral[disabled]:hover, .btn-neutral[disabled]:focus, .btn-neutral[disabled].focus, .btn-neutral[disabled]:active, .btn-neutral[disabled].active, fieldset[disabled] .btn-neutral, fieldset[disabled] .btn-neutral:hover, fieldset[disabled] .btn-neutral:focus, fieldset[disabled] .btn-neutral.focus, fieldset[disabled] .btn-neutral:active, fieldset[disabled] .btn-neutral.active {
  background-color: transparent;
  border-color: #FFFFFF;
}
.btn-neutral.btn-fill {
  color: #FFFFFF;
  background-color: #FFFFFF;
  opacity: 1;
  filter: alpha(opacity=100);
}
.btn-neutral.btn-fill:hover, .btn-neutral.btn-fill:focus, .btn-neutral.btn-fill:active, .btn-neutral.btn-fill.active, .open > .btn-neutral.btn-fill.dropdown-toggle {
  background-color: #FFFFFF;
  color: #FFFFFF;
  border-color: #FFFFFF;
}
.btn-neutral.btn-fill .caret {
  border-top-color: #FFFFFF;
}
.btn-neutral.btn-simple:hover, .btn-neutral.btn-simple:focus, .btn-neutral.btn-simple:active, .btn-neutral.btn-simple.active, .open > .btn-neutral.btn-simple.dropdown-toggle {
  background-color: transparent;
  color: #FFFFFF;
}
.btn-neutral.btn-simple .caret {
  border-top-color: #FFFFFF;
}
.btn-neutral .caret {
  border-top-color: #FFFFFF;
}
.btn-neutral:hover, .btn-neutral:focus {
  color: #66615B;
}
.btn-neutral:hover i, .btn-neutral:focus i {
  color: #66615B;
  opacity: 1;
}
.btn-neutral:active, .btn-neutral.active, .open > .btn-neutral.dropdown-toggle {
  background-color: #FFFFFF;
  color: #66615B;
}
.btn-neutral:active i, .btn-neutral.active i, .open > .btn-neutral.dropdown-toggle i {
  color: #66615B;
  opacity: 1;
}
.btn-neutral.btn-fill {
  color: #66615B;
}
.btn-neutral.btn-fill i {
  color: #66615B;
  opacity: 1;
}
.btn-neutral.btn-fill:hover, .btn-neutral.btn-fill:focus {
  color: #484541;
}
.btn-neutral.btn-fill:hover i, .btn-neutral.btn-fill:focus i {
  color: #484541;
  opacity: 1;
}
.btn-neutral.btn-simple:active, .btn-neutral.btn-simple.active {
  background-color: transparent;
}

.btn:disabled, .btn[disabled], .btn.disabled, .btn.btn-disabled {
  opacity: 0.5;
  filter: alpha(opacity=50);
}

.btn-disabled {
  cursor: default;
}

.btn-simple {
  border: 0;
  padding: 7px 18px;
}

.navbar .navbar-nav > li > a.btn.btn-lg,
.btn-lg {
  font-size: 18px;
  border-radius: 50px;
  padding: 11px 30px;
  font-weight: 400;
}
.navbar .navbar-nav > li > a.btn.btn-lg.btn-simple,
.btn-lg.btn-simple {
  padding: 13px 30px;
}
.navbar .navbar-nav > li > a.btn.btn-lg.btn-icon,
.btn-lg.btn-icon {
  border-radius: 30px;
  padding: 9px 16px;
}

.navbar .navbar-nav > li > a.btn.btn-sm,
.btn-sm {
  font-size: 12px;
  border-radius: 26px;
  padding: 4px 10px;
}
.navbar .navbar-nav > li > a.btn.btn-sm.btn-simple,
.btn-sm.btn-simple {
  padding: 6px 10px;
}
.navbar .navbar-nav > li > a.btn.btn-sm.btn-icon,
.btn-sm.btn-icon {
  padding: 3px 6px;
}
.navbar .navbar-nav > li > a.btn.btn-sm.btn-icon .fa,
.btn-sm.btn-icon .fa {
  line-height: 1.6;
  width: 15px;
}

.navbar .navbar-nav > li > a.btn.btn-xs,
.btn-xs {
  font-size: 12px;
  border-radius: 26px;
  padding: 2px 5px;
}
.navbar .navbar-nav > li > a.btn.btn-xs.btn-simple,
.btn-xs.btn-simple {
  padding: 4px 5px;
}
.navbar .navbar-nav > li > a.btn.btn-xs.btn-icon,
.btn-xs.btn-icon {
  padding: 1px 5px;
}
.navbar .navbar-nav > li > a.btn.btn-xs.btn-icon .fa,
.btn-xs.btn-icon .fa {
  width: 10px;
}

.navbar .navbar-nav > li > a.btn.btn-wd,
.btn-wd {
  min-width: 140px;
}

.btn-group.select {
  width: 100%;
}

.btn-group.select .btn {
  text-align: left;
}

.btn-group.select .caret {
  position: absolute;
  top: 50%;
  margin-top: -1px;
  right: 8px;
}

.btn-tooltip {
  white-space: nowrap;
}

.buttons-with-margin .btn {
  margin-bottom: 5px;
}

/*     General overwrite     */
body {
  color: #66615b;
  font-size: 14px;
  font-family: 'Ubuntu', Arial, sans-serif;
}
body .wrapper {
  min-height: 100vh;
  position: relative;
}
body .wrapper:after {
  display: table;
  content: " ";
  clear: both;
}

a {
  color: #68B3C8;
}
a:hover, a:focus {
  color: #429cb6;
  text-decoration: none;
}

a:focus, a:active,
button::-moz-focus-inner,
input::-moz-focus-inner,
select::-moz-focus-inner,
input[type="file"] > input[type="button"]::-moz-focus-inner {
  outline: 0 !important;
}

.ui-slider-handle:focus,
.navbar-toggle,
input:focus,
button:focus {
  outline: 0 !important;
}

/*           Animations              */
.form-control,
.input-group-addon,
.tagsinput,
.navbar,
.navbar .alert,
.general-animation {
  -webkit-transition: all 300ms linear;
  -moz-transition: all 300ms linear;
  -o-transition: all 300ms linear;
  -ms-transition: all 300ms linear;
  transition: all 300ms linear;
}

.bootstrap-tagsinput .tag,
.bootstrap-tagsinput [data-role="remove"],
.filter,
.btn-hover,
[data-toggle="collapse"] i {
  -webkit-transition: all 150ms linear;
  -moz-transition: all 150ms linear;
  -o-transition: all 150ms linear;
  -ms-transition: all 150ms linear;
  transition: all 150ms linear;
}

.sidebar .nav a,
.sidebar .nav .collapse i,
.sidebar .nav .collapse a > span,
.sidebar .sidebar-wrapper > .nav p,
.sidebar .user .info > a > span,
.sidebar .user .info .collapse .nav a > span,
.sidebar .logo a.logo-mini,
.sidebar .logo a.logo-normal,
.table > tbody > tr .td-actions .btn,
.caret {
  -webkit-transition: all 150ms ease-in;
  -moz-transition: all 150ms ease-in;
  -o-transition: all 150ms ease-in;
  -ms-transition: all 150ms ease-in;
  transition: all 150ms ease-in;
}

.btn,
.pagination a {
  -webkit-transition: all 100ms ease-in;
  -moz-transition: all 100ms ease-in;
  -o-transition: all 100ms ease-in;
  -ms-transition: all 100ms ease-in;
  transition: all 100ms ease-in;
}

.fa {
  text-align: center;
}

.fa-base {
  font-size: 1.25em !important;
}

.margin-top {
  margin-top: 50px;
}

hr {
  border-color: #F1EAE0;
}

.animation-transition-general {
  -webkit-transition: all 300ms linear;
  -moz-transition: all 300ms linear;
  -o-transition: all 300ms linear;
  -ms-transition: all 300ms linear;
  transition: all 300ms linear;
}

.animation-transition-fast, .bootstrap-datetimepicker-widget table td > div, .bootstrap-datetimepicker-widget table th > div, .bootstrap-datetimepicker-widget table th, .bootstrap-datetimepicker-widget table td span {
  -webkit-transition: all 150ms linear;
  -moz-transition: all 150ms linear;
  -o-transition: all 150ms linear;
  -ms-transition: all 150ms linear;
  transition: all 150ms linear;
}

.animation-transition-ultra-fast {
  -webkit-transition: all 100ms ease-in;
  -moz-transition: all 100ms ease-in;
  -o-transition: all 100ms ease-in;
  -ms-transition: all 100ms ease-in;
  transition: all 100ms ease-in;
}

a[data-toggle="collapse"][aria-expanded="true"] .caret,
.btn[data-toggle="collapse"][aria-expanded="true"] .caret,
a.dropdown-toggle[aria-expanded="true"] .caret {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

.section-gray {
  background-color: #EEEEEE;
}

.section-white {
  background-color: #FFFFFF;
}

.form-control::-moz-placeholder {
  color: #cfcfca;
  opacity: 1;
  filter: alpha(opacity=100);
}

.form-control:-moz-placeholder {
  color: #cfcfca;
  opacity: 1;
  filter: alpha(opacity=100);
}

.form-control::-webkit-input-placeholder {
  color: #cfcfca;
  opacity: 1;
  filter: alpha(opacity=100);
}

.form-control:-ms-input-placeholder {
  color: #cfcfca;
  opacity: 1;
  filter: alpha(opacity=100);
}

.form-control {
  background-color: #F3F2EE;
  border: 1px solid #e8e7e3;
  border-radius: 4px;
  color: #66615b;
  font-size: 14px;
  padding: 7px 18px;
  height: 40px;
  -webkit-box-shadow: none;
  box-shadow: none;
}
.form-control:focus {
  border: 1px solid #e8e7e3;
  background-color: #FFFFFF;
  -webkit-box-shadow: none;
  box-shadow: none;
  outline: 0 !important;
}
.has-success .form-control, .has-error .form-control, .has-success .form-control:focus, .has-error .form-control:focus {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.has-success .form-control, .form-control.valid {
  color: #66615b;
  border-color: #e8e7e3;
}
.has-success .form-control:focus, .form-control.valid:focus {
  background-color: #FFFFFF;
  border-color: #7AC29A;
}
.has-error .form-control, .form-control.error {
  background-color: #FFC0A4;
  color: #EB5E28;
  border-color: #EB5E28;
}
.has-error .form-control:focus, .form-control.error:focus {
  background-color: #FFFFFF;
  border-color: #EB5E28;
}
.form-control + .form-control-feedback {
  border-radius: 6px;
  font-size: 14px;
  margin-top: -7px;
  position: absolute;
  right: 10px;
  top: 50%;
  vertical-align: middle;
}
.open .form-control {
  border-bottom-color: transparent;
}
.form-control.input-no-border {
  border: 0 none;
}
.input-group .form-control:not(:first-child):not(:last-child) {
  border-left: 0;
  border-right: 0;
}

.input-lg {
  height: 55px;
  padding: 11px 30px;
}

.has-error .form-control-feedback, .has-error .control-label {
  color: #EB5E28;
}

.has-success .form-control-feedback, .has-success .control-label {
  color: #7AC29A;
}

.input-group-addon {
  background-color: #F3F2EE;
  border: 1px solid #e8e7e3;
  border-radius: 4px;
}
.has-success .input-group-addon, .has-error .input-group-addon {
  background-color: #FFFFFF;
}
.has-error .form-control:focus + .input-group-addon {
  color: #EB5E28;
}
.has-success .form-control:focus + .input-group-addon {
  color: #7AC29A;
}
.form-control:focus + .input-group-addon, .form-control:focus ~ .input-group-addon {
  background-color: #FFFFFF;
}
.has-error .input-group-addon {
  color: #EB5E28;
  border-color: #EB5E28;
}
.has-error .input-group-addon {
  color: #7AC29A;
  border-color: #7AC29A;
}
.input-group-addon + .form-control {
  padding-left: 0;
}

.input-group {
  margin-bottom: 15px;
}

.input-group[disabled] .input-group-addon {
  background-color: #E3E3E3;
}

.input-group .form-control:first-child,
.input-group-addon:first-child,
.input-group-btn:first-child > .dropdown-toggle,
.input-group-btn:last-child > .btn:not(:last-child):not(.dropdown-toggle) {
  border-right: 0 none;
}

.input-group .form-control:last-child,
.input-group-addon:last-child,
.input-group-btn:last-child > .dropdown-toggle,
.input-group-btn:first-child > .btn:not(:first-child) {
  border-left: 0 none;
}

.form-control[disabled], .form-control[readonly], fieldset[disabled] .form-control {
  background-color: #E3E3E3;
  cursor: not-allowed;
  color: #9A9A9A;
  opacity: 1;
  filter: alpha(opacity=100);
}

.form-control[disabled]::-moz-placeholder {
  color: #9A9A9A;
  opacity: 1;
  filter: alpha(opacity=100);
}

.form-control[disabled]:-moz-placeholder {
  color: #cfcfca;
  opacity: 1;
  filter: alpha(opacity=100);
}

.form-control[disabled]::-webkit-input-placeholder {
  color: #cfcfca;
  opacity: 1;
  filter: alpha(opacity=100);
}

.form-control[disabled]:-ms-input-placeholder {
  color: #cfcfca;
  opacity: 1;
  filter: alpha(opacity=100);
}

.input-group-btn .btn {
  border-width: 1px;
  padding: 9px 18px;
}

.input-group-btn .btn-default:not(.btn-fill) {
  border-color: #cfcfca;
}

.input-group-btn:last-child > .btn {
  margin-left: 0;
}

textarea.form-control {
  max-width: 100%;
  padding: 10px 18px;
  resize: none;
}

.input-group-focus .input-group-addon {
  background-color: #FFFFFF;
}

.progress {
  background-color: #cfcfca;
  border-radius: 3px;
  box-shadow: none;
  height: 8px;
}

.progress-thin {
  height: 4px;
}

.progress-bar {
  background-color: #7A9E9F;
}

.progress-bar-primary {
  background-color: #7A9E9F;
}

.progress-bar-info {
  background-color: #68B3C8;
}

.progress-bar-success {
  background-color: #7AC29A;
}

.progress-bar-warning {
  background-color: #F3BB45;
}

.progress-bar-danger {
  background-color: #EB5E28;
}

/*!
 * jQuery UI Slider 1.10.4
 * http://jqueryui.com
 *
 * Copyright 2014 jQuery Foundation and other contributors
 * Released under the MIT license.
 * http://jquery.org/license
 *
 * http://api.jqueryui.com/slider/#theming
 */
.ui-slider {
  border-radius: 3px;
  position: relative;
  text-align: left;
}

.ui-slider .ui-slider-handle {
  position: absolute;
  z-index: 2;
  width: 1.2em;
  height: 1.2em;
  cursor: default;
  transition: none;
  -webkit-transition: none;
}

.ui-slider .ui-slider-range {
  background-position: 0 0;
  border: 0;
  border-radius: 3px;
  display: block;
  font-size: .7em;
  position: absolute;
  z-index: 1;
}

/* For IE8 - See #6727 */
.ui-slider.ui-state-disabled .ui-slider-handle,
.ui-slider.ui-state-disabled .ui-slider-range {
  filter: inherit;
}

.ui-slider-horizontal {
  height: 8px;
}

.ui-slider-horizontal .ui-slider-handle {
  margin-left: -10px;
  top: -4px;
}

.ui-slider-horizontal .ui-slider-range {
  top: 0;
  height: 100%;
}

.ui-slider-horizontal .ui-slider-range-min {
  left: 0;
}

.ui-slider-horizontal .ui-slider-range-max {
  right: 0;
}

.ui-slider-vertical {
  width: .8em;
  height: 100px;
}

.ui-slider-vertical .ui-slider-handle {
  left: -.3em;
  margin-left: 0;
  margin-bottom: -.6em;
}

.ui-slider-vertical .ui-slider-range {
  left: 0;
  width: 100%;
}

.ui-slider-vertical .ui-slider-range-min {
  bottom: 0;
}

.ui-slider-vertical .ui-slider-range-max {
  top: 0;
}

/* Component containers
----------------------------------*/
.ui-widget {
  font-size: 1.1em;
}

.ui-widget .ui-widget {
  font-size: 1em;
}

.ui-widget input,
.ui-widget select,
.ui-widget textarea,
.ui-widget button {
  font-size: 1em;
}

.ui-widget-content {
  background-color: #E5E5E5;
}

.ui-widget-content a {
  color: #222222;
}

.ui-widget-header {
  background: #cfcfca;
  color: #222222;
  font-weight: bold;
}

.ui-widget-header a {
  color: #222222;
}

.slider-primary .ui-widget-header {
  background-color: #7A9E9F;
}

.slider-info .ui-widget-header {
  background-color: #68B3C8;
}

.slider-success .ui-widget-header {
  background-color: #7AC29A;
}

.slider-warning .ui-widget-header {
  background-color: #F3BB45;
}

.slider-danger .ui-widget-header {
  background-color: #EB5E28;
}

/* Interaction states
----------------------------------*/
.ui-state-default,
.ui-widget-content .ui-state-default,
.ui-widget-header .ui-state-default {
  background: white;
  /* Old browsers */
  background: -moz-linear-gradient(top, white 0%, #f1f1f2 100%);
  /* FF3.6+ */
  background: -webkit-gradient(linear, left top, left bottom, color-stop(0%, white), color-stop(100%, #f1f1f2));
  /* Chrome,Safari4+ */
  background: -webkit-linear-gradient(top, white 0%, #f1f1f2 100%);
  /* Chrome10+,Safari5.1+ */
  background: -o-linear-gradient(top, white 0%, #f1f1f2 100%);
  /* Opera 11.10+ */
  background: -ms-linear-gradient(top, white 0%, #f1f1f2 100%);
  /* IE10+ */
  background: linear-gradient(to bottom, white 0%, #f1f1f2 100%);
  /* W3C */
  filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='$white-color', endColorstr='#f1f1f2',GradientType=0 );
  /* IE6-9 */
  border-radius: 50%;
  box-shadow: 0 1px 1px #FFFFFF inset, 0 1px 2px rgba(0, 0, 0, 0.4);
  height: 15px;
  width: 15px;
  cursor: pointer;
}

.ui-state-default a,
.ui-state-default a:link,
.ui-state-default a:visited {
  color: #555555;
  text-decoration: none;
}

.ui-state-hover a,
.ui-state-hover a:hover,
.ui-state-hover a:link,
.ui-state-hover a:visited,
.ui-state-focus a,
.ui-state-focus a:hover,
.ui-state-focus a:link,
.ui-state-focus a:visited {
  color: #212121;
  text-decoration: none;
}

.ui-state-active a,
.ui-state-active a:link,
.ui-state-active a:visited {
  color: #212121;
  text-decoration: none;
}

/* Interaction Cues
----------------------------------*/
.ui-state-highlight,
.ui-widget-content .ui-state-highlight,
.ui-widget-header .ui-state-highlight {
  border: 1px solid #fcefa1;
  background: #fbf9ee;
  color: #363636;
}

.ui-state-highlight a,
.ui-widget-content .ui-state-highlight a,
.ui-widget-header .ui-state-highlight a {
  color: #363636;
}

.ui-state-error,
.ui-widget-content .ui-state-error,
.ui-widget-header .ui-state-error {
  border: 1px solid #EB5E28;
  background-color: #EB5E28;
  color: #EB5E28;
}

.ui-state-error a,
.ui-widget-content .ui-state-error a,
.ui-widget-header .ui-state-error a {
  color: #EB5E28;
}

.ui-state-error-text,
.ui-widget-content .ui-state-error-text,
.ui-widget-header .ui-state-error-text {
  color: #EB5E28;
}

.ui-priority-primary,
.ui-widget-content .ui-priority-primary,
.ui-widget-header .ui-priority-primary {
  font-weight: bold;
}

.ui-priority-secondary,
.ui-widget-content .ui-priority-secondary,
.ui-widget-header .ui-priority-secondary {
  opacity: .7;
  filter: Alpha(Opacity=70);
  font-weight: normal;
}

.ui-state-disabled,
.ui-widget-content .ui-state-disabled,
.ui-widget-header .ui-state-disabled {
  opacity: .35;
  filter: Alpha(Opacity=35);
  background-image: none;
}

.ui-state-disabled .ui-icon {
  filter: Alpha(Opacity=35);
  /* For IE8 - See #6059 */
}

form label.radio, form label.checkbox {
  font-size: 14px;
  text-transform: none;
  cursor: pointer;
}

.form-group {
  position: relative;
}

.form-horizontal .checkbox,
.form-horizontal .radio {
  padding-top: 0;
}
.form-horizontal .checkbox:first-child, .form-horizontal .checkbox.checkbox-inline, .form-horizontal .checkbox.radio-inline,
.form-horizontal .radio:first-child,
.form-horizontal .radio.checkbox-inline,
.form-horizontal .radio.radio-inline {
  margin-top: 13px;
}

star {
  color: #EB5E28;
  padding-left: 3px;
}

@media (min-width: 992px) {
  .form-horizontal .control-label {
    padding-top: 12px !important;
  }
  .form-horizontal code {
    margin-top: 8px;
    display: inline-block;
  }
}
.alert {
  border: 0;
  border-radius: 4px;
  color: #FFFFFF;
  padding: 12px 15px;
  font-size: 14px;
  position: relative;
}
.container .alert {
  border-radius: 4px;
}
.navbar .alert {
  border-radius: 0;
  left: 0;
  position: absolute;
  right: 0;
  top: 85px;
  width: 100%;
  z-index: 3;
}
.navbar:not(.navbar-transparent) .alert {
  top: 70px;
}
.alert span[data-notify="icon"] {
  font-size: 30px;
  display: block;
  left: 15px;
  position: absolute;
  top: 50%;
  margin-top: -20px;
  line-height: 1.42857;
}
.alert .close ~ span {
  display: block;
  max-width: 89%;
}
.alert.alert-with-icon {
  padding-left: 65px;
}

.alert-info {
  background-color: #7CE4FE;
  color: #429cb6;
}

.alert-success {
  background-color: #8EF3C5;
  color: #54b07d;
}

.alert-warning {
  background-color: #FFE28C;
  color: #f0a810;
}

.alert-danger {
  background-color: #FF8F5E;
  color: #c84513;
}

.table {
  border-collapse: inherit;
}
.table thead tr > th,
.table thead tr > td,
.table tbody tr > th,
.table tbody tr > td,
.table tfoot tr > th,
.table tfoot tr > td {
  border-top: 1px solid #CCC5B9;
}
.table thead tr.success th, .table thead tr.success td,
.table tbody tr.success th,
.table tbody tr.success td,
.table tfoot tr.success th,
.table tfoot tr.success td {
  background-color: #bcf8dd;
}
.table thead tr.success:hover th, .table thead tr.success:hover td,
.table tbody tr.success:hover th,
.table tbody tr.success:hover td,
.table tfoot tr.success:hover th,
.table tfoot tr.success:hover td {
  background-color: #a5f5d1;
}
.table thead tr.info th, .table thead tr.info td,
.table tbody tr.info th,
.table tbody tr.info td,
.table tfoot tr.info th,
.table tfoot tr.info td {
  background-color: #afeefe;
}
.table thead tr.info:hover th, .table thead tr.info:hover td,
.table tbody tr.info:hover th,
.table tbody tr.info:hover td,
.table tfoot tr.info:hover th,
.table tfoot tr.info:hover td {
  background-color: #95e9fe;
}
.table thead tr.warning th, .table thead tr.warning td,
.table tbody tr.warning th,
.table tbody tr.warning td,
.table tfoot tr.warning th,
.table tfoot tr.warning td {
  background-color: #ffefbf;
}
.table thead tr.warning:hover th, .table thead tr.warning:hover td,
.table tbody tr.warning:hover th,
.table tbody tr.warning:hover td,
.table tfoot tr.warning:hover th,
.table tfoot tr.warning:hover td {
  background-color: #ffe8a6;
}
.table thead tr.danger th, .table thead tr.danger td,
.table tbody tr.danger th,
.table tbody tr.danger td,
.table tfoot tr.danger th,
.table tfoot tr.danger td {
  background-color: #ffb291;
}
.table thead tr.danger:hover th, .table thead tr.danger:hover td,
.table tbody tr.danger:hover th,
.table tbody tr.danger:hover td,
.table tfoot tr.danger:hover th,
.table tfoot tr.danger:hover td {
  background-color: #ffa178;
}
.table > thead > tr > th {
  border-bottom-width: 0;
  font-size: 1.25em;
  font-weight: 300;
}
.table .radio,
.table .checkbox {
  margin-top: 0;
  margin-bottom: 22px;
  padding: 0;
  width: 15px;
}
.table > thead > tr > th,
.table > tbody > tr > th,
.table > tfoot > tr > th,
.table > thead > tr > td,
.table > tbody > tr > td,
.table > tfoot > tr > td {
  padding: 12px 11px;
  vertical-align: middle;
}
.table .th-description {
  max-width: 150px;
}
.table .td-price {
  font-size: 26px;
  font-weight: 300;
  margin-top: 5px;
  text-align: right;
}
.table .td-total {
  font-weight: 600;
  font-size: 1.25em;
  padding-top: 20px;
  text-align: right;
}
.table .td-actions .btn.btn-sm, .table .td-actions .btn.btn-xs {
  padding-left: 3px;
  padding-right: 3px;
}
.table > tbody > tr {
  position: relative;
}
.table .has-switch {
  top: 9px;
  margin-top: -12px;
}

.table-striped tbody > tr:nth-of-type(2n+1) {
  background-color: #F3F2EE;
}
.table-striped > thead > tr > th,
.table-striped > tbody > tr > th,
.table-striped > tfoot > tr > th,
.table-striped > thead > tr > td,
.table-striped > tbody > tr > td,
.table-striped > tfoot > tr > td {
  padding: 15px 8px;
}

.table-shopping > thead > tr > th {
  color: #a49e93;
  font-size: 1.1em;
  font-weight: 300;
}
.table-shopping > tbody > tr > td {
  font-size: 16px;
  padding: 30px 5px;
}
.table-shopping > tbody > tr > td b {
  display: block;
  margin-bottom: 5px;
}
.table-shopping .td-number,
.table-shopping .td-price,
.table-shopping .td-total {
  font-size: 1.2em;
  min-width: 130px;
  text-align: right;
  padding-right: 20px;
}
.table-shopping .td-number small,
.table-shopping .td-price small,
.table-shopping .td-total small {
  margin-right: 3px;
}
.table-shopping .td-product {
  min-width: 170px;
  padding-left: 30px;
}
.table-shopping .td-product strong {
  color: #484541;
  font-size: 1.2em;
  font-weight: 600;
}
.table-shopping .td-number,
.table-shopping .td-total {
  color: #484541;
  font-weight: 600;
}
.table-shopping .td-quantity .btn-group {
  margin-left: 10px;
}
.table-shopping .img-container {
  border-radius: 6px;
  display: block;
  height: 100px;
  overflow: hidden;
  width: 100px;
  margin-left: 10px;
}
.table-shopping .img-container img {
  width: 100%;
}
.table-shopping .tr-actions > td {
  border-top: 0;
}

.table-icons {
  display: inline-block;
  min-width: 110px;
}

.tooltip {
  font-size: 14px;
  font-weight: 600;
}
.tooltip.top {
  margin-top: -11px;
  padding: 0;
}
.tooltip.top .tooltip-inner:after {
  border-top: 11px solid #FAE6A4;
  border-left: 11px solid transparent;
  border-right: 11px solid transparent;
  bottom: -10px;
}
.tooltip.top .tooltip-inner:before {
  border-top: 11px solid rgba(0, 0, 0, 0.2);
  border-left: 11px solid transparent;
  border-right: 11px solid transparent;
  bottom: -11px;
}
.tooltip.bottom {
  margin-top: 11px;
  padding: 0;
}
.tooltip.bottom .tooltip-inner:after {
  border-bottom: 11px solid #FAE6A4;
  border-left: 11px solid transparent;
  border-right: 11px solid transparent;
  top: -10px;
}
.tooltip.bottom .tooltip-inner:before {
  border-bottom: 11px solid rgba(0, 0, 0, 0.2);
  border-left: 11px solid transparent;
  border-right: 11px solid transparent;
  top: -11px;
}
.tooltip.left {
  margin-left: -11px;
  padding: 0;
}
.tooltip.left .tooltip-inner:after {
  border-left: 11px solid #FAE6A4;
  border-top: 11px solid transparent;
  border-bottom: 11px solid transparent;
  right: -10px;
  left: auto;
  margin-left: 0;
}
.tooltip.left .tooltip-inner:before {
  border-left: 11px solid rgba(0, 0, 0, 0.2);
  border-top: 11px solid transparent;
  border-bottom: 11px solid transparent;
  right: -11px;
  left: auto;
  margin-left: 0;
}
.tooltip.right {
  margin-left: 11px;
  padding: 0;
}
.tooltip.right .tooltip-inner:after {
  border-right: 11px solid #FAE6A4;
  border-top: 11px solid transparent;
  border-bottom: 11px solid transparent;
  left: -10px;
  top: 0;
  margin-left: 0;
}
.tooltip.right .tooltip-inner:before {
  border-right: 11px solid rgba(0, 0, 0, 0.2);
  border-top: 11px solid transparent;
  border-bottom: 11px solid transparent;
  left: -11px;
  top: 0;
  margin-left: 0;
}

.tooltip-arrow {
  display: none;
  opacity: 0;
}

.tooltip-inner {
  background-color: #FAE6A4;
  border-radius: 4px;
  box-shadow: 0 1px 13px rgba(0, 0, 0, 0.14), 0 0 0 1px rgba(115, 71, 38, 0.23);
  color: #734726;
  max-width: 280px;
  min-width: 90px;
  padding: 6px 10px;
  text-align: center;
  text-decoration: none;
}

.tooltip-inner:after {
  content: "";
  display: inline-block;
  left: 100%;
  margin-left: -60%;
  position: absolute;
}

.tooltip-inner:before {
  content: "";
  display: inline-block;
  left: 100%;
  margin-left: -60%;
  position: absolute;
}

.popover {
  padding: 0;
  border-radius: 6px;
  z-index: 1031;
  border: 0;
  -webkit-box-shadow: none;
  box-shadow: none;
}

.popover-title {
  font-size: 16px;
  background-color: rgba(91, 202, 255, 0.98);
  font-weight: normal;
  line-height: 22px;
  padding: 8px 15px;
  margin: 0;
  color: #FFFFFF;
  text-align: center;
  border-radius: 6px 6px 0 0;
}

.popover-content {
  padding: 9px 15px;
}

.popover .arrow {
  border: 0;
}

.popover.top .arrow {
  margin-left: 0;
}

.popover.bottom .arrow:after {
  border-bottom-color: rgba(91, 202, 255, 0.98);
}

.popover-filter {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1030;
  background-color: #000000;
  opacity: 0;
  filter: alpha(opacity=0);
  visibility: hidden;
  transition: visibility 0s linear 0.3s,opacity 0.3s linear;
}

.popover-filter.in {
  visibility: visible;
  opacity: 0.2;
  filter: alpha(opacity=20);
  transition-delay: 0s;
}

.nav > li > a:hover,
.nav > li > a:focus {
  background-color: transparent;
}

.navbar {
  border: 0;
  border-radius: 0;
  font-size: 16px;
}
.navbar .navbar-minimize {
  float: left;
}
.navbar .navbar-minimize .btn {
  margin: 18px 3px;
}
.navbar .navbar-minimize .ti-more-alt {
  display: inline-block;
  min-width: 14px;
  -webkit-transform: rotate(90deg);
  -ms-transform: rotate(90deg);
  transform: rotate(90deg);
}
.navbar .navbar-minimize .btn-simple {
  margin: 20px 3px;
  font-size: 14px;
}
.navbar .navbar-brand {
  font-weight: 600;
  margin: 12px 0px;
  padding: 15px 15px;
  font-size: 20px;
}
.navbar .navbar-nav > li > a {
  line-height: 1.42857;
  margin: 16px 0px;
  padding: 10px 15px;
}
.navbar .navbar-nav > li > a i,
.navbar .navbar-nav > li > a p {
  display: inline-block;
  margin: 0;
}
.navbar .navbar-nav > li > a i {
  position: relative;
  top: 1px;
}
.navbar .navbar-nav > li > a.btn {
  margin: 17px 3px;
  padding: 7px 18px;
}
.navbar .btn-simple {
  font-size: 16px;
}
.navbar.navbar-absolute {
  position: absolute;
  width: 100%;
  z-index: 1030;
}

.navbar-fixed {
  position: fixed;
  z-index: 1032;
  right: 0;
  width: 100%;
}
.navbar-fixed ~ .main-panel > .content {
  padding-top: 95px;
  min-height: calc(100% - 71px);
}

.navbar-nav > li > .dropdown-menu {
  border-radius: 6px;
  margin-top: -5px;
}

.navbar-transparent .navbar-brand, [class*="navbar-ct"] .navbar-brand {
  opacity: 0.9;
  filter: alpha(opacity=90);
}
.navbar-transparent .navbar-brand:focus, .navbar-transparent .navbar-brand:hover, [class*="navbar-ct"] .navbar-brand:focus, [class*="navbar-ct"] .navbar-brand:hover {
  background-color: transparent;
  opacity: 1;
  filter: alpha(opacity=100);
}
.navbar-transparent .navbar-brand:not([class*="text"]), [class*="navbar-ct"] .navbar-brand:not([class*="text"]) {
  color: #FFFFFF;
}
.navbar-transparent .navbar-nav > li > a:not(.btn), [class*="navbar-ct"] .navbar-nav > li > a:not(.btn) {
  color: #FFFFFF;
  border-color: #FFFFFF;
  opacity: 0.8;
  filter: alpha(opacity=80);
}
.navbar-transparent .navbar-nav > .active > a:not(.btn),
.navbar-transparent .navbar-nav > .active > a:hover:not(.btn),
.navbar-transparent .navbar-nav > .active > a:focus:not(.btn),
.navbar-transparent .navbar-nav > li > a:hover:not(.btn),
.navbar-transparent .navbar-nav > li > a:focus:not(.btn), [class*="navbar-ct"] .navbar-nav > .active > a:not(.btn),
[class*="navbar-ct"] .navbar-nav > .active > a:hover:not(.btn),
[class*="navbar-ct"] .navbar-nav > .active > a:focus:not(.btn),
[class*="navbar-ct"] .navbar-nav > li > a:hover:not(.btn),
[class*="navbar-ct"] .navbar-nav > li > a:focus:not(.btn) {
  background-color: transparent;
  border-radius: 3px;
  color: #FFFFFF;
  opacity: 1;
  filter: alpha(opacity=100);
}
.navbar-transparent .navbar-nav .nav > li > a.btn:hover, [class*="navbar-ct"] .navbar-nav .nav > li > a.btn:hover {
  background-color: transparent;
}
.navbar-transparent .navbar-nav > .dropdown > a .caret,
.navbar-transparent .navbar-nav > .dropdown > a:hover .caret,
.navbar-transparent .navbar-nav > .dropdown > a:focus .caret, [class*="navbar-ct"] .navbar-nav > .dropdown > a .caret,
[class*="navbar-ct"] .navbar-nav > .dropdown > a:hover .caret,
[class*="navbar-ct"] .navbar-nav > .dropdown > a:focus .caret {
  border-bottom-color: #FFFFFF;
  border-top-color: #FFFFFF;
}
.navbar-transparent .navbar-nav > .open > a,
.navbar-transparent .navbar-nav > .open > a:hover,
.navbar-transparent .navbar-nav > .open > a:focus, [class*="navbar-ct"] .navbar-nav > .open > a,
[class*="navbar-ct"] .navbar-nav > .open > a:hover,
[class*="navbar-ct"] .navbar-nav > .open > a:focus {
  background-color: transparent;
  color: #FFFFFF;
  opacity: 1;
  filter: alpha(opacity=100);
}
.navbar-transparent .btn-default, [class*="navbar-ct"] .btn-default {
  color: #FFFFFF;
  border-color: #FFFFFF;
}
.navbar-transparent .btn-default.btn-fill, [class*="navbar-ct"] .btn-default.btn-fill {
  color: #9A9A9A;
  background-color: #FFFFFF;
  opacity: 0.9;
  filter: alpha(opacity=90);
}
.navbar-transparent .btn-default.btn-fill:hover,
.navbar-transparent .btn-default.btn-fill:focus,
.navbar-transparent .btn-default.btn-fill:active,
.navbar-transparent .btn-default.btn-fill.active,
.navbar-transparent .open .dropdown-toggle.btn-fill.btn-default, [class*="navbar-ct"] .btn-default.btn-fill:hover,
[class*="navbar-ct"] .btn-default.btn-fill:focus,
[class*="navbar-ct"] .btn-default.btn-fill:active,
[class*="navbar-ct"] .btn-default.btn-fill.active,
[class*="navbar-ct"] .open .dropdown-toggle.btn-fill.btn-default {
  border-color: #FFFFFF;
  opacity: 1;
  filter: alpha(opacity=100);
}

.navbar-transparent .dropdown-menu .divider {
  background-color: rgba(255, 255, 255, 0.2);
}

.navbar-default {
  background-color: #f4f3ef;
  border-bottom: 1px solid #cfcfca;
}
.navbar-default .brand {
  color: #66615b !important;
}
.navbar-default .navbar-nav > li > a:not(.btn) {
  color: #9A9A9A;
}
.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:not(.btn):hover,
.navbar-default .navbar-nav > .active > a:not(.btn):focus,
.navbar-default .navbar-nav > li > a:not(.btn):hover,
.navbar-default .navbar-nav > li > a:not(.btn):focus {
  background-color: transparent;
  border-radius: 3px;
  color: #68B3C8;
  opacity: 1;
  filter: alpha(opacity=100);
}
.navbar-default .navbar-nav > .dropdown > a:hover .caret,
.navbar-default .navbar-nav > .dropdown > a:focus .caret {
  border-bottom-color: #68B3C8;
  border-top-color: #68B3C8;
}
.navbar-default .navbar-nav > .open > a,
.navbar-default .navbar-nav > .open > a:hover,
.navbar-default .navbar-nav > .open > a:focus {
  background-color: transparent;
  color: #68B3C8;
}
.navbar-default .navbar-nav .navbar-toggle:hover, .navbar-default .navbar-nav .navbar-toggle:focus {
  background-color: transparent;
}
.navbar-default:not(.navbar-transparent) .btn-neutral, .navbar-default:not(.navbar-transparent) .btn-neutral:hover, .navbar-default:not(.navbar-transparent) .btn-neutral:active {
  color: #9A9A9A;
}

.navbar-form {
  -webkit-box-shadow: none;
  box-shadow: none;
}
.navbar-form .input-group {
  margin: 0;
}

.navbar-ct-primary {
  background-color: #8ECFD5;
}

.navbar-ct-info {
  background-color: #7CE4FE;
}

.navbar-ct-success {
  background-color: #8EF3C5;
}

.navbar-ct-warning {
  background-color: #FFE28C;
}

.navbar-ct-danger {
  background-color: #FF8F5E;
}

.navbar-transparent {
  padding-top: 15px;
  background-color: transparent;
  border-bottom: 1px solid transparent;
}
.navbar-transparent .navbar-brand {
  color: #FFFFFF;
  opacity: 0.9;
  filter: alpha(opacity=90);
}
.navbar-transparent .navbar-brand:focus, .navbar-transparent .navbar-brand:hover {
  background-color: transparent;
  opacity: 1;
  filter: alpha(opacity=100);
}
.navbar-transparent .navbar-nav > li > a:not(.btn) {
  color: #FFFFFF;
  border-color: #FFFFFF;
  opacity: 0.8;
  filter: alpha(opacity=80);
}
.navbar-transparent .navbar-nav > .active > a:not(.btn),
.navbar-transparent .navbar-nav > .active > a:hover:not(.btn),
.navbar-transparent .navbar-nav > .active > a:focus:not(.btn),
.navbar-transparent .navbar-nav > li > a:hover:not(.btn),
.navbar-transparent .navbar-nav > li > a:focus:not(.btn) {
  background-color: transparent;
  border-radius: 3px;
  color: #FFFFFF;
  opacity: 1;
  filter: alpha(opacity=100);
}
.navbar-transparent .navbar-nav .nav > li > a.btn:hover {
  background-color: transparent;
}

.navbar-toggle {
  margin-top: 19px;
  margin-bottom: 19px;
  border: 0;
}
.navbar-toggle .icon-bar {
  background-color: #FFFFFF;
}
.navbar-toggle .navbar-collapse,
.navbar-toggle .navbar-form {
  border-color: transparent;
}
.navbar-toggle.navbar-default .navbar-toggle:hover, .navbar-toggle.navbar-default .navbar-toggle:focus {
  background-color: transparent;
}

.footer {
  background-attachment: fixed;
  position: relative;
  line-height: 20px;
}
.footer nav ul {
  list-style: none;
  margin: 0;
  padding: 0;
  font-weight: normal;
}
.footer nav ul li {
  display: inline-block;
  padding: 10px 15px;
  margin: 15px 3px;
  line-height: 20px;
  text-align: center;
}
.footer nav ul a:not(.btn) {
  color: #66615b;
  display: block;
  margin-bottom: 3px;
}
.footer nav ul a:not(.btn):focus, .footer nav ul a:not(.btn):hover {
  color: #484541;
}
.footer .copyright {
  color: #66615b;
  padding: 10px 15px;
  font-size: 14px;
  margin: 15px 3px;
  line-height: 20px;
  white-space: nowrap;
  text-align: center;
}
.footer .heart {
  color: #EB5E28;
}

.footer-black {
  background-color: #252422;
  color: #cfcfca;
}
.footer-black .links ul a:not(.btn) {
  color: #A49E9E;
}
.footer-black .links ul a:not(.btn):hover, .footer-black .links ul a:not(.btn):focus {
  color: #F1EAE0;
}
.footer-black .copyright {
  color: #66615b;
}
.footer-black .copyright ul > li a:not(.btn) {
  color: #66615b;
}
.footer-black hr {
  border-color: #66615b;
}

.footer-transparent {
  background-size: cover;
  position: relative;
  background-color: transparent;
}
.footer-transparent .container {
  z-index: 2;
  position: relative;
}
.footer-transparent hr {
  border-color: #A49E9E;
}
.footer-transparent .copyright {
  color: #A49E9E;
}
.footer-transparent .copyright ul > li a:not(.btn) {
  color: #A49E9E;
}

.card {
  border-radius: 6px;
  box-shadow: 0 2px 2px rgba(204, 197, 185, 0.5);
  background-color: #FFFFFF;
  color: #252422;
  margin-bottom: 20px;
  position: relative;
}
.card .card-title,
.card .stats,
.card .category,
.card .description,
.card .social-line,
.card .actions,
.card .card-content,
.card .card-footer,
.card small,
.card a {
  position: relative;
}
.card .card-link {
  color: #444444;
}
.card .card-link:hover, .card .card-link:focus {
  color: #333333;
}
.card img {
  max-width: 100%;
  height: auto;
}
.card .card-header {
  padding: 20px 15px 0px;
  position: relative;
  border-radius: 3px 3px 0 0;
  z-index: 3;
}
.card .card-header.header-with-icon {
  height: 150px;
}
.card .card-header img {
  width: 100%;
}
.card .card-header .category {
  padding: 5px 0px;
}
.card .actions {
  padding: 10px 15px;
}
.card .more {
  float: right;
  z-index: 4;
  display: block;
  padding-top: 10px;
  padding-right: 10px;
  position: relative;
}
.card .alert {
  border-radius: 4px;
}
.card .category {
  font-size: 14px;
  font-weight: 400;
  color: #9A9A9A;
  margin-bottom: 0px;
}
.card .category i {
  font-size: 16px;
}
.card .category.error {
  color: #EB5E28;
  font-size: 12px;
}
.card .card-title {
  margin: 0;
  color: #252422;
  font-weight: 300;
}
.card .card-content {
  padding: 15px 15px 10px 15px;
}
.card .card-content .card-title {
  margin: 10px 0 20px 0;
}
.card .card-content .big-title {
  text-transform: uppercase;
  text-align: center;
}
.card .card-content .category ~ .card-title {
  margin-top: 10px;
}
.card .card-content .description ~ .card-title {
  margin-top: -10px;
}
.card .description {
  font-size: 16px;
  color: #66615b;
}
.card h6 {
  font-size: 12px;
  margin: 10px 0;
}
.card .card-footer {
  padding: 0 15px 15px;
}
.card .card-footer .social-line .btn:first-child {
  border-radius: 0 0 0 6px;
}
.card .card-footer .social-line .btn:last-child {
  border-radius: 0 0 6px 0;
}
.card .card-footer hr {
  margin-top: 10px;
  margin-bottom: 15px;
}
.card .card-footer > .form-group {
  margin-bottom: 0;
}
.card .card-footer .footer-title {
  padding-top: 5px;
  display: inline-block;
}
.card .map {
  height: 280px;
  border-radius: 4px;
}
.card .map.map-big {
  height: 420px;
}
.card.card-separator:after {
  height: 100%;
  right: -15px;
  top: 0;
  width: 1px;
  background-color: #cfcfca;
  content: "";
  position: absolute;
}
.card .icon {
  display: block;
  margin: 0 auto;
  top: 60%;
  position: relative;
  transform: translateY(-50%);
  -webkit-transform: translateY(-50%);
  text-align: center;
}
.card .icon i {
  font-size: 60px;
  padding: 18px;
  width: 110px;
  border: 2px solid #ccc5b9;
  border-radius: 50%;
  height: 110px;
}
.card .icon-big {
  font-size: 3em;
}
.card .numbers {
  text-align: right;
  font-size: 2em;
}
.card .numbers p {
  margin: 0;
}
.card .pull-right .label {
  margin-top: 7px;
}
.card ul.team-members li {
  padding: 10px 0px;
}
.card ul.team-members li:not(:last-child) {
  border-bottom: 1px solid #F1EAE0;
}
.col-lg-4 .card .icon i {
  font-size: 80px;
  padding: 22px;
}
.card .stats {
  color: #a49e93;
  font-weight: 300;
}
.card.card-circle-chart .card-title {
  margin-bottom: 10px;
}
.card.card-circle-chart .card-content {
  padding: 10px 15px 10px;
  text-align: center;
}
.card .bootstrap-table {
  padding: 0px 20px;
}
.card[data-background-color="blue"] .card-title,
.card[data-background-color="blue"] .stats,
.card[data-background-color="blue"] .category,
.card[data-background-color="blue"] .description,
.card[data-background-color="blue"] .card-content,
.card[data-background-color="blue"] .card-footer,
.card[data-background-color="blue"] small,
.card[data-background-color="blue"] .content a, .card[data-background-color="green"] .card-title,
.card[data-background-color="green"] .stats,
.card[data-background-color="green"] .category,
.card[data-background-color="green"] .description,
.card[data-background-color="green"] .card-content,
.card[data-background-color="green"] .card-footer,
.card[data-background-color="green"] small,
.card[data-background-color="green"] .content a, .card[data-background-color="yellow"] .card-title,
.card[data-background-color="yellow"] .stats,
.card[data-background-color="yellow"] .category,
.card[data-background-color="yellow"] .description,
.card[data-background-color="yellow"] .card-content,
.card[data-background-color="yellow"] .card-footer,
.card[data-background-color="yellow"] small,
.card[data-background-color="yellow"] .content a, .card[data-background-color="brown"] .card-title,
.card[data-background-color="brown"] .stats,
.card[data-background-color="brown"] .category,
.card[data-background-color="brown"] .description,
.card[data-background-color="brown"] .card-content,
.card[data-background-color="brown"] .card-footer,
.card[data-background-color="brown"] small,
.card[data-background-color="brown"] .content a, .card[data-background-color="purple"] .card-title,
.card[data-background-color="purple"] .stats,
.card[data-background-color="purple"] .category,
.card[data-background-color="purple"] .description,
.card[data-background-color="purple"] .card-content,
.card[data-background-color="purple"] .card-footer,
.card[data-background-color="purple"] small,
.card[data-background-color="purple"] .content a, .card[data-background-color="orange"] .card-title,
.card[data-background-color="orange"] .stats,
.card[data-background-color="orange"] .category,
.card[data-background-color="orange"] .description,
.card[data-background-color="orange"] .card-content,
.card[data-background-color="orange"] .card-footer,
.card[data-background-color="orange"] small,
.card[data-background-color="orange"] .content a {
  color: #FFFFFF;
}
.card[data-background-color="blue"] .card-content a:hover,
.card[data-background-color="blue"] .card-content a:focus, .card[data-background-color="green"] .card-content a:hover,
.card[data-background-color="green"] .card-content a:focus, .card[data-background-color="yellow"] .card-content a:hover,
.card[data-background-color="yellow"] .card-content a:focus, .card[data-background-color="brown"] .card-content a:hover,
.card[data-background-color="brown"] .card-content a:focus, .card[data-background-color="purple"] .card-content a:hover,
.card[data-background-color="purple"] .card-content a:focus, .card[data-background-color="orange"] .card-content a:hover,
.card[data-background-color="orange"] .card-content a:focus {
  color: #FFFFFF;
}
.card[data-background-color="blue"] .icon i, .card[data-background-color="green"] .icon i, .card[data-background-color="yellow"] .icon i, .card[data-background-color="brown"] .icon i, .card[data-background-color="purple"] .icon i, .card[data-background-color="orange"] .icon i {
  color: #FFFFFF;
  border: 2px solid rgba(255, 255, 255, 0.6);
}
.card[data-background-color="blue"] {
  background: #b8d8d8;
}
.card[data-background-color="blue"] .category {
  color: #7a9e9f;
}
.card[data-background-color="blue"] .description {
  color: #506568;
}
.card[data-background-color="blue"] .icon i {
  color: #506568;
  border: 2px solid #7a9e9f;
}
.card[data-background-color="green"] {
  background: #d5e5a3;
}
.card[data-background-color="green"] .category {
  color: #92ac56;
}
.card[data-background-color="green"] .description {
  color: #60773d;
}
.card[data-background-color="green"] .icon i {
  color: #60773d;
  border: 2px solid #92ac56;
}
.card[data-bakcground-color="yellow"] {
  background: #ffe28c;
}
.card[data-bakcground-color="yellow"] .category {
  color: #d88715;
}
.card[data-bakcground-color="yellow"] .description {
  color: #b25825;
}
.card[data-bakcground-color="yellow"] .icon i {
  color: #b25825;
  border: 2px solid #d88715;
}
.card[data-background-color="brown"] {
  background: #d6c1ab;
}
.card[data-background-color="brown"] .category {
  color: #a47e65;
}
.card[data-background-color="brown"] .description {
  color: #75442e;
}
.card[data-background-color="brown"] .icon i {
  color: #75442e;
  border: 2px solid #a47e65;
}
.card[data-background-color="purple"] {
  background: #baa9ba;
}
.card[data-background-color="purple"] .category {
  color: #5a283d;
}
.card[data-background-color="purple"] .description {
  color: #3a283d;
}
.card[data-background-color="purple"] .icon i {
  color: #3a283d;
  border: 2px solid #5a283d;
}
.card[data-background-color="orange"] {
  background: #ff8f5e;
}
.card[data-background-color="orange"] .category {
  color: #e95e37;
}
.card[data-background-color="orange"] .description {
  color: #772510;
}
.card[data-background-color="orange"] .icon i {
  color: #772510;
  border: 2px solid #e95e37;
}

.btn-center {
  text-align: center;
}

.card-wizard {
  border-radius: 6px;
}
.card-wizard .nav-pills {
  margin-left: -15px;
  margin-right: -15px;
  margin-top: 10px;
  margin-bottom: 10px;
}
.card-wizard .nav-pills > li > a {
  text-align: center;
  border: none;
  background-color: #9A9A9A;
  color: #FFFFFF;
  text-transform: uppercase;
}
.card-wizard .nav-pills > li > a:hover, .card-wizard .nav-pills > li > a:focus {
  background-color: #9A9A9A;
  outline: 0 !important;
}
.card-wizard .nav-pills > li:first-child > a, .card-wizard .nav-pills > li:last-child > a {
  border-radius: 0;
}
.card-wizard .btn-finish {
  display: none;
}
.card-wizard .card-header .category {
  padding: 0px;
}
.card-wizard .card-content h5 {
  padding: 20px;
}
.card-wizard .card-content label.error:not(.form-control) {
  color: #EB5E28;
  font-weight: 300;
  font-size: 0.8em;
}
.card-wizard .footer {
  padding-bottom: 25px;
  padding-top: 25px;
}

.card-user .image {
  border-radius: 8px 8px 0 0;
  height: 150px;
  position: relative;
  overflow: hidden;
}
.card-user .image img {
  width: 100%;
  height: 150px;
}
.card-user .image-plain {
  height: 0;
  margin-top: 110px;
}
.card-user .author {
  text-align: center;
  text-transform: none;
  margin-top: -65px;
}
.card-user .author .card-title {
  color: #484541;
}
.card-user .author .card-title small {
  color: #ccc5b9;
}
.card-user .avatar {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  position: relative;
  margin-bottom: 15px;
}
.card-user .avatar.border-white {
  border: 5px solid #FFFFFF;
}
.card-user .avatar.border-gray {
  border: 5px solid #ccc5b9;
}
.card-user .card-title {
  font-weight: 600;
  line-height: 24px;
}
.card-user .card-content {
  min-height: 240px;
}
.card-user.card-plain .avatar {
  height: 190px;
  width: 190px;
}

.card-image .details {
  margin-left: 4px;
  min-height: 50px;
  padding: 0 4px 0.5em;
  position: relative;
}
.card-image .details .author {
  margin-top: -21px;
}
.card-image .details .author .name {
  color: #66615b;
  font-size: 1.1em;
  font-weight: bold;
  line-height: 1.15;
  max-width: 11em;
  overflow: hidden;
  padding-top: 3px;
  text-overflow: ellipsis;
}
.card-image .details .author .name:hover, .card-image .details .author .name:active, .card-image .details .author .name:focus {
  color: #484541;
}
.card-image .details .author img {
  height: 40px;
  width: 40px;
  margin-bottom: 5px;
}
.card-image .details .meta {
  color: #ccc5b9;
  font-size: 0.8em;
}
.card-image .details .actions {
  float: right;
  font-size: 0.875em;
  line-height: 2.6;
  position: absolute;
  right: 4px;
  top: 36px;
  z-index: 1;
}
.card-image .details .actions .btn.btn-simple {
  padding-left: 2px;
}
.card-image .details-center {
  text-align: center;
}
.card-image .details-center .author {
  position: relative;
  display: inline-block;
  text-align: left;
  margin: 20px auto 0;
}
.card-image .details-center .author img {
  position: absolute;
  left: 0;
  top: 0;
}
.card-image .details-center .author .text {
  padding-left: 50px;
}

.card-lock {
  text-align: center;
  padding: 30px;
  display: block;
}
.card-lock .author {
  border-radius: 50%;
  width: 100px;
  height: 100px;
  overflow: hidden;
  margin: 0 auto;
}
.card-lock .author img {
  width: 100%;
}
.card-lock h4 {
  margin-top: 15px;
  margin-bottom: 30px;
}

.card-login,
.card-lock {
  box-shadow: 0 25px 30px -13px rgba(40, 40, 40, 0.4);
  border-radius: 6px;
  padding-top: 25px;
  padding-bottom: 25px;
}

.card-login .card-header {
  text-align: center;
}
.card-login .card-header .card-title {
  color: #66615b;
  margin-bottom: 20px;
}
.card-login label {
  color: #66615b;
}
.card-login .btn-wd {
  min-width: 180px;
}
.card-login .card-footer {
  padding: 20px 0 0;
}
.card-login .card-footer .forgot {
  padding: 20px 0px 0px;
}

.card-product .details .name {
  margin-top: 20px;
}
.card-product .details .description {
  display: inline-block;
  margin-right: 60px;
}
.card-product .details .actions {
  top: 20px;
}
.card-product .details .actions h5 {
  color: #484541;
}

.card-user .footer,
.card-price .footer {
  padding: 5px 15px 10px;
}
.card-user hr,
.card-price hr {
  margin: 5px 15px;
}

.card-chat hr {
  margin-top: 10px;
  margin-bottom: 10px;
}
.card-chat .card-footer {
  padding: 0;
  float: right;
}

.card-map .full-screen-map {
  height: 500px;
  margin-top: 20px;
}

.card-plain {
  background-color: transparent;
  box-shadow: none;
  border-radius: 0;
}
.card-plain .image {
  border-radius: 4px;
}
.card-plain .card-content,
.card-plain .card-header {
  padding-left: 0;
  padding-right: 0;
}

.card-timeline .timeline {
  list-style: none;
  padding: 20px 0 20px;
  position: relative;
}
.card-timeline .timeline:before {
  top: 0;
  bottom: 0;
  position: absolute;
  content: " ";
  width: 3px;
  background-color: #E3E3E3;
  left: 50%;
  margin-left: -1.5px;
  background-color: #e8e7e3;
}
.card-timeline .timeline h6 {
  color: #9A9A9A;
  font-weight: 400;
  margin: 10px 0px 0px;
}
.card-timeline .timeline.timeline-simple:before {
  left: 5%;
  background-color: #e8e7e3;
}
.card-timeline .timeline.timeline-simple > li > .timeline-panel {
  width: 86%;
}
.card-timeline .timeline.timeline-simple > li > .timeline-badge {
  left: 5%;
}
.card-timeline .timeline > li {
  margin-bottom: 20px;
  position: relative;
}
.card-timeline .timeline > li:before, .card-timeline .timeline > li:after {
  content: " ";
  display: table;
}
.card-timeline .timeline > li:after {
  clear: both;
}
.card-timeline .timeline > li > .timeline-panel {
  width: 45%;
  float: left;
  padding: 20px;
  border-radius: 6px;
  box-shadow: 0 2px 2px rgba(204, 197, 185, 0.5);
  background-color: #FFFFFF;
  color: #252422;
  margin-bottom: 20px;
  position: relative;
}
.card-timeline .timeline > li > .timeline-panel:before {
  position: absolute;
  top: 26px;
  right: -15px;
  display: inline-block;
  border-top: 15px solid transparent;
  border-left: 15px solid #e8e7e3;
  border-right: 0 solid #e8e7e3;
  border-bottom: 15px solid transparent;
  content: " ";
}
.card-timeline .timeline > li > .timeline-panel:after {
  position: absolute;
  top: 27px;
  right: -14px;
  display: inline-block;
  border-top: 14px solid transparent;
  border-left: 14px solid #FFFFFF;
  border-right: 0 solid #FFFFFF;
  border-bottom: 14px solid transparent;
  content: " ";
}
.card-timeline .timeline > li > .timeline-badge {
  color: #FFFFFF;
  width: 50px;
  height: 50px;
  line-height: 51px;
  font-size: 1.4em;
  text-align: center;
  position: absolute;
  top: 16px;
  left: 50%;
  margin-left: -25px;
  background-color: #9A9A9A;
  z-index: 100;
  border-top-right-radius: 50%;
  border-top-left-radius: 50%;
  border-bottom-right-radius: 50%;
  border-bottom-left-radius: 50%;
}
.card-timeline .timeline > li > .timeline-badge [class^="ti-"],
.card-timeline .timeline > li > .timeline-badge [class*=" ti-"] {
  line-height: inherit;
}
.card-timeline .timeline > li.timeline-inverted > .timeline-panel {
  float: right;
  background-color: #FFFFFF;
}
.card-timeline .timeline > li.timeline-inverted > .timeline-panel:before {
  border-left-width: 0;
  border-right-width: 15px;
  left: -15px;
  right: auto;
}
.card-timeline .timeline > li.timeline-inverted > .timeline-panel:after {
  border-left-width: 0;
  border-right-width: 14px;
  left: -14px;
  right: auto;
}
.card-timeline .timeline-heading {
  margin-bottom: 15px;
}
.card-timeline .timeline-badge.primary {
  background-color: #b8d8d8 !important;
}
.card-timeline .timeline-badge.info {
  background-color: #b8d8d8 !important;
}
.card-timeline .timeline-badge.success {
  background-color: #d5e5a3 !important;
}
.card-timeline .timeline-badge.warning {
  background-color: #ffe28c !important;
}
.card-timeline .timeline-badge.danger {
  background-color: #ff8f5e !important;
}
.card-timeline .timeline-title {
  margin-top: 0;
  color: inherit;
}
.card-timeline .timeline-body > p,
.card-timeline .timeline-body > ul {
  margin-bottom: 0;
}
.card-timeline .timeline-body > p + p {
  margin-top: 5px;
}

/*           Labels & Progress-bar              */
.label {
  padding: 3px 8px;
  border-radius: 12px;
  color: #FFFFFF;
  font-weight: 500;
  font-size: 0.75em;
  text-transform: uppercase;
  display: inline-block;
  line-height: 1.5em;
}

.label-icon {
  padding: 0.4em 0.55em;
}
.label-icon i {
  font-size: 0.8em;
  line-height: 1;
}

.label-default {
  background-color: #66615b;
}

.label-primary {
  background-color: #7A9E9F;
}

.label-info {
  background-color: #68B3C8;
}

.label-success {
  background-color: #7AC29A;
}

.label-warning {
  background-color: #F3BB45;
}

.label-danger {
  background-color: #EB5E28;
}

.card-chat .chat {
  list-style: none;
  background: none;
  margin: 0;
  padding: 0 0 20px 0;
  margin-top: 10px;
}
.card-chat .chat > li {
  padding: 0.5rem;
  overflow: hidden;
  display: flex;
  display: -webkit-flex;
}
.card-chat .chat .avatar {
  width: 40px;
  height: 40px;
  position: relative;
  display: block;
  z-index: 2;
  background-color: rgba(255, 255, 255, 0.9);
}
.card-chat .chat .avatar img {
  width: 40px;
  height: 40px;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
.card-chat .chat .footer {
  padding: 0;
  text-align: right;
  font-size: 11px;
  color: #9A9A9A;
}
.card-chat .chat h6 {
  display: inline-block;
  font-size: 11px;
  font-weight: 300;
}
.card-chat .send-message {
  text-align: center;
}
.card-chat .send-message .avatar {
  width: 40px;
  height: 40px;
  display: inline-block;
  margin-top: 7px;
}
.card-chat .send-message .avatar img {
  width: 40px;
  height: 40px;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  background-color: rgba(255, 255, 255, 0.9);
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
.card-chat .send-message .send-button {
  width: 75px;
  display: inline-block;
}
.card-chat ::-webkit-input-placeholder {
  color: #9A9A9A;
}
.card-chat .other .msg {
  order: 1;
  border: 2px solid #b8d8d8;
  border-radius: 16px;
  position: relative;
  margin-left: 22px;
  max-width: 75%;
  background-color: #b8d8d8;
}
.card-chat .other .msg:before {
  content: "";
  position: absolute;
  top: 15px;
  left: -16px;
  height: 0px;
  border: 8px solid #b8d8d8;
  border-left-color: transparent;
  border-bottom-color: transparent;
}
.card-chat .other .no-avatar {
  width: 40px;
  height: 40px;
  order: 2;
}
.card-chat .self {
  justify-content: flex-end;
  align-items: flex-end;
  -webkit-align-items: flex-end;
  -webkit-justify-content: flex-end;
}
.card-chat .self .msg {
  order: 1;
  border: 2px solid #d6c1ab;
  border-radius: 16px;
  position: relative;
  margin-right: 22px;
  max-width: 75%;
  background-color: #d6c1ab;
}
.card-chat .self .avatar {
  order: 2;
}
.card-chat .self .avatar:after {
  content: "";
  position: absolute;
  display: inline-block;
  bottom: 19px;
  right: 48px;
  width: 0px;
  height: 0px;
  border: 8px solid #d6c1ab;
  border-right-color: transparent;
  border-top-color: transparent;
}
.card-chat .self .no-avatar {
  width: 40px;
  height: 40px;
  order: 2;
}
.card-chat .msg {
  background: white;
  min-width: 50px;
  padding: 10px 10px 0px 10px;
  border-radius: 2px;
}
.card-chat .msg p {
  margin: 0 0 0.2rem 0;
  font-size: 15px;
  line-height: 1.2em;
}
.card-chat .msg img {
  position: relative;
  display: block;
  width: 450px;
  border-radius: 100%;
  -webkit-border-radius: 100%;
  -moz-border-radius: 100%;
  -ms-border-radius: 100%;
  cursor: default;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}
.card-chat input.textarea {
  display: inline-block;
  width: calc(100% - 132px);
}
@media screen and (max-width: 800px) {
  .card-chat .msg img {
    width: 300px;
  }
}
@media screen and (max-width: 550px) {
  .card-chat .msg img {
    width: 200px;
  }
}

.checkbox,
.radio {
  padding-left: 20px;
  margin-bottom: 12px;
}

.checkbox label {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  padding-left: 10px;
}

.checkbox label::before {
  content: "";
  display: inline-block;
  position: absolute;
  width: 19px;
  height: 19px;
  left: 0;
  margin-left: -20px;
  border-radius: 3px;
  opacity: .50;
  background-color: #66615b;
  transition: opacity 0.2s linear;
}

.checkbox label::after {
  display: inline-block;
  position: absolute;
  width: 19px;
  height: 19px;
  left: 0;
  top: -2px;
  text-align: center;
  margin-left: -20px;
  font-size: 16px;
  color: #FFFFFF;
}

.checkbox input[type="checkbox"],
.radio input[type="radio"] {
  opacity: 0;
  z-index: 1;
  cursor: pointer;
  width: 19px;
  height: 19px;
  top: -2px;
}

.checkbox input[type="checkbox"]:checked + label::after,
.checkbox input[type="checkbox"]:not(:disabled):hover + label::after {
  font-family: 'FontAwesome';
  content: "\f00c";
}

.checkbox input[type="checkbox"]:checked + label::before {
  opacity: 1;
}

.checkbox input[type="checkbox"]:disabled + label {
  color: #cfcfcf;
}

.checkbox input[type="checkbox"]:disabled + label::before {
  background-color: #cfcfcf;
  cursor: not-allowed;
}

.checkbox.checkbox-circle label::before {
  border-radius: 50%;
}

.checkbox.checkbox-inline {
  margin-top: 0;
}

.checkbox-primary input[type="checkbox"]:checked + label::before {
  background-color: #428bca;
  border-color: #428bca;
}

.checkbox-primary input[type="checkbox"]:checked + label::after {
  color: #fff;
}

.checkbox-danger input[type="checkbox"]:checked + label::before {
  background-color: #d9534f;
  border-color: #d9534f;
}

.checkbox-danger input[type="checkbox"]:checked + label::after {
  color: #fff;
}

.checkbox-info input[type="checkbox"]:checked + label::before {
  background-color: #5bc0de;
  border-color: #5bc0de;
}

.checkbox-info input[type="checkbox"]:checked + label::after {
  color: #fff;
}

.checkbox-warning input[type="checkbox"]:checked + label::before {
  background-color: #f0ad4e;
  border-color: #f0ad4e;
}

.checkbox-warning input[type="checkbox"]:checked + label::after {
  color: #fff;
}

.checkbox-success input[type="checkbox"]:checked + label::before {
  background-color: #5cb85c;
  border-color: #5cb85c;
}

.checkbox-success input[type="checkbox"]:checked + label::after {
  color: #fff;
}

.radio label {
  display: inline-block;
  vertical-align: middle;
  position: relative;
  padding-left: 10px;
}

.radio label::before {
  font-family: 'FontAwesome';
  content: "\f10c";
  font-size: 22px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  position: absolute;
  left: 0;
  top: -5px;
  color: #66615b;
  opacity: .50;
  margin-left: -20px;
}

.radio input[type="radio"]:not(:disabled):hover + label::before {
  font-family: 'FontAwesome';
  content: "\f192";
  color: #66615b;
  opacity: .50;
}

.radio label::after {
  display: inline-block;
  position: absolute;
  font-family: 'FontAwesome';
  content: "\f192";
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  opacity: 0;
  left: 0px;
  top: -5px;
  margin-left: -20px;
  font-size: 22px;
  background-color: transparent;
  color: #66615b;
  transition: opacity 0.2s linear;
}

.radio input[type="radio"]:checked + label::after {
  opacity: 1;
}

.radio input[type="radio"]:disabled + label {
  color: #cfcfcf;
}

.radio input[type="radio"]:disabled + label::before,
.radio input[type="radio"]:disabled + label::after {
  color: #cfcfcf;
}

.radio.radio-inline {
  margin-top: 0;
}

.radio-primary input[type="radio"] + label::after {
  background-color: #428bca;
}

.radio-primary input[type="radio"]:checked + label::before {
  border-color: #428bca;
}

.radio-primary input[type="radio"]:checked + label::after {
  background-color: #428bca;
}

.radio-danger input[type="radio"] + label::after {
  background-color: #d9534f;
}

.radio-danger input[type="radio"]:checked + label::before {
  border-color: #d9534f;
}

.radio-danger input[type="radio"]:checked + label::after {
  background-color: #d9534f;
}

.radio-info input[type="radio"] + label::after {
  background-color: #5bc0de;
}

.radio-info input[type="radio"]:checked + label::before {
  border-color: #5bc0de;
}

.radio-info input[type="radio"]:checked + label::after {
  background-color: #5bc0de;
}

.radio-warning input[type="radio"] + label::after {
  background-color: #f0ad4e;
}

.radio-warning input[type="radio"]:checked + label::before {
  border-color: #f0ad4e;
}

.radio-warning input[type="radio"]:checked + label::after {
  background-color: #f0ad4e;
}

.radio-success input[type="radio"] + label::after {
  background-color: #5cb85c;
}

.radio-success input[type="radio"]:checked + label::before {
  border-color: #5cb85c;
}

.radio-success input[type="radio"]:checked + label::after {
  background-color: #5cb85c;
}

.dropdown-menu {
  background-color: #FFFFFF;
  border: 0 none;
  border-radius: 6px;
  margin-top: 5px;
  padding: 0px;
  -webkit-box-shadow: 0 2px rgba(17, 16, 15, 0.1), 0 2px 10px rgba(17, 16, 15, 0.1);
  box-shadow: 0 2px rgba(17, 16, 15, 0.1), 0 2px 10px rgba(17, 16, 15, 0.1);
}
.dropdown .dropdown-menu, .dropdown-menu.bootstrap-datetimepicker-widget, .bootstrap-table .btn-group .dropdown-menu, .btn-group .dropdown-menu, .dropup .dropdown-menu {
  -webkit-transform: translate3d(0, -30px, 0);
  -moz-transform: translate3d(0, -30px, 0);
  -o-transform: translate3d(0, -30px, 0);
  -ms-transform: translate3d(0, -30px, 0);
  transform: translate3d(0, -30px, 0);
  -webkit-transition: all 150ms linear;
  -moz-transition: all 150ms linear;
  -o-transition: all 150ms linear;
  -ms-transition: all 150ms linear;
  transition: all 150ms linear;
  opacity: 0;
  filter: alpha(opacity=0);
  visibility: hidden;
  display: block;
}
.dropdown.open .dropdown-menu, .dropdown-menu.bootstrap-datetimepicker-widget.open, .bootstrap-table .btn-group.open .dropdown-menu, .btn-group.open .dropdown-menu {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  opacity: 1;
  filter: alpha(opacity=100);
  visibility: visible;
}
.dropup .dropdown-menu, .dropdown-menu.bootstrap-datetimepicker-widget.top {
  -webkit-transform: translate3d(0, 30px, 0);
  -moz-transform: translate3d(0, 30px, 0);
  -o-transform: translate3d(0, 30px, 0);
  -ms-transform: translate3d(0, 30px, 0);
  transform: translate3d(0, 30px, 0);
  -webkit-transition: all 150ms linear;
  -moz-transition: all 150ms linear;
  -o-transition: all 150ms linear;
  -ms-transition: all 150ms linear;
  transition: all 150ms linear;
  opacity: 0;
  filter: alpha(opacity=0);
  visibility: hidden;
  display: block;
}
.dropup.open .dropdown-menu, .dropdown-menu.bootstrap-datetimepicker-widget.top.open {
  -webkit-transform: translate3d(0, 0, 0);
  -moz-transform: translate3d(0, 0, 0);
  -o-transform: translate3d(0, 0, 0);
  -ms-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  opacity: 1;
  filter: alpha(opacity=100);
  visibility: visible;
}
.dropup .dropdown-menu {
  margin-bottom: 5px;
}
.dropup .dropdown-menu > .active > a,
.dropup .dropdown-menu > .active > a:hover,
.dropup .dropdown-menu > .active > a:focus {
  background-color: #F9F7F3;
  color: #66615b;
}
.dropup .dropdown-menu > li > a:hover,
.dropup .dropdown-menu > li > a:focus {
  background-color: #66615B;
  color: rgba(255, 255, 255, 0.85);
  opacity: 1;
  text-decoration: none;
}
.dropdown-menu:before {
  border-bottom: 11px solid #F1EAE0;
  border-left: 11px solid transparent;
  border-right: 11px solid transparent;
  content: "";
  display: inline-block;
  position: absolute;
  right: 12px;
  top: -12px;
}
.dropdown-menu:after {
  border-bottom: 11px solid #FFFFFF;
  border-left: 11px solid transparent;
  border-right: 11px solid transparent;
  content: "";
  display: inline-block;
  position: absolute;
  right: 12px;
  top: -11px;
}
.dropup .dropdown-menu:before, .dropdown-menu.bootstrap-datetimepicker-widget.top:before {
  border-top: 11px solid #F1EAE0;
  border-left: 11px solid transparent;
  border-right: 11px solid transparent;
  border-bottom: none;
  content: "";
  display: inline-block;
  position: absolute;
  left: 12px;
  right: auto;
  top: auto;
  bottom: -12px;
}
.dropup .dropdown-menu:after, .dropdown-menu.bootstrap-datetimepicker-widget.top:after {
  border-top: 11px solid #FFFFFF;
  border-left: 11px solid transparent;
  border-right: 11px solid transparent;
  border-bottom: none;
  content: "";
  display: inline-block;
  position: absolute;
  left: 12px;
  right: auto;
  bottom: -11px;
  top: auto;
}
.dropdown-menu.dropdown-menu-left:after {
  left: 12px;
  right: auto;
}
.dropdown-menu.dropdown-menu-left:before {
  left: 12px;
  right: auto;
}
.dropdown-menu .divider {
  background-color: #F1EAE0;
  margin: 0px;
}
.dropdown-menu .dropdown-header {
  color: #9A9A9A;
  font-size: 12px;
  padding: 10px 15px;
}
.dropdown-menu > li > a {
  color: #66615b;
  font-size: 14px;
  padding: 10px 15px;
  -webkit-transition: none;
  -moz-transition: none;
  -o-transition: none;
  -ms-transition: none;
  transition: none;
}
.dropdown-menu > li > a img {
  margin-top: -3px;
}
.dropdown-menu > li > a:focus {
  outline: 0 !important;
}
.btn-group.select .dropdown-menu {
  min-width: 100%;
}
.dropdown-menu > li:first-child > a {
  border-top-left-radius: 6px;
  border-top-right-radius: 6px;
}
.dropdown-menu > li:last-child > a {
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}
.dropdown-menu > li > a:hover,
.dropdown-menu > li > a:focus {
  background-color: #66615B;
  color: rgba(255, 255, 255, 0.85);
  opacity: 1;
  text-decoration: none;
}
.dropdown-menu.dropdown-primary > li:not(.disabled) > a:hover,
.dropdown-menu.dropdown-primary > li:not(.disabled) > a:focus {
  background-color: #7A9E9F;
}
.dropdown-menu.dropdown-info > li:not(.disabled) > a:hover,
.dropdown-menu.dropdown-info > li:not(.disabled) > a:focus {
  background-color: #68B3C8;
}
.dropdown-menu.dropdown-success > li:not(.disabled) > a:hover,
.dropdown-menu.dropdown-success > li:not(.disabled) > a:focus {
  background-color: #7AC29A;
}
.dropdown-menu.dropdown-warning > li:not(.disabled) > a:hover,
.dropdown-menu.dropdown-warning > li:not(.disabled) > a:focus {
  background-color: #F3BB45;
}
.dropdown-menu.dropdown-danger > li:not(.disabled) > a:hover,
.dropdown-menu.dropdown-danger > li:not(.disabled) > a:focus {
  background-color: #EB5E28;
}
.dropdown-menu > li.dropdown-footer {
  background-color: #E8E7E3;
  border-radius: 0 0 6px 6px;
}
.dropdown-menu > li.dropdown-footer > ul {
  list-style: outside none none;
  padding: 0px 5px;
}
.dropdown-menu > li.dropdown-footer > ul > li {
  display: inline-block;
  text-align: left;
  padding: 0 10px;
}
.dropdown-menu > li.dropdown-footer > ul > li > a {
  color: #9C9B99;
  font-size: 0.9em;
  line-height: 35px;
}
.dropdown-menu > li.dropdown-footer > ul > li:hover a {
  color: #5E5E5C;
}

.select .no-style:hover,
.select .no-style:active,
.select .no-style:focus {
  background-color: #FFFFFF;
  color: #66615b;
}
.select .no-style:hover .caret,
.select .no-style:active .caret,
.select .no-style:focus .caret {
  border-top-color: #66615b;
}

.open .no-style {
  background-color: #FFFFFF !important;
  color: #66615b !important;
}
.open .no-style .caret {
  border-top-color: #66615b !important;
}

.btn-group.select {
  overflow: hidden;
}

.btn-group.select.open {
  overflow: visible;
}

.notification-bubble {
  left: 25px;
  position: absolute;
  top: 13px;
}

.dropdown-notification .dropdown-header {
  border-bottom: 1px solid #F1EAE0;
}
.dropdown-notification .no-notification {
  color: #9A9A9A;
  font-size: 1.2em;
  padding: 30px 30px;
  text-align: center;
}

.dropdown-notification-list > li {
  border-bottom: 1px solid #F1EAE0;
  color: #66615b;
  font-size: 14px;
  padding: 10px 5px;
}
.dropdown-notification-list > li > a {
  color: #66615b;
  white-space: normal;
}
.dropdown-notification-list > li > a .notification-text {
  padding-left: 40px;
  position: relative;
}
.dropdown-notification-list > li > a .label {
  display: block;
  position: absolute;
  top: 50%;
  margin-top: -12px;
  left: 7px;
}
.dropdown-notification-list > li > a .message {
  font-size: 0.9em;
  line-height: 0.7;
}
.dropdown-notification-list > li > a .time {
  color: #9A9A9A;
  font-size: 0.7em;
}
.dropdown-notification-list > li > a .read-notification {
  font-size: 12px;
  opacity: 0;
  position: absolute;
  right: 5px;
  top: 50%;
  margin-top: -12px;
}
.dropdown-notification-list > li:hover,
.dropdown-notification-list > li:focus {
  background-color: #F0EFEB;
  color: #66615b;
  opacity: 1;
  text-decoration: none;
}
.dropdown-notification-list > li:hover .read-notification,
.dropdown-notification-list > li:focus .read-notification {
  opacity: 1;
}

.scroll-area {
  max-height: 310px;
  overflow-y: scroll;
  list-style: outside none none;
  padding: 0px;
}

.dropdown-sharing li {
  color: #66615b;
  font-size: 14px;
}
.dropdown-sharing li .social-line {
  line-height: 28px;
  padding: 10px 20px 5px 20px;
}
.dropdown-sharing li .social-line [class*="icon-"] {
  font-size: 20px;
}
.dropdown-sharing li:hover .social-line,
.dropdown-sharing li:hover a,
.dropdown-sharing li:hover .action-line,
.dropdown-sharing li:focus .social-line,
.dropdown-sharing li:focus a,
.dropdown-sharing li:focus .action-line {
  background-color: #F9F7F3;
  color: #66615b;
  opacity: 1;
  text-decoration: none;
}

.dropdown-actions li .action-line {
  line-height: 24px;
  padding: 10px 20px;
  font-weight: bold;
}
.dropdown-actions li .action-line [class*="icon-"] {
  font-size: 24px;
}
.dropdown-actions li .action-line .col-xs-9 {
  line-height: 34px;
}
.dropdown-actions li .link-danger {
  color: #EB5E28;
}
.dropdown-actions li .link-danger:hover, .dropdown-actions li .link-danger:active, .dropdown-actions li .link-danger:focus {
  color: #EB5E28;
}
.dropdown-actions li:hover a,
.dropdown-actions li:focus a {
  background-color: #F0EFEB;
  color: #66615b;
  opacity: 1;
  text-decoration: none;
}

.btn-facebook {
  border-color: #3b5998;
  color: #3b5998;
}
.btn-facebook:hover, .btn-facebook:focus, .btn-facebook:active, .btn-facebook.active, .open > .btn-facebook.dropdown-toggle {
  background-color: #3b5998;
  border-color: #3b5998;
  color: #FFFFFF;
}
.btn-facebook:disabled, .btn-facebook[disabled], .btn-facebook.disabled {
  background-color: transparent;
  border-color: #3b5998;
}
.btn-facebook.btn-fill {
  color: #FFFFFF;
  background-color: #3b5998;
  opacity: 0.8;
}
.btn-facebook.btn-fill:hover, .btn-facebook.btn-fill:focus, .btn-facebook.btn-fill:active, .btn-facebook.btn-fill.active, .open > .btn-facebook.btn-fill.dropdown-toggle {
  background-color: #3b5998;
  border-color: #3b5998;
  color: #FFFFFF;
  opacity: 1;
}
.btn-facebook.btn-simple {
  color: #3b5998;
  opacity: 0.8;
}
.btn-facebook.btn-simple:hover, .btn-facebook.btn-simple:focus, .btn-facebook.btn-simple:active, .btn-facebook.btn-simple.active, .open > .btn-facebook.btn-simple.dropdown-toggle {
  background-color: transparent;
  color: #3b5998;
  opacity: 1;
}
.btn-facebook.btn-simple:hover i, .btn-facebook.btn-simple:focus i, .btn-facebook.btn-simple:active i, .btn-facebook.btn-simple.active i, .open > .btn-facebook.btn-simple.dropdown-toggle i {
  color: #3b5998;
  opacity: 1;
}

.btn-twitter {
  border-color: #55acee;
  color: #55acee;
}
.btn-twitter:hover, .btn-twitter:focus, .btn-twitter:active, .btn-twitter.active, .open > .btn-twitter.dropdown-toggle {
  background-color: #55acee;
  border-color: #55acee;
  color: #FFFFFF;
}
.btn-twitter:disabled, .btn-twitter[disabled], .btn-twitter.disabled {
  background-color: transparent;
  border-color: #55acee;
}
.btn-twitter.btn-fill {
  color: #FFFFFF;
  background-color: #55acee;
  opacity: 0.8;
}
.btn-twitter.btn-fill:hover, .btn-twitter.btn-fill:focus, .btn-twitter.btn-fill:active, .btn-twitter.btn-fill.active, .open > .btn-twitter.btn-fill.dropdown-toggle {
  background-color: #55acee;
  border-color: #55acee;
  color: #FFFFFF;
  opacity: 1;
}
.btn-twitter.btn-simple {
  color: #55acee;
  opacity: 0.8;
}
.btn-twitter.btn-simple:hover, .btn-twitter.btn-simple:focus, .btn-twitter.btn-simple:active, .btn-twitter.btn-simple.active, .open > .btn-twitter.btn-simple.dropdown-toggle {
  background-color: transparent;
  color: #55acee;
  opacity: 1;
}
.btn-twitter.btn-simple:hover i, .btn-twitter.btn-simple:focus i, .btn-twitter.btn-simple:active i, .btn-twitter.btn-simple.active i, .open > .btn-twitter.btn-simple.dropdown-toggle i {
  color: #55acee;
  opacity: 1;
}

.btn-pinterest {
  border-color: #cc2127;
  color: #cc2127;
}
.btn-pinterest:hover, .btn-pinterest:focus, .btn-pinterest:active, .btn-pinterest.active, .open > .btn-pinterest.dropdown-toggle {
  background-color: #cc2127;
  border-color: #cc2127;
  color: #FFFFFF;
}
.btn-pinterest:disabled, .btn-pinterest[disabled], .btn-pinterest.disabled {
  background-color: transparent;
  border-color: #cc2127;
}
.btn-pinterest.btn-fill {
  color: #FFFFFF;
  background-color: #cc2127;
  opacity: 0.8;
}
.btn-pinterest.btn-fill:hover, .btn-pinterest.btn-fill:focus, .btn-pinterest.btn-fill:active, .btn-pinterest.btn-fill.active, .open > .btn-pinterest.btn-fill.dropdown-toggle {
  background-color: #cc2127;
  border-color: #cc2127;
  color: #FFFFFF;
  opacity: 1;
}
.btn-pinterest.btn-simple {
  color: #cc2127;
  opacity: 0.8;
}
.btn-pinterest.btn-simple:hover, .btn-pinterest.btn-simple:focus, .btn-pinterest.btn-simple:active, .btn-pinterest.btn-simple.active, .open > .btn-pinterest.btn-simple.dropdown-toggle {
  background-color: transparent;
  color: #cc2127;
  opacity: 1;
}
.btn-pinterest.btn-simple:hover i, .btn-pinterest.btn-simple:focus i, .btn-pinterest.btn-simple:active i, .btn-pinterest.btn-simple.active i, .open > .btn-pinterest.btn-simple.dropdown-toggle i {
  color: #cc2127;
  opacity: 1;
}

.btn-google {
  border-color: #dd4b39;
  color: #dd4b39;
}
.btn-google:hover, .btn-google:focus, .btn-google:active, .btn-google.active, .open > .btn-google.dropdown-toggle {
  background-color: #dd4b39;
  border-color: #dd4b39;
  color: #FFFFFF;
}
.btn-google:disabled, .btn-google[disabled], .btn-google.disabled {
  background-color: transparent;
  border-color: #dd4b39;
}
.btn-google.btn-fill {
  color: #FFFFFF;
  background-color: #dd4b39;
  opacity: 0.8;
}
.btn-google.btn-fill:hover, .btn-google.btn-fill:focus, .btn-google.btn-fill:active, .btn-google.btn-fill.active, .open > .btn-google.btn-fill.dropdown-toggle {
  background-color: #dd4b39;
  border-color: #dd4b39;
  color: #FFFFFF;
  opacity: 1;
}
.btn-google.btn-simple {
  color: #dd4b39;
  opacity: 0.8;
}
.btn-google.btn-simple:hover, .btn-google.btn-simple:focus, .btn-google.btn-simple:active, .btn-google.btn-simple.active, .open > .btn-google.btn-simple.dropdown-toggle {
  background-color: transparent;
  color: #dd4b39;
  opacity: 1;
}
.btn-google.btn-simple:hover i, .btn-google.btn-simple:focus i, .btn-google.btn-simple:active i, .btn-google.btn-simple.active i, .open > .btn-google.btn-simple.dropdown-toggle i {
  color: #dd4b39;
  opacity: 1;
}

.btn-linkedin {
  border-color: #0976b4;
  color: #0976b4;
}
.btn-linkedin:hover, .btn-linkedin:focus, .btn-linkedin:active, .btn-linkedin.active, .open > .btn-linkedin.dropdown-toggle {
  background-color: #0976b4;
  border-color: #0976b4;
  color: #FFFFFF;
}
.btn-linkedin:disabled, .btn-linkedin[disabled], .btn-linkedin.disabled {
  background-color: transparent;
  border-color: #0976b4;
}
.btn-linkedin.btn-fill {
  color: #FFFFFF;
  background-color: #0976b4;
  opacity: 0.8;
}
.btn-linkedin.btn-fill:hover, .btn-linkedin.btn-fill:focus, .btn-linkedin.btn-fill:active, .btn-linkedin.btn-fill.active, .open > .btn-linkedin.btn-fill.dropdown-toggle {
  background-color: #0976b4;
  border-color: #0976b4;
  color: #FFFFFF;
  opacity: 1;
}
.btn-linkedin.btn-simple {
  color: #0976b4;
  opacity: 0.8;
}
.btn-linkedin.btn-simple:hover, .btn-linkedin.btn-simple:focus, .btn-linkedin.btn-simple:active, .btn-linkedin.btn-simple.active, .open > .btn-linkedin.btn-simple.dropdown-toggle {
  background-color: transparent;
  color: #0976b4;
  opacity: 1;
}
.btn-linkedin.btn-simple:hover i, .btn-linkedin.btn-simple:focus i, .btn-linkedin.btn-simple:active i, .btn-linkedin.btn-simple.active i, .open > .btn-linkedin.btn-simple.dropdown-toggle i {
  color: #0976b4;
  opacity: 1;
}

.btn-dribbble {
  border-color: #ea4c89;
  color: #ea4c89;
}
.btn-dribbble:hover, .btn-dribbble:focus, .btn-dribbble:active, .btn-dribbble.active, .open > .btn-dribbble.dropdown-toggle {
  background-color: #ea4c89;
  border-color: #ea4c89;
  color: #FFFFFF;
}
.btn-dribbble:disabled, .btn-dribbble[disabled], .btn-dribbble.disabled {
  background-color: transparent;
  border-color: #ea4c89;
}
.btn-dribbble.btn-fill {
  color: #FFFFFF;
  background-color: #ea4c89;
  opacity: 0.8;
}
.btn-dribbble.btn-fill:hover, .btn-dribbble.btn-fill:focus, .btn-dribbble.btn-fill:active, .btn-dribbble.btn-fill.active, .open > .btn-dribbble.btn-fill.dropdown-toggle {
  background-color: #ea4c89;
  border-color: #ea4c89;
  color: #FFFFFF;
  opacity: 1;
}
.btn-dribbble.btn-simple {
  color: #ea4c89;
  opacity: 0.8;
}
.btn-dribbble.btn-simple:hover, .btn-dribbble.btn-simple:focus, .btn-dribbble.btn-simple:active, .btn-dribbble.btn-simple.active, .open > .btn-dribbble.btn-simple.dropdown-toggle {
  background-color: transparent;
  color: #ea4c89;
  opacity: 1;
}
.btn-dribbble.btn-simple:hover i, .btn-dribbble.btn-simple:focus i, .btn-dribbble.btn-simple:active i, .btn-dribbble.btn-simple.active i, .open > .btn-dribbble.btn-simple.dropdown-toggle i {
  color: #ea4c89;
  opacity: 1;
}

.btn-github {
  border-color: #333333;
  color: #333333;
}
.btn-github:hover, .btn-github:focus, .btn-github:active, .btn-github.active, .open > .btn-github.dropdown-toggle {
  background-color: #333333;
  border-color: #333333;
  color: #FFFFFF;
}
.btn-github:disabled, .btn-github[disabled], .btn-github.disabled {
  background-color: transparent;
  border-color: #333333;
}
.btn-github.btn-fill {
  color: #FFFFFF;
  background-color: #333333;
  opacity: 0.8;
}
.btn-github.btn-fill:hover, .btn-github.btn-fill:focus, .btn-github.btn-fill:active, .btn-github.btn-fill.active, .open > .btn-github.btn-fill.dropdown-toggle {
  background-color: #333333;
  border-color: #333333;
  color: #FFFFFF;
  opacity: 1;
}
.btn-github.btn-simple {
  color: #333333;
  opacity: 0.8;
}
.btn-github.btn-simple:hover, .btn-github.btn-simple:focus, .btn-github.btn-simple:active, .btn-github.btn-simple.active, .open > .btn-github.btn-simple.dropdown-toggle {
  background-color: transparent;
  color: #333333;
  opacity: 1;
}
.btn-github.btn-simple:hover i, .btn-github.btn-simple:focus i, .btn-github.btn-simple:active i, .btn-github.btn-simple.active i, .open > .btn-github.btn-simple.dropdown-toggle i {
  color: #333333;
  opacity: 1;
}

.btn-youtube {
  border-color: #e52d27;
  color: #e52d27;
}
.btn-youtube:hover, .btn-youtube:focus, .btn-youtube:active, .btn-youtube.active, .open > .btn-youtube.dropdown-toggle {
  background-color: #e52d27;
  border-color: #e52d27;
  color: #FFFFFF;
}
.btn-youtube:disabled, .btn-youtube[disabled], .btn-youtube.disabled {
  background-color: transparent;
  border-color: #e52d27;
}
.btn-youtube.btn-fill {
  color: #FFFFFF;
  background-color: #e52d27;
  opacity: 0.8;
}
.btn-youtube.btn-fill:hover, .btn-youtube.btn-fill:focus, .btn-youtube.btn-fill:active, .btn-youtube.btn-fill.active, .open > .btn-youtube.btn-fill.dropdown-toggle {
  background-color: #e52d27;
  border-color: #e52d27;
  color: #FFFFFF;
  opacity: 1;
}
.btn-youtube.btn-simple {
  color: #e52d27;
  opacity: 0.8;
}
.btn-youtube.btn-simple:hover, .btn-youtube.btn-simple:focus, .btn-youtube.btn-simple:active, .btn-youtube.btn-simple.active, .open > .btn-youtube.btn-simple.dropdown-toggle {
  background-color: transparent;
  color: #e52d27;
  opacity: 1;
}
.btn-youtube.btn-simple:hover i, .btn-youtube.btn-simple:focus i, .btn-youtube.btn-simple:active i, .btn-youtube.btn-simple.active i, .open > .btn-youtube.btn-simple.dropdown-toggle i {
  color: #e52d27;
  opacity: 1;
}

.btn-instagram {
  border-color: #125688;
  color: #125688;
}
.btn-instagram:hover, .btn-instagram:focus, .btn-instagram:active, .btn-instagram.active, .open > .btn-instagram.dropdown-toggle {
  background-color: #125688;
  border-color: #125688;
  color: #FFFFFF;
}
.btn-instagram:disabled, .btn-instagram[disabled], .btn-instagram.disabled {
  background-color: transparent;
  border-color: #125688;
}
.btn-instagram.btn-fill {
  color: #FFFFFF;
  background-color: #125688;
  opacity: 0.8;
}
.btn-instagram.btn-fill:hover, .btn-instagram.btn-fill:focus, .btn-instagram.btn-fill:active, .btn-instagram.btn-fill.active, .open > .btn-instagram.btn-fill.dropdown-toggle {
  background-color: #125688;
  border-color: #125688;
  color: #FFFFFF;
  opacity: 1;
}
.btn-instagram.btn-simple {
  color: #125688;
  opacity: 0.8;
}
.btn-instagram.btn-simple:hover, .btn-instagram.btn-simple:focus, .btn-instagram.btn-simple:active, .btn-instagram.btn-simple.active, .open > .btn-instagram.btn-simple.dropdown-toggle {
  background-color: transparent;
  color: #125688;
  opacity: 1;
}
.btn-instagram.btn-simple:hover i, .btn-instagram.btn-simple:focus i, .btn-instagram.btn-simple:active i, .btn-instagram.btn-simple.active i, .open > .btn-instagram.btn-simple.dropdown-toggle i {
  color: #125688;
  opacity: 1;
}

.btn-reddit {
  border-color: #ff4500;
  color: #ff4500;
}
.btn-reddit:hover, .btn-reddit:focus, .btn-reddit:active, .btn-reddit.active, .open > .btn-reddit.dropdown-toggle {
  background-color: #ff4500;
  border-color: #ff4500;
  color: #FFFFFF;
}
.btn-reddit:disabled, .btn-reddit[disabled], .btn-reddit.disabled {
  background-color: transparent;
  border-color: #ff4500;
}
.btn-reddit.btn-fill {
  color: #FFFFFF;
  background-color: #ff4500;
  opacity: 0.8;
}
.btn-reddit.btn-fill:hover, .btn-reddit.btn-fill:focus, .btn-reddit.btn-fill:active, .btn-reddit.btn-fill.active, .open > .btn-reddit.btn-fill.dropdown-toggle {
  background-color: #ff4500;
  border-color: #ff4500;
  color: #FFFFFF;
  opacity: 1;
}
.btn-reddit.btn-simple {
  color: #ff4500;
  opacity: 0.8;
}
.btn-reddit.btn-simple:hover, .btn-reddit.btn-simple:focus, .btn-reddit.btn-simple:active, .btn-reddit.btn-simple.active, .open > .btn-reddit.btn-simple.dropdown-toggle {
  background-color: transparent;
  color: #ff4500;
  opacity: 1;
}
.btn-reddit.btn-simple:hover i, .btn-reddit.btn-simple:focus i, .btn-reddit.btn-simple:active i, .btn-reddit.btn-simple.active i, .open > .btn-reddit.btn-simple.dropdown-toggle i {
  color: #ff4500;
  opacity: 1;
}

.btn-tumblr {
  border-color: #35465c;
  color: #35465c;
}
.btn-tumblr:hover, .btn-tumblr:focus, .btn-tumblr:active, .btn-tumblr.active, .open > .btn-tumblr.dropdown-toggle {
  background-color: #35465c;
  border-color: #35465c;
  color: #FFFFFF;
}
.btn-tumblr:disabled, .btn-tumblr[disabled], .btn-tumblr.disabled {
  background-color: transparent;
  border-color: #35465c;
}
.btn-tumblr.btn-fill {
  color: #FFFFFF;
  background-color: #35465c;
  opacity: 0.8;
}
.btn-tumblr.btn-fill:hover, .btn-tumblr.btn-fill:focus, .btn-tumblr.btn-fill:active, .btn-tumblr.btn-fill.active, .open > .btn-tumblr.btn-fill.dropdown-toggle {
  background-color: #35465c;
  border-color: #35465c;
  color: #FFFFFF;
  opacity: 1;
}
.btn-tumblr.btn-simple {
  color: #35465c;
  opacity: 0.8;
}
.btn-tumblr.btn-simple:hover, .btn-tumblr.btn-simple:focus, .btn-tumblr.btn-simple:active, .btn-tumblr.btn-simple.active, .open > .btn-tumblr.btn-simple.dropdown-toggle {
  background-color: transparent;
  color: #35465c;
  opacity: 1;
}
.btn-tumblr.btn-simple:hover i, .btn-tumblr.btn-simple:focus i, .btn-tumblr.btn-simple:active i, .btn-tumblr.btn-simple.active i, .open > .btn-tumblr.btn-simple.dropdown-toggle i {
  color: #35465c;
  opacity: 1;
}

.label-facebook {
  background-color: #3b5998;
}

.label-twitter {
  background-color: #55acee;
}

.label-pinterest {
  background-color: #cc2127;
}

.label-google {
  background-color: #dd4b39;
}

.label-linkedin {
  background-color: #0976b4;
}

.label-dribbble {
  background-color: #ea4c89;
}

.label-github {
  background-color: #333333;
}

.label-youtube {
  background-color: #e52d27;
}

.label-instagram {
  background-color: #125688;
}

.label-reddit {
  background-color: #ff4500;
}

.label-tumblr {
  background-color: #35465c;
}

.icon-facebook {
  color: #3b5998;
}

.icon-twitter {
  color: #55acee;
}

.icon-pinterest {
  color: #cc2127;
}

.icon-google {
  color: #dd4b39;
}

.icon-linkedin {
  color: #0976b4;
}

.icon-dribbble {
  color: #ea4c89;
}

.icon-github {
  color: #333333;
}

.icon-youtube {
  color: #e52d27;
}

.icon-instagram {
  color: #125688;
}

.icon-reddit {
  color: #ff4500;
}

.icon-tumblr {
  color: #35465c;
}

/*
 * bootstrap-tagsinput v0.8.0
 *
 */
.bootstrap-tagsinput {
  display: inline-block;
  padding: 4px 6px;
  max-width: 100%;
  line-height: 22px;
}
.bootstrap-tagsinput input {
  border: none;
  box-shadow: none;
  outline: none;
  background-color: transparent;
  margin: 0;
  width: 72px;
  max-width: inherit;
}
.bootstrap-tagsinput input:focus {
  border: none;
  box-shadow: none;
}
.bootstrap-tagsinput.form-control input::-moz-placeholder {
  color: #777;
  opacity: 1;
}
.bootstrap-tagsinput.form-control input:-ms-input-placeholder, .bootstrap-tagsinput.form-control input::-webkit-input-placeholder {
  color: #777;
}
.bootstrap-tagsinput .tag {
  cursor: pointer;
  margin: 5px 3px 5px 0;
  position: relative;
  padding: 3px 8px;
  border-radius: 12px;
  color: #FFFFFF;
  font-weight: 500;
  font-size: 0.75em;
  text-transform: uppercase;
  display: inline-block;
  line-height: 1.5em;
  padding-left: 0.8em;
}
.bootstrap-tagsinput .tag.tag-primary .tag {
  background-color: #7A9E9F;
  color: #FFFFFF;
}
.bootstrap-tagsinput .tag.tag-primary .tag .tagsinput-remove-link {
  color: #FFFFFF;
}
.bootstrap-tagsinput .tag.tag-primary .tagsinput-add {
  color: #7A9E9F;
}
.bootstrap-tagsinput .tag.tag-info .tag {
  background-color: #68B3C8;
  color: #FFFFFF;
}
.bootstrap-tagsinput .tag.tag-info .tag .tagsinput-remove-link {
  color: #FFFFFF;
}
.bootstrap-tagsinput .tag.tag-info .tagsinput-add {
  color: #68B3C8;
}
.bootstrap-tagsinput .tag.tag-success .tag {
  background-color: #7AC29A;
  color: #FFFFFF;
}
.bootstrap-tagsinput .tag.tag-success .tag .tagsinput-remove-link {
  color: #FFFFFF;
}
.bootstrap-tagsinput .tag.tag-success .tagsinput-add {
  color: #7AC29A;
}
.bootstrap-tagsinput .tag.tag-warning .tag {
  background-color: #F3BB45;
  color: #FFFFFF;
}
.bootstrap-tagsinput .tag.tag-warning .tag .tagsinput-remove-link {
  color: #FFFFFF;
}
.bootstrap-tagsinput .tag.tag-warning .tagsinput-add {
  color: #F3BB45;
}
.bootstrap-tagsinput .tag.tag-danger .tag {
  background-color: #EB5E28;
  color: #FFFFFF;
}
.bootstrap-tagsinput .tag.tag-danger .tag .tagsinput-remove-link {
  color: #FFFFFF;
}
.bootstrap-tagsinput .tag.tag-danger .tagsinput-add {
  color: #EB5E28;
}
.bootstrap-tagsinput .tag:hover {
  padding-right: 18px;
}
.bootstrap-tagsinput .tag:hover [data-role="remove"] {
  opacity: 1;
  padding-right: 6px;
}
.bootstrap-tagsinput .tag [data-role="remove"] {
  cursor: pointer;
  position: absolute;
  top: 3px;
  right: 0;
  opacity: 0;
}
.bootstrap-tagsinput .tag [data-role="remove"]:after {
  content: "x";
  padding: 0px 2px;
}

.panel {
  background-color: #F9F7F3;
  border: 0 none;
  border-radius: 3px;
  font-size: 18;
  font-weight: 300;
  line-height: 1.6em;
  margin-top: 10px;
  padding: 7px 10px;
}

.panel-border {
  border: 1px solid #e8e7e3;
}

.panel-default .panel-heading {
  background-color: #F9F7F3;
  border: 0 none;
  border-radius: 3px;
}
.panel-default a {
  color: #66615b;
}
.panel-default a:hover, .panel-default a:active, .panel-default a:focus {
  color: #EB5E28;
}
.panel-default a .panel-title > i {
  float: right;
}
.panel-default a[aria-expanded="true"] .panel-title > i,
.panel-default a.expanded .panel-title > i {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);
  -webkit-transform: rotate(180deg);
  -ms-transform: rotate(180deg);
  transform: rotate(180deg);
}

.panel-default > .panel-heading + .panel-collapse > .panel-body {
  border: 0 none;
}

.carousel-control {
  width: 8%;
}

.carousel-control .icon-prev, .carousel-control .icon-next, .carousel-control .fa, .carousel-control .fa {
  display: inline-block;
  position: absolute;
  top: 50%;
  z-index: 5;
}

.carousel-control .fa {
  font-size: 35px;
}

.carousel-control.left, .carousel-control.right {
  background-image: none;
}

.icon-property, .btn-rotate i, .btn-magnify i, .btn-move-left i, .btn-move-right i {
  -webkit-transition: all 300ms cubic-bezier(0.34, 1.61, 0.7, 1);
  -moz-transition: all 300ms cubic-bezier(0.34, 1.61, 0.7, 1);
  -o-transition: all 300ms cubic-bezier(0.34, 1.61, 0.7, 1);
  -ms-transition: all 300ms cubic-bezier(0.34, 1.61, 0.7, 1);
  transition: all 300ms cubic-bezier(0.34, 1.61, 0.7, 1);
  position: relative;
  display: inline-block;
}

.btn-rotate:hover i, .btn-rotate:focus i {
  filter: progid:DXImageTransform.Microsoft.BasicImage(rotation=2);
  -webkit-transform: rotate(53deg);
  -ms-transform: rotate(53deg);
  transform: rotate(53deg);
}

.btn-magnify:hover i, .btn-magnify:focus i {
  -webkit-transform: scale(1.22);
  -moz-transform: scale(1.22);
  -o-transform: scale(1.22);
  -ms-transform: scale(1.22);
  transform: scale(1.22);
}

.btn-move-left i {
  margin-right: 0;
}
.btn-move-left:hover i, .btn-move-left:focus i {
  -webkit-transform: translate3d(-5px, 0, 0);
  -moz-transform: translate3d(-5px, 0, 0);
  -o-transform: translate3d(-5px, 0, 0);
  -ms-transform: translate3d(-5px, 0, 0);
  transform: translate3d(-5px, 0, 0);
}

.btn-move-right i {
  margin-right: 0;
}
.btn-move-right:hover i, .btn-move-right:focus i {
  -webkit-transform: translate3d(5px, 0, 0);
  -moz-transform: translate3d(5px, 0, 0);
  -o-transform: translate3d(5px, 0, 0);
  -ms-transform: translate3d(5px, 0, 0);
  transform: translate3d(5px, 0, 0);
}

/* ========================================================================
 * bootstrap-switch - v3.3.2
 * http://www.bootstrap-switch.org
 * ========================================================================
 * Copyright 2012-2013 Mattia Larentis
 *
 * ========================================================================
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 * ========================================================================
 */
.bootstrap-switch {
  display: inline-block;
  direction: ltr;
  cursor: pointer;
  border-radius: 30px;
  border: 0;
  position: relative;
  text-align: left;
  overflow: hidden;
  margin-bottom: 5px;
  line-height: 8px;
  width: 61px !important;
  height: 26px;
  outline: none;
  z-index: 0;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  vertical-align: middle;
  -webkit-transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
  transition: border-color ease-in-out .15s, box-shadow ease-in-out .15s;
}

.bootstrap-switch .bootstrap-switch-container {
  display: inline-flex;
  top: 0;
  height: 26px;
  border-radius: 4px;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translate3d(0, 0, 0);
  width: 100px !important;
}

.bootstrap-switch .bootstrap-switch-handle-on,
.bootstrap-switch .bootstrap-switch-handle-off,
.bootstrap-switch .bootstrap-switch-label {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  cursor: pointer;
  display: inline-block !important;
  height: 100%;
  color: #fff;
  padding: 6px 12px;
  font-size: 11px;
  text-indent: -5px;
  line-height: 15px;
  -webkit-transition: 0.25s ease-out;
  transition: 0.25s ease-out;
}

.bootstrap-switch .bootstrap-switch-handle-on,
.bootstrap-switch .bootstrap-switch-handle-off {
  text-align: center;
  z-index: 1;
  float: left;
  width: 50% !important;
  background-color: #66615B;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-primary,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-primary {
  color: #fff;
  background: #7A9E9F;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-info,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-info {
  color: #fff;
  background: #447DF7;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-success,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-success {
  color: #fff;
  background: #7AC29A;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-warning,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-warning {
  background: #FFA534;
  color: #fff;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-danger,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-danger {
  color: #fff;
  background: #FB404B;
}

.bootstrap-switch .bootstrap-switch-handle-on.bootstrap-switch-default,
.bootstrap-switch .bootstrap-switch-handle-off.bootstrap-switch-default {
  color: #fff;
  background: #cfcfca;
}

.bootstrap-switch .bootstrap-switch-label {
  text-align: center;
  z-index: 100;
  color: #333333;
  background: #ffffff;
  width: 22px !important;
  height: 22px;
  margin: 2px -11px;
  border-radius: 12px;
  position: relative;
  float: left;
  padding: 0;
  background-color: #FFFFFF;
  box-shadow: 0 1px 1px #FFFFFF inset, 0 1px 1px rgba(0, 0, 0, 0.25);
}

.bootstrap-switch .bootstrap-switch-handle-on {
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px;
}

.bootstrap-switch .bootstrap-switch-handle-off {
  text-indent: 6px;
}

.bootstrap-switch input[type='radio'],
.bootstrap-switch input[type='checkbox'] {
  position: absolute !important;
  top: 0;
  left: 0;
  opacity: 0;
  filter: alpha(opacity=0);
  z-index: -1;
}

.bootstrap-switch input[type='radio'].form-control,
.bootstrap-switch input[type='checkbox'].form-control {
  height: auto;
}

.bootstrap-switch.bootstrap-switch-mini .bootstrap-switch-handle-on,
.bootstrap-switch.bootstrap-switch-mini .bootstrap-switch-handle-off,
.bootstrap-switch.bootstrap-switch-mini .bootstrap-switch-label {
  padding: 1px 5px;
  font-size: 12px;
  line-height: 1.5;
}

.bootstrap-switch.bootstrap-switch-small .bootstrap-switch-handle-on,
.bootstrap-switch.bootstrap-switch-small .bootstrap-switch-handle-off,
.bootstrap-switch.bootstrap-switch-small .bootstrap-switch-label {
  padding: 5px 10px;
  font-size: 12px;
  line-height: 1.5;
}

.bootstrap-switch.bootstrap-switch-large .bootstrap-switch-handle-on,
.bootstrap-switch.bootstrap-switch-large .bootstrap-switch-handle-off,
.bootstrap-switch.bootstrap-switch-large .bootstrap-switch-label {
  padding: 6px 16px;
  font-size: 18px;
  line-height: 1.33;
}

.bootstrap-switch.bootstrap-switch-disabled,
.bootstrap-switch.bootstrap-switch-readonly,
.bootstrap-switch.bootstrap-switch-indeterminate {
  cursor: default !important;
}

.bootstrap-switch.bootstrap-switch-disabled .bootstrap-switch-handle-on,
.bootstrap-switch.bootstrap-switch-readonly .bootstrap-switch-handle-on,
.bootstrap-switch.bootstrap-switch-indeterminate .bootstrap-switch-handle-on,
.bootstrap-switch.bootstrap-switch-disabled .bootstrap-switch-handle-off,
.bootstrap-switch.bootstrap-switch-readonly .bootstrap-switch-handle-off,
.bootstrap-switch.bootstrap-switch-indeterminate .bootstrap-switch-handle-off,
.bootstrap-switch.bootstrap-switch-disabled .bootstrap-switch-label,
.bootstrap-switch.bootstrap-switch-readonly .bootstrap-switch-label,
.bootstrap-switch.bootstrap-switch-indeterminate .bootstrap-switch-label {
  opacity: 0.5;
  filter: alpha(opacity=50);
  cursor: default !important;
}

.bootstrap-switch.bootstrap-switch-animate .bootstrap-switch-container {
  -webkit-transition: margin-left 0.5s;
  transition: margin-left 0.5s;
}

.bootstrap-switch.bootstrap-switch-inverse .bootstrap-switch-handle-on {
  border-bottom-left-radius: 0;
  border-top-left-radius: 0;
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}

.bootstrap-switch.bootstrap-switch-inverse .bootstrap-switch-handle-off {
  border-bottom-right-radius: 0;
  border-top-right-radius: 0;
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px;
}

.bootstrap-switch.bootstrap-switch-on .bootstrap-switch-container {
  margin-left: -2px !important;
}

.bootstrap-switch.bootstrap-switch-off .bootstrap-switch-container {
  margin-left: -37px !important;
}

.bootstrap-switch.bootstrap-switch-on:hover .bootstrap-switch-label {
  width: 26px !important;
  margin: 2px -15px;
}

.bootstrap-switch.bootstrap-switch-off:hover .bootstrap-switch-label {
  width: 26px !important;
  margin: 2px -15px -13px -11px;
}

.bootstrap-switch.bootstrap-switch-on .bootstrap-switch-handle-off {
  background-color: #66615B;
}

.bootstrap-switch.bootstrap-switch-off .bootstrap-switch-handle-on {
  background-color: #cfcfca;
}

.bootstrap-switch.bootstrap-switch-on .bootstrap-switch-danger ~ .bootstrap-switch-default {
  background-color: #FB404B;
}

.bootstrap-switch.bootstrap-switch-on .bootstrap-switch-warning ~ .bootstrap-switch-default {
  background-color: #FFA534;
}

.bootstrap-switch.bootstrap-switch-on .bootstrap-switch-success ~ .bootstrap-switch-default {
  background-color: #7AC29A;
}

.bootstrap-switch.bootstrap-switch-on .bootstrap-switch-primary ~ .bootstrap-switch-default {
  background-color: #7A9E9F;
}

.bootstrap-switch.bootstrap-switch-on .bootstrap-switch-info ~ .bootstrap-switch-default {
  background-color: #447DF7;
}

.bootstrap-switch.bootstrap-switch-off .bootstrap-switch-danger,
.bootstrap-switch.bootstrap-switch-off .bootstrap-switch-primary,
.bootstrap-switch.bootstrap-switch-off .bootstrap-switch-info,
.bootstrap-switch.bootstrap-switch-off .bootstrap-switch-warning,
.bootstrap-switch.bootstrap-switch-off .bootstrap-switch-success {
  background-color: #cfcfca;
}

.wrapper {
  position: relative;
  top: 0;
  height: 100vh;
}
.wrapper.wrapper-full-page {
  height: auto;
  min-height: 100vh;
}

.full-page:after, .full-page:before {
  display: block;
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 2;
}
.full-page:after {
  background: #5e5e5e;
  z-index: 3;
  opacity: 1;
}
.full-page > .content,
.full-page > .footer {
  position: relative;
  z-index: 4;
}
.full-page > .content {
  min-height: calc(100vh - 70px);
}
.full-page .full-page-background {
  position: absolute;
  z-index: 1;
  height: 100%;
  width: 100%;
  display: block;
  top: 0;
  left: 0;
  background-size: cover;
  background-position: center center;
}
.full-page[data-image]:after, .full-page.has-image:after {
  opacity: .7;
}
.full-page[data-color="blue"]:after {
  background-color: rgba(96, 133, 134, 0.7);
}
.full-page[data-color="azure"]:after {
  background-color: rgba(68, 160, 185, 0.7);
}
.full-page[data-color="green"]:after {
  background-color: rgba(87, 178, 127, 0.7);
}
.full-page[data-color="orange"]:after {
  background-color: rgba(240, 169, 21, 0.7);
}
.full-page[data-color="red"]:after {
  background-color: rgba(205, 71, 19, 0.7);
}
.full-page .footer nav > ul a:not(.btn),
.full-page .footer .copyright,
.full-page .footer .copyright a {
  color: #FFFFFF;
  font-size: 14px;
}

.login-page > .content,
.lock-page > .content {
  padding-top: 20vh;
}

.lock-page .card-lock {
  text-align: center;
  width: 300px;
  margin: 30px auto 0;
  padding: 30px;
  position: absolute;
  left: 50%;
  margin-left: -150px;
  display: block;
}

.register-page .header-text {
  text-align: center;
  padding: 60px 0 40px;
}
.register-page .header-text h4 {
  margin-top: 10px;
}
.register-page .header-text hr {
  opacity: .3;
  margin-top: 30px;
}
.register-page .media {
  /*         color: $white-color; */
  margin-bottom: 45px;
}
.register-page .media .icon {
  float: left;
  margin-right: 5px;
}
.register-page .media i {
  font-size: 2.6em;
}
.register-page .media h4 {
  margin: 0 0 5px 0;
}
.register-page .form-group {
  margin-bottom: 25px;
}
.register-page .card .content {
  padding-top: 0;
  padding-bottom: 0;
}

/*             Navigation Tabs                 */
.nav-tabs-navigation {
  text-align: center;
  border-bottom: 1px solid #F1EAE0;
  margin-bottom: 30px;
}
.nav-tabs-navigation .nav > li > a {
  padding-bottom: 20px;
}

.nav-tabs-wrapper {
  display: inline-block;
  margin-bottom: -6px;
  margin-left: 1.25%;
  margin-right: 1.25%;
  position: relative;
  width: auto;
}

.nav-tabs {
  border-bottom: 0 none;
  font-size: 14px;
  font-weight: 600;
}
.nav-tabs > li > a {
  border: 0 none;
  color: #A49E93;
}
.nav-tabs > li > a:hover {
  color: #66615b;
}
.nav-tabs > li.active {
  color: #66615b;
  position: relative;
}
.nav-tabs > li.active > a,
.nav-tabs > li.active > a:hover,
.nav-tabs > li.active > a:focus {
  background-color: transparent;
  border: 0 none;
}
.nav-tabs > li.active :after {
  border-bottom: 11px solid #FFFFFF;
  border-left: 11px solid transparent;
  border-right: 11px solid transparent;
  content: "";
  display: inline-block;
  position: absolute;
  right: 40%;
  bottom: 0;
}
.nav-tabs > li.active :before {
  border-bottom: 11px solid #F1EAE0;
  border-left: 11px solid transparent;
  border-right: 11px solid transparent;
  content: "";
  display: inline-block;
  position: absolute;
  right: 40%;
  bottom: 1px;
}

.nav-stacked {
  border-right: 1px solid #F1EAE0;
  font-size: 14px;
  font-weight: 600;
  padding: 20px 0;
}
.nav-stacked > li > a {
  color: #A49E93;
  padding: 7px 25px;
  text-align: right;
}
.nav-stacked > li > a:hover {
  color: #66615b;
}
.nav-stacked > li.active > a {
  color: #66615b;
}
.nav-stacked > li.active :after {
  border-right: 11px solid #FFFFFF;
  border-top: 11px solid transparent;
  border-bottom: 11px solid transparent;
  content: "";
  display: inline-block;
  position: absolute;
  right: -1px;
  bottom: 7px;
}
.nav-stacked > li.active :before {
  border-right: 11px solid #F1EAE0;
  border-top: 11px solid transparent;
  border-bottom: 11px solid transparent;
  content: "";
  display: inline-block;
  position: absolute;
  right: 0;
  bottom: 7px;
}

.left-vertical-tabs {
  width: 170px;
  float: left;
}

.right-text-tabs {
  width: calc(100% - 170px);
  float: left;
  padding: 6px 15px;
}

/*             Navigation Pills               */
.nav-pills > li + li {
  margin-left: 0;
}
.nav-pills > li > a {
  border: 2px solid #66615B;
  border-radius: 0;
  color: #66615B;
  font-weight: 600;
  margin-left: -1px;
  padding: 10px 20px;
}
.nav-pills > li.active > a,
.nav-pills > li.active > a:hover,
.nav-pills > li.active > a:focus {
  background-color: #66615B;
  color: #FFFFFF;
}
.nav-pills > li:first-child > a {
  border-radius: 30px 0 0 30px;
  margin: 0;
}
.nav-pills > li:last-child > a {
  border-radius: 0 30px 30px 0;
}

.pagination > li:first-child > a,
.pagination > li:first-child > span,
.pagination > li:last-child > a,
.pagination > li:last-child > span {
  border-radius: 20px;
}

.pagination > li > a,
.pagination > li > span {
  background-color: transparent;
  border: 2px solid #66615B;
  border-radius: 20px;
  color: #66615B;
  height: 36px;
  font-weight: 600;
  margin: 0 2px;
  min-width: 36px;
  padding: 6px;
  line-height: 22px;
  text-align: center;
}

.pagination > li > a:hover,
.pagination > li > a:focus,
.pagination > li > a:active,
.pagination > li.active > a,
.pagination > li.active > span,
.pagination > li.active > a:hover,
.pagination > li.active > span:hover,
.pagination > li.active > a:focus,
.pagination > li.active > span:focus {
  background-color: #66615B;
  border-color: #66615B;
  color: #FFFFFF;
}

.nav-pills-primary > li > a,
.pagination-primary > li > a,
.pagination-primary > li > span,
.pagination-primary > li:first-child > a,
.pagination-primary > li:first-child > span,
.pagination-primary > li:last-child > a,
.pagination-primary > li:last-child > span {
  border: 2px solid #7A9E9F;
  color: #7A9E9F;
}

.nav-pills-primary > li.active > a,
.nav-pills-primary > li.active > a:hover,
.nav-pills-primary > li.active > a:focus,
.pagination-primary > li > a:hover,
.pagination-primary > li > a:focus,
.pagination-primary > li > a:active,
.pagination-primary > li.active > a,
.pagination-primary > li.active > span,
.pagination-primary > li.active > a:hover,
.pagination-primary > li.active > span:hover,
.pagination-primary > li.active > a:focus,
.pagination-primary > li.active > span:focus {
  background-color: #7A9E9F;
  border-color: #7A9E9F;
  color: #FFFFFF;
}

.nav-pills-info > li > a,
.pagination-info > li > a,
.pagination-info > li > span,
.pagination-info > li:first-child > a,
.pagination-info > li:first-child > span,
.pagination-info > li:last-child > a,
.pagination-info > li:last-child > span {
  border: 2px solid #68B3C8;
  color: #68B3C8;
}

.nav-pills-info > li.active > a,
.nav-pills-info > li.active > a:hover,
.nav-pills-info > li.active > a:focus,
.pagination-info > li > a:hover,
.pagination-info > li > a:focus,
.pagination-info > li > a:active,
.pagination-info > li.active > a,
.pagination-info > li.active > span,
.pagination-info > li.active > a:hover,
.pagination-info > li.active > span:hover,
.pagination-info > li.active > a:focus,
.pagination-info > li.active > span:focus {
  background-color: #68B3C8;
  border-color: #68B3C8;
  color: #FFFFFF;
}

.nav-pills-success > li > a,
.pagination-success > li > a,
.pagination-success > li > span,
.pagination-success > li:first-child > a,
.pagination-success > li:first-child > span,
.pagination-success > li:last-child > a,
.pagination-success > li:last-child > span {
  border: 2px solid #7AC29A;
  color: #7AC29A;
}

.nav-pills-success > li.active > a,
.nav-pills-success > li.active > a:hover,
.nav-pills-success > li.active > a:focus,
.pagination-success > li > a:hover,
.pagination-success > li > a:focus,
.pagination-success > li > a:active,
.pagination-success > li.active > a,
.pagination-success > li.active > span,
.pagination-success > li.active > a:hover,
.pagination-success > li.active > span:hover,
.pagination-success > li.active > a:focus,
.pagination-success > li.active > span:focus {
  background-color: #7AC29A;
  border-color: #7AC29A;
  color: #FFFFFF;
}

.nav-pills-warning > li > a,
.pagination-warning > li > a,
.pagination-warning > li > span,
.pagination-warning > li:first-child > a,
.pagination-warning > li:first-child > span,
.pagination-warning > li:last-child > a,
.pagination-warning > li:last-child > span {
  border: 2px solid #F3BB45;
  color: #F3BB45;
}

.nav-pills-warning > li.active > a,
.nav-pills-warning > li.active > a:hover,
.nav-pills-warning > li.active > a:focus,
.pagination-warning > li > a:hover,
.pagination-warning > li > a:focus,
.pagination-warning > li > a:active,
.pagination-warning > li.active > a,
.pagination-warning > li.active > span,
.pagination-warning > li.active > a:hover,
.pagination-warning > li.active > span:hover,
.pagination-warning > li.active > a:focus,
.pagination-warning > li.active > span:focus {
  background-color: #F3BB45;
  border-color: #F3BB45;
  color: #FFFFFF;
}

.nav-pills-danger > li > a,
.pagination-danger > li > a,
.pagination-danger > li > span,
.pagination-danger > li:first-child > a,
.pagination-danger > li:first-child > span,
.pagination-danger > li:last-child > a,
.pagination-danger > li:last-child > span {
  border: 2px solid #EB5E28;
  color: #EB5E28;
}

.nav-pills-danger > li.active > a,
.nav-pills-danger > li.active > a:hover,
.nav-pills-danger > li.active > a:focus,
.pagination-danger > li > a:hover,
.pagination-danger > li > a:focus,
.pagination-danger > li > a:active,
.pagination-danger > li.active > a,
.pagination-danger > li.active > span,
.pagination-danger > li.active > a:hover,
.pagination-danger > li.active > span:hover,
.pagination-danger > li.active > a:focus,
.pagination-danger > li.active > span:focus {
  background-color: #EB5E28;
  border-color: #EB5E28;
  color: #FFFFFF;
}

.pagination > .disabled > span, .pagination > .disabled > span:hover, .pagination > .disabled > span:focus,
.pagination > .disabled > a,
.pagination > .disabled > a:hover,
.pagination > .disabled > a:focus {
  color: #cfcfca;
  border-color: #cfcfca;
}

.chart-circle {
  display: inline-block;
  font-size: 2em;
  height: 160px;
  line-height: 160px;
  margin-top: 30px;
  margin-bottom: 30px;
  position: relative;
  text-align: center;
  width: 160px;
}
.chart-circle canvas {
  position: absolute;
  top: 0;
  left: 0;
}

@media (min-width: 992px) {
  .navbar {
    min-height: 75px;
  }

  .navbar-form {
    margin-top: 17px;
    margin-bottom: 17px;
    padding-left: 5px;
    padding-right: 5px;
  }

  .navbar-fixed {
    width: calc(100% - 260px);
  }

  .close-layer {
    display: none;
  }

  .navbar-nav > li > .dropdown-menu {
    -webkit-transform: translate3d(0, -30px, 0);
    -moz-transform: translate3d(0, -30px, 0);
    -o-transform: translate3d(0, -30px, 0);
    -ms-transform: translate3d(0, -30px, 0);
    transform: translate3d(0, -30px, 0);
    transition: all 0.3s cubic-bezier(0.215, 0.61, 0.355, 1) 0s, opacity 0.3s ease 0s, height 0s linear 0.35s;
    opacity: 0;
    filter: alpha(opacity=0);
    visibility: hidden;
    display: block;
  }
  .navbar-nav > li > .dropdown-menu:before {
    border-bottom: 11px solid #F1EAE0;
    border-left: 11px solid transparent;
    border-right: 11px solid transparent;
    content: "";
    display: inline-block;
    position: absolute;
    right: 12px;
    top: -12px;
  }
  .navbar-nav > li > .dropdown-menu:after {
    border-bottom: 11px solid #FFFFFF;
    border-left: 11px solid transparent;
    border-right: 11px solid transparent;
    content: "";
    display: inline-block;
    position: absolute;
    right: 12px;
    top: -11px;
  }

  .navbar-nav > li.open > .dropdown-menu {
    -webkit-transform: translate3d(0, 0, 0);
    -moz-transform: translate3d(0, 0, 0);
    -o-transform: translate3d(0, 0, 0);
    -ms-transform: translate3d(0, 0, 0);
    transform: translate3d(0, 0, 0);
    opacity: 1;
    filter: alpha(opacity=100);
    visibility: visible;
  }

  .navbar-nav.navbar-left > li > .dropdown-menu:before {
    right: auto;
    left: 12px;
  }

  .navbar-nav.navbar-left > li > .dropdown-menu:after {
    right: auto;
    left: 12px;
  }

  .navbar .navbar-header {
    margin-left: 15px;
  }

  .footer:not(.footer-big) nav > ul li:first-child {
    margin-left: 0;
  }

  body > .navbar-collapse.collapse {
    display: none !important;
  }

  .card form {
    /*[class*="col-"]{
        padding: 6px;
    }*/
  }
  .card form [class*="col-"]:first-child {
    padding-left: 15px;
  }
  .card form [class*="col-"]:last-child {
    padding-right: 15px;
  }

  .sidebar .sidebar-wrapper li.active > a:not([data-toggle="collapse"]):before {
    border-right: 17px solid #cfcfca;
    border-top: 17px solid transparent;
    border-bottom: 17px solid transparent;
    content: "";
    display: inline-block;
    position: absolute;
    right: 0;
    opacity: 1;
    top: 3px;
    transition: opacity 150ms ease-in;
  }
  .sidebar .sidebar-wrapper li.active > a:not([data-toggle="collapse"]):after {
    border-right: 17px solid #f4f3ef;
    border-top: 17px solid transparent;
    border-bottom: 17px solid transparent;
    content: "";
    display: inline-block;
    position: absolute;
    right: -1px;
    opacity: 1;
    top: 3px;
    transition: opacity 150ms ease-in;
  }
}
/*          Changes for small display      */
@media (max-width: 992px) {
  .navbar-form {
    margin: 25px 0 0;
    padding: 0 23px 0;
    float: none !important;
  }

  .bootstrap-select {
    margin-bottom: 10px;
  }

  .main-panel {
    -webkit-transform: translate3d(0px, 0, 0);
    -moz-transform: translate3d(0px, 0, 0);
    -o-transform: translate3d(0px, 0, 0);
    -ms-transform: translate3d(0px, 0, 0);
    transform: translate3d(0px, 0, 0);
    -webkit-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    -moz-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    -o-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    -ms-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    left: 0;
    width: 100%;
  }

  .navbar-transparent {
    padding-top: 15px;
  }

  body {
    position: relative;
  }

  h6 {
    font-size: 1em;
  }

  .wrapper {
    background-color: white;
  }

  .main-panel {
    -webkit-transform: translate3d(0px, 0, 0);
    -moz-transform: translate3d(0px, 0, 0);
    -o-transform: translate3d(0px, 0, 0);
    -ms-transform: translate3d(0px, 0, 0);
    transform: translate3d(0px, 0, 0);
    -webkit-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    -moz-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    -o-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    -ms-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    left: 0;
  }

  .navbar .container,
  .wrapper-full-page {
    left: 0;
    width: 100%;
    -webkit-transform: translate3d(0px, 0, 0);
    -moz-transform: translate3d(0px, 0, 0);
    -o-transform: translate3d(0px, 0, 0);
    -ms-transform: translate3d(0px, 0, 0);
    transform: translate3d(0px, 0, 0);
    -webkit-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    -moz-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    -o-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    -ms-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    position: relative;
  }

  .navbar .navbar-collapse.collapse,
  .navbar .navbar-collapse.collapse.in,
  .navbar .navbar-collapse.collapsing {
    display: none !important;
  }

  .navbar-nav > li {
    float: none;
    position: relative;
    display: block;
  }

  .sidebar,
  .off-canvas-sidebar {
    position: fixed;
    display: block;
    top: 0;
    height: 100%;
    width: 230px;
    right: 0;
    left: auto;
    z-index: 9999;
    visibility: visible;
    background-color: #9A9A9A;
    overflow-y: visible;
    border-top: none;
    text-align: left;
    padding-right: 0px;
    padding-left: 0;
    -webkit-transform: translate3d(230px, 0, 0);
    -moz-transform: translate3d(230px, 0, 0);
    -o-transform: translate3d(230px, 0, 0);
    -ms-transform: translate3d(230px, 0, 0);
    transform: translate3d(230px, 0, 0);
    -webkit-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    -moz-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    -o-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    -ms-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
  }
  .sidebar .sidebar-wrapper,
  .off-canvas-sidebar .sidebar-wrapper {
    width: 230px;
    box-shadow: inset 1px 0px 0px 0px #cfcfca;
  }
  .sidebar .sidebar-wrapper li.active > a:not([data-toggle="collapse"]):before,
  .off-canvas-sidebar .sidebar-wrapper li.active > a:not([data-toggle="collapse"]):before {
    border-left: 12px solid #cfcfca;
    border-top: 12px solid transparent;
    border-bottom: 12px solid transparent;
    content: "";
    display: inline-block;
    position: absolute;
    left: 0;
    top: 8px;
  }
  .sidebar .sidebar-wrapper li.active > a:not([data-toggle="collapse"]):after,
  .off-canvas-sidebar .sidebar-wrapper li.active > a:not([data-toggle="collapse"]):after {
    border-left: 12px solid #f4f3ef;
    border-top: 12px solid transparent;
    border-bottom: 12px solid transparent;
    content: "";
    display: inline-block;
    position: absolute;
    left: -1px;
    top: 8px;
  }
  .sidebar .sidebar-wrapper .dropdown-menu:before, .sidebar .sidebar-wrapper .dropdown-menu:after,
  .off-canvas-sidebar .sidebar-wrapper .dropdown-menu:before,
  .off-canvas-sidebar .sidebar-wrapper .dropdown-menu:after {
    display: none;
  }
  .sidebar .nav,
  .off-canvas-sidebar .nav {
    margin-top: 0;
    position: relative;
    z-index: 4;
  }
  .sidebar .nav > li > a:hover, .sidebar .nav > li > a.active,
  .off-canvas-sidebar .nav > li > a:hover,
  .off-canvas-sidebar .nav > li > a.active {
    color: #484541;
  }
  .sidebar .logo,
  .off-canvas-sidebar .logo {
    box-shadow: inset 1px 0px 0px 0px #cfcfca;
  }
  .sidebar .logo a.logo-mini,
  .off-canvas-sidebar .logo a.logo-mini {
    display: none;
  }
  .sidebar .logo a.logo-normal,
  .off-canvas-sidebar .logo a.logo-normal {
    text-align: center;
  }
  .sidebar .divider,
  .off-canvas-sidebar .divider {
    height: 1px;
    margin: 10px 0;
  }
  .sidebar .nav-mobile-menu .notification,
  .off-canvas-sidebar .nav-mobile-menu .notification {
    float: left;
    line-height: 30px;
    margin-right: 8px;
    font-weight: 600;
  }
  .sidebar .nav-mobile-menu .open .dropdown-menu,
  .off-canvas-sidebar .nav-mobile-menu .open .dropdown-menu {
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    background-color: transparent;
    border: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
    -webkit-transform: none;
    transform: none;
  }
  .sidebar .nav-mobile-menu .open .dropdown-menu > li a,
  .off-canvas-sidebar .nav-mobile-menu .open .dropdown-menu > li a {
    padding-left: 70px;
    margin: 0;
  }
  .sidebar .nav-mobile-menu .open .dropdown-menu > li a:hover, .sidebar .nav-mobile-menu .open .dropdown-menu > li a:active, .sidebar .nav-mobile-menu .open .dropdown-menu > li a:focus,
  .off-canvas-sidebar .nav-mobile-menu .open .dropdown-menu > li a:hover,
  .off-canvas-sidebar .nav-mobile-menu .open .dropdown-menu > li a:active,
  .off-canvas-sidebar .nav-mobile-menu .open .dropdown-menu > li a:focus {
    background-color: transparent;
    border-radius: 0;
  }
  .sidebar .nav-mobile-menu .dropdown .dropdown-menu,
  .off-canvas-sidebar .nav-mobile-menu .dropdown .dropdown-menu {
    transition: none;
  }
  .sidebar .nav-mobile-menu a,
  .off-canvas-sidebar .nav-mobile-menu a {
    transition: none;
  }

  .off-canvas-sidebar .nav {
    margin-top: 15px;
  }
  .off-canvas-sidebar .nav > li > a {
    line-height: 30px;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    position: relative;
    left: 0;
    opacity: 1;
    white-space: nowrap;
  }

  .navbar-fixed > div {
    -webkit-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    -moz-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    -o-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    -ms-transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
    transition: all 0.33s cubic-bezier(0.685, 0.0473, 0.346, 1);
  }

  .nav-open .main-panel,
  .nav-open .wrapper-full-page {
    left: 0;
    -webkit-transform: translate3d(-230px, 0, 0);
    -moz-transform: translate3d(-230px, 0, 0);
    -o-transform: translate3d(-230px, 0, 0);
    -ms-transform: translate3d(-230px, 0, 0);
    transform: translate3d(-230px, 0, 0);
  }
  .nav-open .navbar-fixed > div {
    -webkit-transform: translate3d(-230px, 0, 0);
    -moz-transform: translate3d(-230px, 0, 0);
    -o-transform: translate3d(-230px, 0, 0);
    -ms-transform: translate3d(-230px, 0, 0);
    transform: translate3d(-230px, 0, 0);
  }
  .nav-open .navbar-collapse {
    -webkit-transform: translate3d(0px, 0, 0);
    -moz-transform: translate3d(0px, 0, 0);
    -o-transform: translate3d(0px, 0, 0);
    -ms-transform: translate3d(0px, 0, 0);
    transform: translate3d(0px, 0, 0);
  }
  .nav-open .navbar .container {
    -webkit-transform: translate3d(-230px, 0, 0);
    -moz-transform: translate3d(-230px, 0, 0);
    -o-transform: translate3d(-230px, 0, 0);
    -ms-transform: translate3d(-230px, 0, 0);
    transform: translate3d(-230px, 0, 0);
  }
  .nav-open .sidebar,
  .nav-open .off-canvas-sidebar {
    -webkit-transform: translate3d(0px, 0, 0);
    -moz-transform: translate3d(0px, 0, 0);
    -o-transform: translate3d(0px, 0, 0);
    -ms-transform: translate3d(0px, 0, 0);
    transform: translate3d(0px, 0, 0);
  }

  .navbar-toggle .icon-bar {
    display: block;
    position: relative;
    background: #FFFFFF;
    width: 24px;
    height: 2px;
    border-radius: 1px;
    margin: 0 auto;
  }

  .navbar-toggle-black .icon-bar {
    background: #9A9A9A;
  }

  .navbar-header .navbar-toggle {
    margin: 17px 15px 17px 0;
    width: 40px;
    height: 40px;
  }

  .bar1,
  .bar2,
  .bar3 {
    outline: 1px solid transparent;
  }

  .bar1 {
    top: 0px;
    -webkit-animation: topbar-back 500ms linear 0s;
    -moz-animation: topbar-back 500ms linear 0s;
    -ms-animaton: topbar-back 500ms linear 0s;
    animation: topbar-back 500ms 0s;
    -webkit-animation-fill-mode: forwards;
    -moz-animation-fill-mode: forwards;
    -ms-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
  }

  .bar2 {
    opacity: 1;
  }

  .bar3 {
    bottom: 0px;
    -webkit-animation: bottombar-back 500ms linear 0s;
    -moz-animation: bottombar-back 500ms linear 0s;
    -ms-animaton: bottombar-back 500ms linear 0s;
    animation: bottombar-back 500ms 0s;
    -webkit-animation-fill-mode: forwards;
    -moz-animation-fill-mode: forwards;
    -ms-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
  }

  .toggled .bar1 {
    top: 6px;
    -webkit-animation: topbar-x 500ms linear 0s;
    -moz-animation: topbar-x 500ms linear 0s;
    -ms-animaton: topbar-x 500ms linear 0s;
    animation: topbar-x 500ms 0s;
    -webkit-animation-fill-mode: forwards;
    -moz-animation-fill-mode: forwards;
    -ms-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
  }

  .toggled .bar2 {
    opacity: 0;
  }

  .toggled .bar3 {
    bottom: 6px;
    -webkit-animation: bottombar-x 500ms linear 0s;
    -moz-animation: bottombar-x 500ms linear 0s;
    -ms-animaton: bottombar-x 500ms linear 0s;
    animation: bottombar-x 500ms 0s;
    -webkit-animation-fill-mode: forwards;
    -moz-animation-fill-mode: forwards;
    -ms-animation-fill-mode: forwards;
    animation-fill-mode: forwards;
  }

  @keyframes topbar-x {
    0% {
      top: 0px;
      transform: rotate(0deg);
    }
    45% {
      top: 6px;
      transform: rotate(145deg);
    }
    75% {
      transform: rotate(130deg);
    }
    100% {
      transform: rotate(135deg);
    }
  }
  @-webkit-keyframes topbar-x {
    0% {
      top: 0px;
      -webkit-transform: rotate(0deg);
    }
    45% {
      top: 6px;
      -webkit-transform: rotate(145deg);
    }
    75% {
      -webkit-transform: rotate(130deg);
    }
    100% {
      -webkit-transform: rotate(135deg);
    }
  }
  @-moz-keyframes topbar-x {
    0% {
      top: 0px;
      -moz-transform: rotate(0deg);
    }
    45% {
      top: 6px;
      -moz-transform: rotate(145deg);
    }
    75% {
      -moz-transform: rotate(130deg);
    }
    100% {
      -moz-transform: rotate(135deg);
    }
  }
  @keyframes topbar-back {
    0% {
      top: 6px;
      transform: rotate(135deg);
    }
    45% {
      transform: rotate(-10deg);
    }
    75% {
      transform: rotate(5deg);
    }
    100% {
      top: 0px;
      transform: rotate(0);
    }
  }
  @-webkit-keyframes topbar-back {
    0% {
      top: 6px;
      -webkit-transform: rotate(135deg);
    }
    45% {
      -webkit-transform: rotate(-10deg);
    }
    75% {
      -webkit-transform: rotate(5deg);
    }
    100% {
      top: 0px;
      -webkit-transform: rotate(0);
    }
  }
  @-moz-keyframes topbar-back {
    0% {
      top: 6px;
      -moz-transform: rotate(135deg);
    }
    45% {
      -moz-transform: rotate(-10deg);
    }
    75% {
      -moz-transform: rotate(5deg);
    }
    100% {
      top: 0px;
      -moz-transform: rotate(0);
    }
  }
  @keyframes bottombar-x {
    0% {
      bottom: 0px;
      transform: rotate(0deg);
    }
    45% {
      bottom: 6px;
      transform: rotate(-145deg);
    }
    75% {
      transform: rotate(-130deg);
    }
    100% {
      transform: rotate(-135deg);
    }
  }
  @-webkit-keyframes bottombar-x {
    0% {
      bottom: 0px;
      -webkit-transform: rotate(0deg);
    }
    45% {
      bottom: 6px;
      -webkit-transform: rotate(-145deg);
    }
    75% {
      -webkit-transform: rotate(-130deg);
    }
    100% {
      -webkit-transform: rotate(-135deg);
    }
  }
  @-moz-keyframes bottombar-x {
    0% {
      bottom: 0px;
      -moz-transform: rotate(0deg);
    }
    45% {
      bottom: 6px;
      -moz-transform: rotate(-145deg);
    }
    75% {
      -moz-transform: rotate(-130deg);
    }
    100% {
      -moz-transform: rotate(-135deg);
    }
  }
  @keyframes bottombar-back {
    0% {
      bottom: 6px;
      transform: rotate(-135deg);
    }
    45% {
      transform: rotate(10deg);
    }
    75% {
      transform: rotate(-5deg);
    }
    100% {
      bottom: 0px;
      transform: rotate(0);
    }
  }
  @-webkit-keyframes bottombar-back {
    0% {
      bottom: 6px;
      -webkit-transform: rotate(-135deg);
    }
    45% {
      -webkit-transform: rotate(10deg);
    }
    75% {
      -webkit-transform: rotate(-5deg);
    }
    100% {
      bottom: 0px;
      -webkit-transform: rotate(0);
    }
  }
  @-moz-keyframes bottombar-back {
    0% {
      bottom: 6px;
      -moz-transform: rotate(-135deg);
    }
    45% {
      -moz-transform: rotate(10deg);
    }
    75% {
      -moz-transform: rotate(-5deg);
    }
    100% {
      bottom: 0px;
      -moz-transform: rotate(0);
    }
  }
  @-webkit-keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
  @-moz-keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
  @keyframes fadeIn {
    0% {
      opacity: 0;
    }
    100% {
      opacity: 1;
    }
  }
  .dropdown-menu .divider {
    background-color: rgba(229, 229, 229, 0.15);
  }

  .navbar-nav {
    margin: 1px 0;
  }

  .dropdown-menu {
    display: none;
  }

  .navbar-fixed-top {
    -webkit-backface-visibility: hidden;
  }

  .bodyClick {
    height: 100%;
    width: 100%;
    position: fixed;
    opacity: 0;
    top: 0;
    left: auto;
    right: 230px;
    content: "";
    z-index: 9999;
    overflow-x: hidden;
  }

  .form-control + .form-control-feedback {
    margin-top: -8px;
  }

  .navbar-toggle:hover, .navbar-toggle:focus {
    background-color: transparent !important;
  }

  .btn.dropdown-toggle {
    margin-bottom: 0;
  }

  .media-post .author {
    width: 20%;
    float: none !important;
    display: block;
    margin: 0 auto 10px;
  }

  .media-post .media-body {
    width: 100%;
  }

  .close-layer {
    height: 100%;
    width: 100%;
    position: absolute;
    opacity: 0;
    top: 0;
    left: auto;
    display: block;
    content: "";
    z-index: 9999;
    overflow-x: hidden;
    -webkit-transition: all 300ms ease-in;
    -moz-transition: all 300ms ease-in;
    -o-transition: all 300ms ease-in;
    -ms-transition: all 300ms ease-in;
    transition: all 300ms ease-in;
  }
  .close-layer.visible {
    opacity: 1;
  }

  .navbar-collapse.collapse {
    height: 100% !important;
  }

  .navbar-collapse.collapse.in {
    display: block;
  }

  .navbar-header .collapse, .navbar-toggle {
    display: block !important;
  }

  .navbar-header {
    float: none;
  }

  .navbar-nav .open .dropdown-menu {
    position: static;
    float: none;
    width: auto;
    margin-top: 0;
    background-color: transparent;
    border: 0;
    -webkit-box-shadow: none;
    box-shadow: none;
  }

  .main-panel > .content {
    padding-left: 0;
    padding-right: 0;
  }

  .nav .open > a, .nav .open > a:focus, .nav .open > a:hover {
    background-color: transparent;
  }

  .footer .copyright {
    padding: 10px 15px;
    width: 100%;
    text-align: center;
  }

  .footer nav.pull-left {
    float: none !important;
  }

  .navbar-minimize {
    display: none;
  }

  .column-sizing .form-control {
    margin-bottom: 15px;
  }
}
@media (min-width: 992px) {
  .table-full-width {
    margin-left: -15px;
    margin-right: -15px;
  }

  .table-responsive {
    overflow: visible;
  }
}
@media (max-width: 991px) {
  .table-responsive {
    width: 100%;
    margin-bottom: 15px;
    border: 1px solid #cfcfca;
    overflow-x: scroll;
    overflow-y: hidden;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    -webkit-overflow-scrolling: touch;
  }

  .timeline:before {
    left: 5%;
  }
  .timeline > li > .timeline-badge {
    left: 5%;
  }
  .timeline > li .timeline-panel {
    float: right;
    width: 89%;
  }
  .timeline > li .timeline-panel:before {
    border-left-width: 0;
    border-right-width: 15px;
    left: -15px;
    right: auto;
  }
  .timeline > li .timeline-panel:after {
    border-left-width: 0;
    border-right-width: 14px;
    left: -14px;
    right: auto;
  }
  .timeline h6 {
    font-size: 0.9em;
  }
  .timeline > .timeline-inverted .timeline-panel {
    width: 89%;
  }
}
@media (max-width: 550px) {
  .footer nav ul > li:first-child {
    padding-top: 16px;
  }
  .footer nav ul li {
    display: block;
    margin: 0;
    padding: 5px 15px;
  }
}
