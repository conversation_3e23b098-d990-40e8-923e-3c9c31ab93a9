# Webapp

src/
-main.js - File principale
-sidebarLinks - menu Sidebar
-utils.js - utility generiche
+assets/            - sorgenti sass stile
+components/

+data/
 -api.js            - classe gestione chiamate a api
 -auth.js           - funzioni chiamate api auth
 -config.js         - dati di configurazione url, etc..
 -scuole.js         - funzioni chiamate api scuole
+filters/
+i18n/
 -index.js          - gestione multilingua
 -element-ui        - traduzioni element
+routes
 -routes.js         - definizione routes
                    meta: {
                        conditionalRoute: true/false,           - filtra la route in base a scuola.[nomeservizio] true/false
                        authRequired: true,                     - imposta richiesta auth
                        routeName: 'annotazioni'                - associazione con nome servizio scuola
                    }
+store
 -index.js          - file principale istanza vuex
 +modules/
  -auth.js          - gestione utente loggato
  -main.js          - gestione dati app
  -argomenti.js     - gestione sezione argomenti
  -voti.js          - gestione sezione voti
  -compiti.js       - gestione sezione compiti
  -scuole.js        - gestione dati api scuole (materie, sezioni abilitate)
  -studenti.js      - gestione dati studenti (anni scolastici, dati profilo)
+