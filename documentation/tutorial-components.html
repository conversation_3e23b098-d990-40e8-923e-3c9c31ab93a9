<!doctype html>
<html lang="en">
<head>
	<meta charset="utf-8" />
	<link rel="apple-touch-icon" sizes="76x76" href="img/apple-icon.png">
	<link rel="icon" type="image/png" sizes="96x96" href="img/favicon.png">

	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />

	<title>Components - Vue Paper Dashboard Pro by Creative Tim</title>

	<meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0' name='viewport' />
    <meta name="viewport" content="width=device-width" />

	<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.7/css/bootstrap.min.css" integrity="sha384-BVYiiSIFeK1dGmJRAkycuHAHRg32OmUcww7on3RYdg4Va+PmSTsz/K68vbdEjh4u" crossorigin="anonymous">

    <link href="css/paper-dashboard.css" rel="stylesheet" />
    <link href="css/documentation.css" rel="stylesheet" />

    <!--  Fonts and icons     -->
    <link href="http://maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css" rel="stylesheet">
    <link href='https://fonts.googleapis.com/css?family=Muli:400,300' rel='stylesheet' type='text/css'>

</head>

<body>

<nav class="navbar navbar-transparent navbar-fixed-top" role="navigation">
  <div class="container">
	<!-- Brand and toggle get grouped for better mobile display -->
	<div class="navbar-header">
	  <button id="menu-toggle" type="button" class="navbar-toggle">
		<span class="sr-only">Toggle navigation</span>
		<span class="icon-bar bar1"></span>
		<span class="icon-bar bar2"></span>
		<span class="icon-bar bar3"></span>
	  </button>
	  <a href="https://www.creative-tim.com">
		   <div class="logo-container">
				<div class="logo">
					<img src="img/tim-logo.png" alt="Creative Tim Logo">
				</div>
				<div class="brand">
					Creative Tim
				</div>
			</div>
	  </a>
	</div>

	<!-- Collect the nav links, forms, and other content for toggling -->
	<div class="collapse navbar-collapse">
	  <ul  class="nav navbar-nav navbar-right">
			<li>
				<a href="https://github.com/creativetimofficial/vue-paper-dashboard-pro/issues" >
					<i class="fa fa-github-alt"></i> Report Issue
                </a>
			</li>
	   </ul>

	</div><!-- /.navbar-collapse -->
  </div><!-- /.container-fluid -->
</nav>

<div class="header-wrapper header-full">
    <div class="header" style="background-image: url('img/back.jpg')">
        <div class="filter"></div>
        <div class="title-container text-center">
                <h1>Vue Paper Dashboard PRO</h1>
                <h3 class="category">v1.1.0</h3>
                <h4 class="description text-center">We are constantly doing updates for you.</h4>
                <a href="https://cristijora.github.io/paper-dashboard-pro/documentation.html?ref=vue-pkp-pro-doc" class="btn btn-neutral btn-lg btn-round btn-fill" target="_blank">View Documentation</a>
        </div>
    </div>
</div>


</body>
</html>
