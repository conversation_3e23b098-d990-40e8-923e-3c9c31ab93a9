{"name": "webapp.v3", "version": "1.13.0", "private": true, "scripts": {"serve": "node versionUpdate && vue-cli-service serve", "build": "node versionUpdate && vue-cli-service build", "lint": "vue-cli-service lint", "i18n:report": "vue-cli-service i18n:report --src './src/**/*.?(js|vue)' --locales './src/locales/**/*.json'", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"@babel/plugin-syntax-dynamic-import": "^7.2.0", "@chenfengyuan/vue-countdown": "^1.1.3", "@stripe/stripe-js": "^1.29.0", "@vue-stripe/vue-stripe": "^4.5.0", "@vue/cli": "^5.0.8", "@vue/cli-plugin-pwa": "^3.11.0", "axios": "^0.22.0", "chart.js": "^2.9.4", "core-js": "^2.6.5", "dayjs": "^1.8.12", "element-theme-chalk": "^2.10.1", "element-ui": "^2.11.1", "perfect-scrollbar": "^0.8.1", "register-service-worker": "^1.6.2", "sweetalert2": "^8.13.0", "tiptap": "^1.23.0", "tiptap-extensions": "^1.24.0", "vue": "^2.6.10", "vue-chartjs": "^3.4.2", "vue-clickaway": "^2.2.2", "vue-cookie-law": "^1.13.3", "vue-i18n": "^8.0.0", "vue-material-design-icons": "^4.4.0", "vue-nav-tabs": "^0.5.7", "vue-notifyjs": "^0.4.3", "vue-number-transition": "^1.0.2", "vue-router": "^3.0.3", "vue2-transitions": "^0.3.0", "vuex": "^3.0.1"}, "devDependencies": {"@kazupon/vue-i18n-loader": "^0.3.0", "@vue/cli-plugin-babel": "^3.8.0", "@vue/cli-plugin-eslint": "^3.8.0", "@vue/cli-plugin-unit-mocha": "^3.8.0", "@vue/cli-service": "^4.1.2", "@vue/eslint-config-prettier": "^4.0.1", "@vue/test-utils": "1.0.0-beta.29", "babel-eslint": "^10.0.1", "babel-plugin-component": "^1.1.1", "chai": "^4.1.2", "es6-promise": "^4.2.8", "eslint": "^5.16.0", "eslint-plugin-vue": "^5.2.3", "fs": "0.0.1-security", "node-sass": "^4.13.1", "sass-loader": "^7.1.0", "vue-cli-plugin-axios": "0.0.4", "vue-cli-plugin-element": "^1.0.1", "vue-cli-plugin-i18n": "^0.6.0", "vue-template-compiler": "^2.6.10"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/recommended", "@vue/prettier"], "rules": {"prettier/prettier": ["error", {"trailingComma": "none", "singleQuote": true, "semi": false, "htmlWhitespaceSensitivity": "ignore", "arrowParens": "avoid", "singleAttributePerLine": false}], "comma-dangle": ["error", "never"], "vue/attributes-order": "off", "vue/no-v-html": "off", "vue/order-in-components": "off", "vue/max-attributes-per-line": "off", "vue/multiline-html-element-content-newline": "off", "no-console": "off"}, "parserOptions": {"parser": "babel-es<PERSON>"}}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 2 versions"]}