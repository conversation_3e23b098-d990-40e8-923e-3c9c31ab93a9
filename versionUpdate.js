const fs = require('fs')
fs.readFile('./package.json', 'utf8', function(readErr, contents) {
  if (readErr) {
    console.log('ERROR while reading App config file:', readErr)
  }
  const data = JSON.parse(contents)

  if (data.version) {
    let fileContent = `VERSION=${data.version}`
    fs.writeFile('./public/version.txt', fileContent, writeErr => {
      if (writeErr) {
        console.log('ERROR while updating version.txt file:', writeErr)
      }
      console.log(`version.txt updated to ${data.version}`)
    })
    fileContent = `mc_mp_webapp_version{version="${data.version}"} 1`
    fs.writeFile('./public/metrics', fileContent, writeErr => {
      if (writeErr) {
        console.log('ERROR while updating metrics file:', writeErr)
      }
      console.log(`metrics updated to ${data.version}`)
    })
  }
})
