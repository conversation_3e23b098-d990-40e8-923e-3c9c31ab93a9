<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Carrello Web Component</title>
    <style>
        body {
            font-family: 'Ubuntu', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        
        .btn-primary {
            background: #2c5aa0;
            color: white;
        }
        
        .btn-primary:hover {
            background: #1e3d6f;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .product-card {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        
        .product-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .product-price {
            color: #2c5aa0;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Carrello Web Component Test</h1>
        
        <div class="test-section">
            <h3>Test Cart Functionality</h3>
            <p>Click the buttons below to add items to the cart. The cart icon should appear in the bottom right corner.</p>
            
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="addTestProduct1()">
                    Add Product 1 (T-Shirt)
                </button>
                <button class="btn btn-primary" onclick="addTestProduct2()">
                    Add Product 2 (Notebook)
                </button>
                <button class="btn btn-primary" onclick="addTestProduct3()">
                    Add Product 3 (Pen - with options)
                </button>
                <button class="btn btn-secondary" onclick="clearCart()">
                    Clear Cart
                </button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Sample Products</h3>
            <div class="product-card">
                <div class="product-name">School T-Shirt</div>
                <div class="product-price">€15.00</div>
                <p>Basic school uniform t-shirt</p>
            </div>
            
            <div class="product-card">
                <div class="product-name">Exercise Notebook</div>
                <div class="product-price">€3.50</div>
                <p>A5 lined notebook for exercises</p>
            </div>
            
            <div class="product-card">
                <div class="product-name">School Pen</div>
                <div class="product-price">€2.00</div>
                <p>Blue ballpoint pen with school logo</p>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Instructions</h3>
            <ol>
                <li>Click the "Add Product" buttons to add items to the cart</li>
                <li><strong>Watch for animations:</strong> The cart icon should appear in the bottom right corner with a bounce/scale animation</li>
                <li>The cart label should update to show the number of items</li>
                <li>Click on the cart to open the modal and see the items</li>
                <li>Use the "Remove" buttons in the modal to remove items</li>
                <li>Press Escape or click outside the modal to close it</li>
                <li><strong>Animation features:</strong>
                    <ul>
                        <li>Cart icon bounces and changes color when items are added</li>
                        <li>Cart icon has a subtle wiggle animation</li>
                        <li>Smooth hover effects on the cart</li>
                    </ul>
                </li>
            </ol>
        </div>
    </div>

    <!-- Import the CarrelloWebComponent -->
    <script type="module">
        // Import the component
        import './src/components/WebComponents/CarrelloWebComponent.js';
        
        // Create and add the cart component to the page
        const cartComponent = document.createElement('carrello-web-component');
        document.body.appendChild(cartComponent);
        
        // Store reference for testing
        window.testCartComponent = cartComponent;
        
        // Test functions
        window.addTestProduct1 = function() {
            cartComponent.addItem({
                productId: 1,
                productName: 'School T-Shirt',
                quantity: 1,
                selectedOptions: { size: 'M', color: 'Blue' },
                price: 15.00
            });
        };
        
        window.addTestProduct2 = function() {
            cartComponent.addItem({
                productId: 2,
                productName: 'Exercise Notebook',
                quantity: 2,
                selectedOptions: {},
                price: 3.50
            });
        };
        
        window.addTestProduct3 = function() {
            cartComponent.addItem({
                productId: 3,
                productName: 'School Pen',
                quantity: 1,
                selectedOptions: { color: 'Blue', type: 'Ballpoint' },
                price: 2.00
            });
        };
        
        window.clearCart = function() {
            // Clear all items by removing them one by one
            const items = [...cartComponent.state.cartItems];
            items.forEach(item => {
                cartComponent.removeItem(item.id);
            });
        };
        
        // Listen for cart events
        cartComponent.addEventListener('cart-updated', function(event) {
            console.log('Cart updated:', event.detail);
        });
    </script>
</body>
</html>
