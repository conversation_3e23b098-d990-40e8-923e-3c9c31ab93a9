const webpack = require('webpack')
process.env.VUE_APP_VERSION = require('./package.json').version

module.exports = {
  devServer: {
    proxy: {
      '^/api': {
        // target: 'http://127.0.0.1:8000',
        // target: 'https://mp.registroelettronico.com',
        target: 'https://mp-dev.registroelettronico.com',
        // target: 'https://mp-canary.registroelettronico.com',
        secure: false,
        changeOrigin: true,
        pathRewrite: {
          '^/api': ''
        }
      },
      '^/next-api/([^/]+)': {
        target: 'https://demo4.registroelettronico.com',
        secure: false,
        changeOrigin: true,
        pathRewrite: {
          '^/next-api/[^/]+': '/next-api/v1/app_sito'
        },
        router: function(req) {
          // Extract mastercom_id from the URL path
          const match = req.url.match(/^\/next-api\/([^/]+)/)
          if (match && match[1]) {
            const mastercomId = match[1]
            return `https://${mastercomId}.registroelettronico.com`
          }
          return 'https://demo4.registroelettronico.com' // fallback
        }
      }
    }
  },

  pluginOptions: {
    i18n: {
      locale: 'it',
      fallbackLocale: 'en',
      localeDir: 'locales',
      enableInSFC: true
    }
  },

  configureWebpack: {
    devtool: 'source-map',
    plugins: [
      new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/),
      new webpack.DefinePlugin({
        'process.env': {
          PACKAGE_JSON:
            '"' + escape(JSON.stringify(require('./package.json'))) + '"'
        }
      })
    ]
  },

  pwa: {
    workboxPluginMode: 'InjectManifest',
    workboxOptions: {
      swSrc: './src/service-worker.js',
      swDest: 'service-worker.js'
    }
  },

  productionSourceMap: false
}
