variables:
  BUILD_IMAGE: $CI_REGISTRY_IMAGE:build
  IMAGE: $CI_REGISTRY_IMAGE:$CI_COMMIT_REF_NAME

before_script:
  - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY

build:
  stage: build
  script:
    - docker pull $BUILD_IMAGE || true
    - docker build --cache-from $BUILD_IMAGE --tag $BUILD_IMAGE .
    - docker push $BUILD_IMAGE

trivy:
  stage: test
  image: docker:24
  services:
    - name: docker:dind
      entrypoint: ["env", "-u", "DOCKER_HOST"]
      command: ["dockerd-entrypoint.sh"]
  variables:
    IMAGE: $BUILD_IMAGE
    TRIVY_NO_PROGRESS: "true"
    TRIVY_CACHE_DIR: ".trivycache/"
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - export TRIVY_VERSION=$(wget -qO - "https://api.github.com/repos/aquasecurity/trivy/releases/latest" | grep '"tag_name":' | sed -E 's/.*"v([^"]+)".*/\1/')
    - echo $TRIVY_VERSION
    - wget --no-verbose https://github.com/aquasecurity/trivy/releases/download/v${TRIVY_VERSION}/trivy_${TRIVY_VERSION}_Linux-64bit.tar.gz -O - | tar -zxvf -
  allow_failure: true
  script:
    # Build image
    - docker build --pull --tag $BUILD_IMAGE .
    # Build report
    - ./trivy image --exit-code 0 --format template --template "@contrib/gitlab.tpl" -o gl-container-scanning-report.json $BUILD_IMAGE
    # Print report
    - ./trivy image --exit-code 0 --severity HIGH $BUILD_IMAGE
    # Fail on severe vulnerabilities
    - ./trivy image --exit-code 1 --severity CRITICAL $BUILD_IMAGE
  cache:
    paths:
      - .trivycache/
  # Enables https://docs.gitlab.com/ee/user/application_security/container_scanning/ (Container Scanning report is available on GitLab EE Ultimate or GitLab.com Gold)
  artifacts:
    reports:
      container_scanning: gl-container-scanning-report.json

publish:
  stage: deploy
  script:
    - docker pull $BUILD_IMAGE || true
    - docker build --cache-from $BUILD_IMAGE --tag $IMAGE .
    - docker push $IMAGE
    - docker rmi $IMAGE
