<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Detail Availability Test</title>
    <style>
        body {
            font-family: 'Ubuntu', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 4px;
            margin-bottom: 20px;
        }
        .test-info h3 {
            margin: 0 0 10px 0;
            color: #1976d2;
        }
        .test-info p {
            margin: 5px 0;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-info">
            <h3>Product Detail Availability Test</h3>
            <p><strong>Test scenario:</strong> Product with options that have different availability</p>
            <p><strong>Expected behavior:</strong></p>
            <ul>
                <li>Initially: "Add to cart" button should be disabled (required option not selected)</li>
                <li>Select "Blu" option: Button enabled, shows "Colore copertina: Blu", stock shows "30 disponibili"</li>
                <li>Select "Verde" option: Button enabled, shows "Colore copertina: Verde", stock shows "20 disponibili"</li>
                <li>Select "Rosso" option: Button enabled, shows "Colore copertina: Rosso", stock shows "25 disponibili"</li>
                <li>Selected option should be visible in combo box</li>
                <li>Quantity input max should update based on selected option availability</li>
            </ul>
        </div>

        <product-detail-web-component id="productDetail"></product-detail-web-component>
    </div>

    <script type="module">
        // Import the component
        import './src/components/WebComponents/ProductDetailWebComponent.js';

        // Test product data with options having different availability
        const testProduct = {
            id: 31,
            name: 'Quaderno A4 Righe',
            description: 'Quaderno formato A4 con righe, 80 pagine, copertina rigida',
            price: 2.00,
            originalPrice: 2.50,
            currency: 'euro',
            category: 'NEGOZIO',
            available: true,
            stock: 75, // Total stock
            discountActive: true,
            backgroundColor: '#20A4CD',
            textColor: '#000000',
            multipleOrderAllowed: true,
            characteristics: {
                description: 'Colore copertina',
                required: true, // This makes option selection required
                options: [
                    {
                        id: '1',
                        label: 'Blu',
                        available: 30
                    },
                    {
                        id: '2',
                        label: 'Rosso',
                        available: 25
                    },
                    {
                        id: '3',
                        label: 'Verde',
                        available: 20
                    },
                    {
                        id: '4',
                        label: 'Giallo',
                        available: 0 // Out of stock option
                    }
                ]
            }
        };

        // Set the product data
        document.addEventListener('DOMContentLoaded', () => {
            const productDetail = document.getElementById('productDetail');
            productDetail.setProduct(testProduct);

            // Add event listener for add to cart
            productDetail.addEventListener('add-to-cart', (event) => {
                console.log('Add to cart event:', event.detail);
                alert(`Added to cart: ${event.detail.product.name}\nSelected options: ${JSON.stringify(event.detail.selectedOptions)}\nQuantity: ${event.detail.quantity}`);
            });
        });
    </script>
</body>
</html>
