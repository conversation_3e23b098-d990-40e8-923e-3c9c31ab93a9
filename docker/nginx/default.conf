server {
    listen       80;
    server_name  localhost;
    root   /usr/share/nginx/html;
    index  index.html index.htm;

    #charset koi8-r;
    #access_log  /var/log/nginx/host.access.log  main;

    location / {
        try_files $uri $uri/ /index.html;

        # if ($request_method = 'OPTIONS') {
        # add_header 'Access-Control-Allow-Origin' '*';
        #     add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        #     #
        #     # Custom headers and headers various browsers *should* be OK with but aren't
        #     #
        #     add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
        #     #
        #     # Tell client that this pre-flight info is valid for 20 days
        #     #
        #     add_header 'Access-Control-Max-Age' 1728000;
        #     add_header 'Content-Type' 'text/plain; charset=utf-8';
        #     add_header 'Content-Length' 0;
        #     return 204;
        # }
        # if ($request_method = 'POST') {
        #     add_header 'Access-Control-Allow-Origin' '*';
        #     add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        #     add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
        #     add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
        # }
        # if ($request_method = 'GET') {
        #     add_header 'Access-Control-Allow-Origin' '*';
        #     add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
        #     add_header 'Access-Control-Allow-Headers' 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
        #     add_header 'Access-Control-Expose-Headers' 'Content-Length,Content-Range';
        # }
    }

    location /metrics {
         default_type text/plain;
    }

    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }



    # location /utenti/login/ {
    #     # proxy_pass         https://**********:8000/utenti/login/;
    #     proxy_pass         https://mp.registroelettronico.com/utenti/login/;
    #     proxy_set_header   Host $host;
    #     proxy_set_header   X-Real-IP $remote_addr;
    #     proxy_set_header   X-Forwarded-For $proxy_add_x_forwarded_for;
    #     proxy_set_header   X-Forwarded-Host $server_name;
    # }
}
