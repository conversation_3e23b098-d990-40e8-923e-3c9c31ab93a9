// For authoring Nightwatch tests, see
// http://nightwatchjs.org/guide#usage

module.exports = {
  'default e2e tests': function (browser) {
    // automatically uses dev Server port from /config.index.js
    // default: http://localhost:8080
    // see nightwatch.conf.js
    const devServer = browser.globals.devServerURL

    browser
      .url(devServer)
      .waitForElementVisible('.main-panel', 5000)
      .assert.containsText('.navbar-brand', 'Overview')
      .assert.containsText('.logo a', 'SITO GENITORI')
      .assert.elementCount('.card', 10)
      .end()
  }
}
