<?xml version="1.0" encoding="UTF-8" ?>
<testsuites errors="0"
            failures="1"
            tests="1">

  <testsuite name="test"
    errors="0" failures="1" hostname="" id="" package="test" skipped="0"
    tests="1" time="52.02" timestamp="Fri, 01 Jun 2018 15:43:27 GMT">
  
    <testcase name="default e2e tests" classname="test" time="52.02" assertions="3">

    

      <failure message="Testing if element &lt;.logo a&gt; contains text: &#34;SITO GENITORI&#34;.">    at Object.defaultE2eTests [as default e2e tests] (/home/<USER>/workspace/projects/mp-webapp/paper-dashboard-pro-master/test/e2e/specs/test.js:15:15)
    at process._tickCallback (internal/process/next_tick.js:172:11)</failure>

    
    </testcase>
  

  

  
  </testsuite>
</testsuites>
