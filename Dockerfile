FROM node:12.5.0-alpine AS build

# Install required packages
COPY package-lock.json /src/package-lock.json
COPY package.json /src/package.json
RUN cd /src && npm install

# Build source
COPY . /src
RUN cd /src && npm run build


FROM nginx:1.17.1-alpine

# Default Conf
COPY ./docker/nginx/default.conf /etc/nginx/conf.d/default.conf
# Entrypoint
ENV MASTERCOM_ID dev
WORKDIR /usr/share/nginx/html
COPY docker /docker
ENTRYPOINT ["/docker/entrypoint.sh"]
CMD ["nginx", "-g", "daemon off;"]

# Static documents
COPY --from=build /src/dist /usr/share/nginx/html
RUN touch /usr/share/nginx/html/health && touch /usr/share/nginx/html/metrics
