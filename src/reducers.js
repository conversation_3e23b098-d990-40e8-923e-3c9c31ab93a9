function mediaReducer(accumulator, currentValue, currentIndex, dataArray) {
  if (currentIndex + 1 === dataArray.length) {
    let result = (accumulator + currentValue.voto_numerico) / dataArray.length
    return Math.round(result * 100) / 100
  } else {
    return accumulator + currentValue.voto_numerico
  }
}

function mediaPesataReducer(
  accumulator,
  currentValue,
  currentIndex,
  dataArray
) {
  if (currentIndex + 1 === dataArray.length) {
    let result =
      (accumulator + currentValue.voto_numerico * currentValue.valore_peso) /
      dataArray.reduce((accumulator, val) => accumulator + val.valore_peso, 0)
    return Math.round(result * 100) / 100
  } else {
    return accumulator + currentValue.voto_numerico * currentValue.valore_peso
  }
}

export { mediaReducer, mediaPesataReducer }
