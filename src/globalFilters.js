import Moment from 'moment'

const GlobalFilters = {
  install(Vue) {
    Vue.filter('dateDMY', function(value) {
      if (!value) {
        return ''
      }

      value = Moment(value).format('DD-MM-YYYY')
      return value
    })
    Vue.filter('currency', function(value, currency, locale) {
      if (typeof value !== 'number') {
        return value
      }
      var formatter = new Intl.NumberFormat(locale, {
        style: 'currency',
        currency: currency,
        minimumFractionDigits: 2
      })
      return formatter.format(value)
    })
  }
}

export default GlobalFilters
