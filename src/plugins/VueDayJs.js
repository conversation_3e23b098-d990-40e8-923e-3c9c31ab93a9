import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import relativeTime from 'dayjs/plugin/relativeTime'

export default {
  install(Vue, options) {
    const { locale } = options
    dayjs.locale(locale)
    dayjs.extend(isBetween)
    dayjs.extend(isSameOrBefore)
    dayjs.extend(isSameOrAfter)
    dayjs.extend(relativeTime)
    Vue.prototype.$dayjs = dayjs

    Vue.filter('extdate', function(value) {
      if (!value) {
        return ''
      }

      return dayjs(value).format('dddd DD MMMM')
    })

    Vue.filter('simpledate', function(value) {
      if (!value) {
        return ''
      }

      return dayjs(value).format('DD-MM-YYYY')
    })
    Vue.filter('datetime', function(value) {
      if (!value) {
        return ''
      }

      return dayjs(value).format('DD-MM-YYYY HH:mm')
    })
    Vue.filter('fulldate', function(value) {
      if (!value) {
        return ''
      }

      return dayjs(value).format('dddd, DD-MM-YYYY')
    })
    Vue.filter('longdate', function(value) {
      if (!value) {
        return ''
      }

      return dayjs(value).format('dddd, DD MMMM YYYY')
    })

    Vue.filter('simpletime', function(value) {
      if (!value) {
        return ''
      }

      return dayjs(value).format('HH:mm')
    })

    Vue.filter('fulldatetime', function(value) {
      if (!value) {
        return ''
      }
      return dayjs(value).format('dddd, DD-MM-YYYY HH:mm')
    })

    Vue.filter('fromnow', function(value) {
      return dayjs(value).fromNow()
    })
  }
}
