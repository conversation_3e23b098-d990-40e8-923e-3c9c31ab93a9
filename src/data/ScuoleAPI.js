import axios from 'axios'

export { getScuole }

function getScuole() {
  /*
  const url = `${config.BASE_URL}/${config.API_VERSION}/scuole`
  console.log(config)
  console.log(`${config.BASE_URL}`)
  */

  const API_URL = '/api'
  let post = {
    scuola: 2,
    utente: 'nexus::853976',
    password: 'aglihackers73annidiPRIGIONE'
  }

  // post.scuola = 5695
  axios
    .post(`${API_URL}/utenti/login/`, post)
    .then(response => {
      console.log(response)
    })
    .catch(error => console.log(error))
}
