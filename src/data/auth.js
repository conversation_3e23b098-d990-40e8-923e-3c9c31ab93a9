import axios from 'axios'
import { api } from './config'

export {
  authenticate,
  resetPassword,
  isValidJWT,
  validateJWT,
  refreshTokenJWT,
  initLoginScreen
}

/**
 * Authenticate:
 * invia utente + password + scuola a API Login per verifica credenziali.
 */

function authenticate(post) {
  let config = {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers':
        'Origin, X-Requested-With, Content-Type, Accept'
    }
  }

  let result = axios.post(
    `${api.baseUrl}/${api.loginVersion}/utenti/login/`,
    post,
    config
  )
  return result
}

/**
 * resetPassword:
 * invia nuova password a API
 */

function resetPassword(password) {
  let post = {
    password: password
  }
  return axios.post(`${api.BASE_URL}/utenti/aggiorna_password`, post)
}

/**
 * isValidJWT:
 * verifica validità token JWT in base a data di scadenza.
 */

function isValidJWT(jwt) {
  if (!jwt || jwt.split('.').length < 3) {
    // console.log('jwt non conforme')
    return false
  }
  // console.log('jwt ok, test')
  const data = JSON.parse(atob(jwt.split('.')[1]))
  const exp = new Date(data.exp * 1000) // JS deals with dates in milliseconds since epoch
  const now = new Date()
  return now < exp
}

function validateJWT(token, idUtente) {
  let post = {
    token: token
  }

  if (isNaN(idUtente)) {
    // throw new Error('idUtente non conforme')
    // console.log('NaN' + idUtente)
    return false
  }
  // console.log('N')
  return axios.post(
    `${api.baseUrl}/${api.loginVersion}/utenti/${idUtente}/`,
    post
  )
}

function refreshTokenJWT(token, mastercom) {
  let post = {
    token: token
  }
  let url = `${api.baseUrl}/${
    api.loginVersion
  }/utenti/${mastercom}/refresh-token/`
  // console.log('URL', url)
  return axios.post(url, post)
}

function initLoginScreen(mastercom_id) {
  // console.log('URL', `${api.BASE_URL}/mastercom/?mastercom_id=${mastercom_id}`)
  let result = axios.get(
    `${api.baseUrl}/v3/mastercom_nextapi/?mastercom_id=${mastercom_id}`
  )
  return result
}
