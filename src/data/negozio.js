import axios from 'axios'
import { api } from './config'

/**
 * Negozio API Service
 * Handles all API calls related to the negozio (shop) functionality
 */

/**
 * Load negozio products from API
 * @param {string} token - JWT access token
 * @param {string} studentId - Student ID
 * @param {string} schoolYear - School year (e.g., '2024_2025')
 * @param {string} [nextApiUrl] - URL encoded next-api base URL (for mobile)
 * @returns {Promise} - Products list
 */
export function loadProductsFromAPI(
  token,
  studentId,
  schoolYear,
  nextApiUrl = null
) {
  let config, url

  if (nextApiUrl) {
    // Mobile API call through proxy
    config = {
      headers: {
        Authorization: token, // Token without Bearer prefix for mobile
        'Content-Type': 'application/json'
      }
    }

    // Extract mastercom_id from the next_api_url to use with proxy
    const decodedApiUrl = decodeURIComponent(nextApiUrl)
    const mastercomMatch = decodedApiUrl.match(
      /https:\/\/([^.]+)\.registroelettronico\.com/
    )
    const mastercomId = mastercomMatch ? mastercomMatch[1] : 'demo4'

    // Use the webpack proxy to avoid CORS issues
    url = `/next-api/${mastercomId}/studenti/${studentId}/${schoolYear}/negozio/lista_oggetti`

    console.log('Making mobile API call to:', url)
    console.log('Mastercom ID extracted:', mastercomId)
    console.log('Original next_api_url:', decodedApiUrl)
  } else {
    // Desktop API call
    config = {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }

    // Use the standard API endpoint for desktop
    url = `/api/v3/studenti/${studentId}/negozio/lista_oggetti?anno_scolastico=${schoolYear}`

    console.log('Making desktop API call to:', url)
  }

  console.log('With headers:', config.headers)
  return axios.get(url, config)
}

/**
 * Add product to cart using the new API endpoint
 * @param {string} token - JWT access token
 * @param {string} studentId - Student ID
 * @param {string} schoolYear - School year (e.g., '2024_2025')
 * @param {string} [nextApiUrl] - URL encoded next-api base URL (for mobile)
 * @param {Object} cartItem - Cart item data
 * @param {number} cartItem.id_marketplace - Product marketplace ID
 * @param {number} [cartItem.quantita] - Quantity to add (default: 1)
 * @param {Object} [cartItem.opzioni] - Selected options for the product
 * @param {string} [cartItem.db_richiesto] - Specific database (optional)
 * @returns {Promise} - Add to cart result
 */
export function addProductToCart(
  token,
  studentId,
  schoolYear,
  nextApiUrl = null,
  cartItem
) {
  let config, url

  // Prepare the payload according to the API specification
  const payload = {
    id_marketplace: cartItem.id_marketplace,
    quantita: cartItem.quantita || 1
  }

  // Add optional fields if provided
  if (cartItem.opzioni) {
    payload.opzioni = cartItem.opzioni
  }
  if (cartItem.db_richiesto) {
    payload.db_richiesto = cartItem.db_richiesto
  }

  if (nextApiUrl) {
    // Mobile API call - use next-api proxy
    config = {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }

    // Extract mastercom_id from the nextApiUrl
    const decodedApiUrl = decodeURIComponent(nextApiUrl)
    const mastercomMatch = decodedApiUrl.match(/https:\/\/([^.]+)\./)
    const mastercomId = mastercomMatch ? mastercomMatch[1] : 'demo4'

    // Use the webpack proxy to avoid CORS issues
    url = `/next-api/${mastercomId}/studenti/${studentId}/${schoolYear}/negozio_inserisci_carrello`

    console.log('Making mobile add to cart API call to:', url)
    console.log('Mastercom ID extracted:', mastercomId)
    console.log('Original next_api_url:', decodedApiUrl)
  } else {
    // Desktop API call
    config = {
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    }

    // Use the standard API endpoint for desktop
    url = `/api/v3/studenti/${studentId}/${schoolYear}/negozio_inserisci_carrello`

    console.log('Making desktop add to cart API call to:', url)
  }

  console.log('Add to cart payload:', payload)
  console.log('With headers:', config.headers)
  return axios.post(url, payload, config)
}

/**
 * Add product to cart (legacy function - kept for backward compatibility)
 * @param {Object} authObject - Authentication object (desktop) or token (mobile)
 * @param {Object} product - Product to add to cart
 * @param {boolean} isMobile - Whether this is a mobile request
 * @returns {Promise} - Add to cart result
 */
export function addToCart(authObject, product, isMobile = false) {
  let config = {}
  let url = ''

  const payload = {
    product_id: product.id,
    quantity: 1
  }

  if (isMobile) {
    // Mobile app request with token
    config = {
      headers: {
        Authorization: `Bearer ${authObject.token}`,
        'Content-Type': 'application/json'
      }
    }
    url = `${api.baseUrl}/v3/mobile/negozio/cart`
  } else {
    // Desktop request with standard auth
    config = {
      headers: {
        Authorization: `Bearer ${authObject.jwt.token}`,
        'Content-Type': 'application/json'
      }
    }
    url = `${api.baseUrl}/v3/scuole/${
      authObject.studenteCorrente.id_scuola
    }/studenti/${authObject.studenteCorrente.id}/${
      authObject.studenteCorrente.anno_corrente
    }/negozio/cart`
  }

  // TODO: Replace with actual backend endpoint when available
  return axios.post(url, payload, config)
}

/**
 * Get cart contents
 * @param {Object} authObject - Authentication object (desktop) or token (mobile)
 * @param {boolean} isMobile - Whether this is a mobile request
 * @returns {Promise} - Cart contents
 */
export function getCart(authObject, isMobile = false) {
  let config = {}
  let url = ''

  if (isMobile) {
    // Mobile app request with token
    config = {
      headers: {
        Authorization: `Bearer ${authObject.token}`,
        'Content-Type': 'application/json'
      }
    }
    url = `${api.baseUrl}/v3/mobile/negozio/cart`
  } else {
    // Desktop request with standard auth
    config = {
      headers: {
        Authorization: `Bearer ${authObject.jwt.token}`,
        'Content-Type': 'application/json'
      }
    }
    url = `${api.baseUrl}/v3/scuole/${
      authObject.studenteCorrente.id_scuola
    }/studenti/${authObject.studenteCorrente.id}/${
      authObject.studenteCorrente.anno_corrente
    }/negozio/cart`
  }

  // TODO: Replace with actual backend endpoint when available
  return axios.get(url, config)
}

/**
 * Mock data for development (remove when backend is ready)
 */
export const mockNegozioProducts = [
  {
    id: 1,
    name: 'Quaderno A4',
    description: 'Quaderno a righe per appunti',
    price: 2.5,
    image: '/static/img/quaderno.jpg',
    category: 'Cancelleria',
    available: true,
    stock: 50
  },
  {
    id: 2,
    name: 'Penna Blu',
    description: 'Penna a sfera colore blu',
    price: 1.2,
    image: '/static/img/penna.jpg',
    category: 'Cancelleria',
    available: true,
    stock: 100
  },
  {
    id: 3,
    name: 'Calcolatrice Scientifica',
    description: 'Calcolatrice per matematica e scienze',
    price: 15.9,
    image: '/static/img/calcolatrice.jpg',
    category: 'Elettronica',
    available: true,
    stock: 25
  },
  {
    id: 4,
    name: 'Zaino Scuola',
    description: 'Zaino resistente per libri e materiali',
    price: 35.0,
    image: '/static/img/zaino.jpg',
    category: 'Accessori',
    available: true,
    stock: 15
  },
  {
    id: 5,
    name: 'Set Matite Colorate',
    description: 'Set di 24 matite colorate per disegno',
    price: 8.5,
    image: '/static/img/matite.jpg',
    category: 'Cancelleria',
    available: true,
    stock: 30
  }
]

/**
 * Development helper: Get mock products with delay simulation
 * @returns {Promise} - Mock products
 */
export function getMockProducts() {
  return new Promise(resolve => {
    setTimeout(() => {
      // Create a mock API response that matches the real API structure
      const mockApiResponse = {
        info: { messaggio: '' },
        parametri: { totale_oggetti: 4, categoria_filtrata: 'NEGOZIO' },
        dati: [
          {
            id_marketplace: 31,
            titolo: 'Quaderno A4 Righe',
            descrizione:
              'Quaderno formato A4 con righe, 80 pagine, copertina rigida',
            prezzo: '0,00',
            prezzo_numerico: 0,
            categoria: 'NEGOZIO',
            disponibilita: 50,
            icona: 'default',
            colore_sfondo: '20A4CD',
            colore_testo: '000000',
            sconto_attivo: true,
            caratteristiche: {
              oggetto_negozio: {
                valuta: 'euro',
                prezzo_unitario: '2.50',
                prezzo_scontato: '2.00',
                sconto_valido_dal: '11/07/2025',
                sconto_valido_al: '10/08/2025',
                consenti_ordine_multiplo: 'SI',
                caratteristica: {
                  descrizione: 'Colore copertina',
                  scelta_obbligatoria: 'NO',
                  valori: [
                    { label: 'Blu', disponibili: 30 },
                    { label: 'Rosso', disponibili: 25 },
                    { label: 'Verde', disponibili: 20 }
                  ]
                }
              }
            }
          },
          {
            id_marketplace: 32,
            titolo: 'Penne Blu (3pz)',
            descrizione:
              'Confezione da 3 penne a sfera blu, inchiostro di qualità',
            prezzo: '0,00',
            prezzo_numerico: 0,
            categoria: 'NEGOZIO',
            disponibilita: 100,
            icona: 'default',
            colore_sfondo: '20A4CD',
            colore_testo: '000000',
            sconto_attivo: false,
            caratteristiche: {
              oggetto_negozio: {
                valuta: 'euro',
                prezzo_unitario: '3.75',
                consenti_ordine_multiplo: 'SI'
              }
            }
          },
          {
            id_marketplace: 33,
            titolo: 'Astuccio Portapenne',
            descrizione:
              'Astuccio in tessuto resistente con zip, disponibile in vari colori',
            prezzo: '0,00',
            prezzo_numerico: 0,
            categoria: 'NEGOZIO',
            disponibilita: 25,
            icona: 'default',
            colore_sfondo: 'FF6B6B',
            colore_testo: 'FFFFFF',
            sconto_attivo: true,
            caratteristiche: {
              oggetto_negozio: {
                valuta: 'euro',
                prezzo_unitario: '8.90',
                prezzo_scontato: '7.50',
                sconto_valido_dal: '11/07/2025',
                sconto_valido_al: '26/07/2025',
                consenti_ordine_multiplo: 'NO',
                caratteristica: {
                  descrizione: 'Colore',
                  scelta_obbligatoria: 'SI',
                  valori_possibili: {
                    '1': { label: 'Blu', disponibili: 15 },
                    '2': { label: 'Rosso', disponibili: 12 }
                  }
                }
              }
            }
          },
          {
            id_marketplace: 34,
            titolo: 'Kit Laboratorio Base',
            descrizione: 'Kit completo per esperimenti di laboratorio',
            prezzo: '0,00',
            prezzo_numerico: 0,
            categoria: 'NEGOZIO',
            disponibilita: 10,
            icona: 'default',
            colore_sfondo: '4ECDC4',
            colore_testo: '000000',
            sconto_attivo: false,
            caratteristiche: {
              oggetto_negozio: {
                valuta: 'euro',
                prezzo_unitario: '45.00',
                consenti_ordine_multiplo: 'NO'
              }
            }
          }
        ],
        esito: 'OK'
      }

      // Transform using the real transformation function
      const transformedProducts = transformApiProducts(mockApiResponse)

      resolve({
        data: {
          products: transformedProducts,
          success: true
        }
      })
    }, 1000) // Simulate network delay
  })
}

/**
 * Transform API response to internal product format
 * @param {Object} apiResponse - Response from the negozio API
 * @returns {Array} - Array of products in internal format
 */
export function transformApiProducts(apiResponse) {
  if (!apiResponse.dati || !Array.isArray(apiResponse.dati)) {
    return []
  }

  return apiResponse.dati.map(item => {
    const negozioData =
      (item.caratteristiche && item.caratteristiche.oggetto_negozio) || {}

    // Get the actual price (use discounted price if available and discount is active)
    let price = parseFloat(negozioData.prezzo_unitario || '0')
    if (item.sconto_attivo && negozioData.prezzo_scontato) {
      price = parseFloat(negozioData.prezzo_scontato)
    }

    // Extract characteristics/options if available
    const characteristics = negozioData.caratteristica || null
    let options = null

    try {
      options = characteristics ? extractProductOptions(characteristics) : null
    } catch (error) {
      console.warn('Error extracting product options:', error)
      options = null
    }

    // Extract images from caratteristiche if available
    let images = null
    if (item.caratteristiche && item.caratteristiche.immagini) {
      images = item.caratteristiche.immagini
    } else if (negozioData.immagini) {
      images = negozioData.immagini
    } else if (item.immagini) {
      images = item.immagini
    }

    return {
      id: item.id_marketplace,
      name: item.titolo,
      description: item.descrizione,
      price: price,
      originalPrice: parseFloat(negozioData.prezzo_unitario || '0'),
      currency: negozioData.valuta || 'euro',
      category: item.categoria,
      available: item.disponibilita > 0,
      stock: item.disponibilita,
      discountActive: item.sconto_attivo,
      discountValidFrom: negozioData.sconto_valido_dal,
      discountValidTo: negozioData.sconto_valido_al,
      backgroundColor: item.colore_sfondo
        ? `#${item.colore_sfondo}`
        : '#20A4CD',
      textColor: item.colore_testo ? `#${item.colore_testo}` : '#000000',
      icon: item.icona,
      immagini: images, // Include images array if present
      multipleOrderAllowed: negozioData.consenti_ordine_multiplo === 'SI',
      orderDetails: negozioData.dettagli_ordine_multiplo || null,
      paymentType: negozioData.tipo_pagamento_disponibile,
      availableFor: negozioData.oggetto_disponibile_per,
      characteristics: options,
      rawData: item // Keep original data for reference
    }
  })
}

/**
 * Extract product options/characteristics
 * @param {Object} characteristic - Characteristic object from API
 * @returns {Object} - Formatted options
 */
function extractProductOptions(characteristic) {
  if (!characteristic.valori) {
    return null
  }

  const options = []
  characteristic.valori.forEach(valore => {
    const option = valore
    if (option && option.label) {
      options.push({
        label: option.label,
        available: option.disponibili || 0
      })
    }
  })

  return {
    description: characteristic.descrizione,
    required: characteristic.scelta_obbligatoria === 'SI',
    options: options
  }
}

/**
 * Development helper: Validate mock token
 * @param {string} token - Token to validate
 * @param {string} studentId - Student ID
 * @returns {Promise} - Validation result
 */
export function validateMockToken(token, studentId) {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      // Simple validation for development
      if (token && token.length > 10 && studentId) {
        resolve({
          data: {
            valid: true,
            student: {
              id: studentId,
              nome: 'Studente Mobile',
              classe: '3A',
              id_scuola: '1'
            }
          }
        })
      } else {
        reject(new Error('Invalid token or student ID'))
      }
    }, 500) // Simulate network delay
  })
}
