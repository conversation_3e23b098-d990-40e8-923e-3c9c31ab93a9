import axios from 'axios'

import { apiUrl, Events } from '@/utils'

export class Api {
  constructor(data = {}) {
    if (('studente', 'anno') in data) {
      this.apiUrl = `${apiUrl(data.studente, data.anno)}`
    }
    if ('token' in data) {
      // this.config = this._config(data.token)
      this.token = data.token
    }
    this.postdata = new URLSearchParams()
    this.formdata = new FormData()
    this.formDataType = 'postdata'
    this.jsondata = ''
    this.params = {}
    this.extraHeaders = []
    this.extraConfig = []
    this.axios = axios
  }

  getConfig() {
    return this._config()
  }

  _config() {
    let headers = {
      'Access-Control-Allow-Origin': '*',
      Authorization: `JWT ${this.token}`
    }

    this.extraHeaders.map(h => (headers = { ...headers, ...h }))
    let configObject = {
      params: this._getParams(),
      headers: headers
    }

    this.extraConfig.map(c => (configObject = { ...configObject, ...c }))

    return configObject
  }

  getApiUrl(api) {
    return this._getApiUrl(api)
  }
  _getApiUrl(api) {
    return `${this.apiUrl}/${api}/`
  }

  _getRawApiUrl() {
    return `${this.apiUrl}`
  }

  _getParams() {
    return this.params
  }

  addExtraHeader(header) {
    this.extraHeaders.push(header)
  }

  addParams(params = {}) {
    Object.assign(this.params, params)
  }

  /**
   * setRawUrl
   * @param url::string
   * Raw Url setter
   */
  setRawUrl(url) {
    this.apiUrl = url
  }
  // recupera tutti i dati da api
  // to do: filtri

  async fetchAll(api) {
    const apiUrl = this._getApiUrl(api)
    try {
      const response = await this.axios.get(apiUrl, this._config())
      return response
    } catch (error) {
      this._errorHandler(error)
    }
  }

  _errorHandler(error) {
    throw error
  }

  async fetchRaw(type = 'blob') {
    // let apiUrl = this._getRawApiUrl().replace(
    //   'https://mp.registroelettronico.com',
    //   'api'
    // )
    // apiUrl = apiUrl.replace('api/api', 'api')
    let apiUrl = this._getRawApiUrl()
    let rawConfig = {}
    Object.assign(rawConfig, this._config())
    rawConfig.responseType = type
    try {
      const response = await this.axios.get(apiUrl, rawConfig)
      return response
    } catch (error) {
      this._errorHandler(error)
    }
  }

  setPostData(key, value) {
    this.postdata.append(key, value)
  }

  setJsonData(data) {
    this.jsondata = JSON.stringify(data)
  }

  setFormData(key, value) {
    this.formdata.append(key, value)
  }

  getFormValues() {
    // for (var pair of this[this.formDataType].entries()) {
    //   console.log(pair[0] + ', ' + pair[1])
    // }
    return this[this.formDataType]
  }
  setMultiPart() {
    this.multipart = true
    this.formDataType = 'formdata'
    this.addExtraHeader({ 'Content-Type': 'multipart/form-data' })
  }

  setOnUploadProgress(func) {
    this.extraConfig.push({
      onUploadProgress: func
    })
  }

  async post(api) {
    const apiUrl = this._getApiUrl(api)
    // console.log(apiUrl)
    // console.log(this._config())
    try {
      const response = await this.axios.post(
        apiUrl,
        this.getFormValues(),
        this._config()
      )
      return response
    } catch (error) {
      this._errorHandler(error)
    }
  }

  async postJson(api) {
    const apiUrl = this._getApiUrl(api)
    let config = this._config()
    config.headers['Content-Type'] = 'application/json'
    try {
      const response = await this.axios.post(apiUrl, this.jsondata, config)
      return response
    } catch (error) {
      this._errorHandler(error)
    }
  }

  async put(api) {
    const apiUrl = this._getApiUrl(api)
    // console.log(apiUrl)
    // console.log(this._config())
    try {
      const response = await this.axios.put(
        apiUrl,
        this.postdata,
        this._config()
      )
      return response
    } catch (error) {
      this._errorHandler(error)
    }
  }

  async putJson(api) {
    const apiUrl = this._getApiUrl(api)
    // console.log(apiUrl)
    // console.log(this._config())
    let config = this._config()
    config.headers['Content-Type'] = 'application/json'
    try {
      const response = await this.axios.put(apiUrl, this.jsondata, config)
      return response
    } catch (error) {
      this._errorHandler(error)
    }
  }

  async delete(api) {
    const apiUrl = this._getApiUrl(api)
    // console.log(apiUrl)
    // console.log(this._config())
    try {
      const response = await this.axios.delete(apiUrl, this._config())
      return response
    } catch (error) {
      this._errorHandler(error)
    }
  }

  async deleteBody(api) {
    const apiUrl = this._getApiUrl(api)
    let config = this._config()
    config.data = this.getFormValues()
    try {
      const response = await this.axios.delete(apiUrl, config)
      return response
    } catch (error) {
      this._errorHandler(error)
    }
  }

  async patch(api) {
    const apiUrl = this._getApiUrl(api)
    try {
      const response = await this.axios.patch(
        apiUrl,
        this.getFormValues(),
        this._config()
      )
      return response
    } catch (error) {
      this._errorHandler(error)
    }
  }
  // Gestore centralizzato degli status code

  static errCatcher(err) {
    /*
    console.log('api error handling')
    console.log(vue)
    console.log(err)
    */
    switch (err.status) {
      case 400:
      case 401:
        Events.$emit('failedTokenVerify', err.status)
        break
    }
    return null
  }
}
