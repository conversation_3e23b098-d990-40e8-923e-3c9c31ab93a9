import Vue from 'vue'
import Router from 'vue-router'
import store from './store'

// import Home from '@/views/Home.vue'

// Vue.use(Router)

// export default new Router({
//   routes: [
//     {
//       path: '/',
//       name: 'home',
//       component: Home
//     },
//     {
//       path: '/about',
//       name: 'about',
//       // route level code-splitting
//       // this generates a separate chunk (about.[hash].js) for this route
//       // which is lazy-loaded when the route is visited.
//       component: () =>
//         import(/* webpackChunkName: "about" */ '@/views/About.vue')
//     }
//   ]
// })

Vue.use(Router)

import routes from '@/routes/routes'

const router = new Router({
  routes,
  mode: 'history',
  linkActiveClass: 'active'
})

// router Guard
router.beforeEach((to, from, next) => {
  if (!to.matched.some(record => record.meta.authRequired)) {
    // la route non richiede autenticazione, segue.
    // console.log('not required')
    next()
  } else {
    // la route richiede autenticazione, controlliamo
    // validità del token
    // console.log('check route valid token', store.getters)
    if (
      !('auth/isAuthenticated' in store.getters) ||
      !store.getters['auth/isAuthenticated']
    ) {
      // non è valido, inviamo al login
      if (sessionStorage.getItem('mp-webapp-data')) {
        store.replaceState(JSON.parse(sessionStorage.getItem('mp-webapp-data')))
        if (
          !('auth/isAuthenticated' in store.getters) ||
          !store.getters['auth/isAuthenticated']
        ) {
          next('/login')
        } else {
          if (to.matched.some(record => record.meta.conditionalRoute)) {
            let serviceRoute = to.meta.routeName // recupera il nome del servizio dalla route
            let servizi = store.state.main.studenteCorrente.servizi // recupera dallo store i servizi della scuola
            if (!servizi[serviceRoute]) {
              // se la route non è abilitata per la scuola => 404
              next({
                path: '/404'
              })
            } else {
              // se è abilitata, segue
              // console.log('conditional route ok', to)
              store.dispatch('main/clearDateFilter')
              next()
            }
          } else {
            // console.log('non conditional route')
            next()
          }
        }
      } else {
        next('/login')
      }
    } else {
      // è valido, verifichiamo che la route sia
      // abilitata per la scuola.
      // console.log('BEFORE EACH')
      // store.dispatch('auth/validateAuth')
      // store.dispatch('aggiornamenti/loadAggiornamenti')
      // let refreshData = store.state
      let dataToKeep = [
        'app',
        'auth',
        'materie',
        'main',
        'aggiornamenti',
        'apiErrorHandler'
      ]
      let refreshData = {}
      for (let key of Object.keys(store.state)) {
        if (dataToKeep.includes(key)) {
          refreshData[key] = store.state[key]
        } else {
          refreshData[key] = store.dispatch[`${key}/refreshReset`]
        }
      }
      refreshData = store.state
      sessionStorage.setItem('mp-webapp-data', JSON.stringify(refreshData))
      if (to.matched.some(record => record.meta.conditionalRoute)) {
        let serviceRoute = to.meta.routeName // recupera il nome del servizio dalla route
        let servizi = store.state.main.studenteCorrente.servizi // recupera dallo store i servizi della scuola
        if (!servizi[serviceRoute]) {
          // se la route non è abilitata per la scuola => 404
          next({
            path: '/404'
          })
        } else {
          // se è abilitata, segue
          // console.log('conditional route ok', to)
          store.dispatch('main/clearDateFilter')
          next()
        }
      } else {
        // console.log('non conditional route')
        next()
      }
    }
  }
})

export default router
