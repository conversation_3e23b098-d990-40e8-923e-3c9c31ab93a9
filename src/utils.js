import Vue from 'vue'
import Moment from 'moment'
import SHA from 'sha.js'

import store from './store'
import { api } from './data/config'

// Events
export const Events = new Vue()

export function apiUrl(studente, anno) {
  return `${api.baseUrl}/${api.version}/scuole/${studente.id_scuola}/studenti/${
    studente.id
  }/${anno}`
}

export function apiBaseUrl() {
  return `${api.baseUrl}/${api.version}`
}

export function apiLoginBaseUrl() {
  return `${api.baseUrl}/${api.loginVersion}`
}

export function vendorToken() {
  const today = new Date()
  let token = Moment(today).format('YYYYMMDD') + 'secret'
  let shaToken = SHA('sha256')
    .update(token)
    .digest('hex')
  return shaToken
}

export function capitalizeFirst(s) {
  if (!s) {
    return ''
  }
  s = s.toString()
  return s.charAt(0).toUpperCase() + s.slice(1)
}

export function getMateriaDetails(id_materia) {
  let materie = store.getters['materie/materieStudenteCorrente']
  let x = {}
  let materia = {}
  try {
    materia = materie.filter(k => k.id === parseInt(id_materia))[0]
    x.materia = materia['descrizione']
    x.indice_colore = isNaN(materia['indiceColore'])
      ? 1
      : materia['indiceColore']
    x.insegnante = materia['insegnante']
    // x.insegnante = materia.professori.reduce((acc, cv) => {
    //   return acc !== '' ? `${acc}, ${cv.nome}` : cv.nome
    // }, '')
  } catch (error) {
    x.materia = ''
    x.indice_colore = null
    x.insegnante = ''
  }
  return x
}
