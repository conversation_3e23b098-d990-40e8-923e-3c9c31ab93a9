// Ensure accessible color contrast
export function getAccessibleColors(backgroundColor, textColor) {
  // Convert hex colors to RGB for contrast calculation
  const bgColor = backgroundColor || '#ffffff'
  const fgColor = textColor || '#000000'

  // For accessibility, ensure sufficient contrast
  // If background is too dark or too light, override with accessible colors
  const bgHex = bgColor.replace('#', '')
  const r = parseInt(bgHex.substr(0, 2), 16)
  const g = parseInt(bgHex.substr(2, 2), 16)
  const b = parseInt(bgHex.substr(4, 2), 16)

  // Calculate luminance (simplified)
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255

  // If background is too dark, use white text
  // If background is too light, use dark text
  let accessibleTextColor = fgColor

  if (luminance < 0.5) {
    // Dark background - ensure white or light text
    accessibleTextColor = '#ffffff'
  } else {
    // Light background - ensure dark text
    accessibleTextColor = '#333333'
  }

  // For specific colors that we know need adjustment
  if (bgHex.toLowerCase() === '20a4cd') {
    // Blue background - use white text for better contrast
    accessibleTextColor = '#ffffff'
  }

  return {
    backgroundColor: bgColor,
    textColor: accessibleTextColor
  }
}
