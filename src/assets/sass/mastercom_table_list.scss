.table-list {
    .row {
        text-align: left;
        display: flex;
        border-bottom: 1px solid #f8f8f8;
        color: #666666;
        cursor: pointer;
        overflow: hidden;
        &.da-leggere {
            color: #000000;
            font-weight: 700;
        }
        &:nth-of-type(even) {
            background-color: #f8f8f8;
        }

        &:not(.table-header):hover {
            font-size: 1.1em;

            background-color: #bbfffc;
            transition: background-color 0.3s, font-size 0.3s;
            color: #000000;
        }
        .currency {
            text-align: right;
        }
        &.inner-detail {
            font-size: 1em;
            flex-wrap: wrap;
            @media only screen and (max-width: 1024px) {
                .col-xs-6 {
                    flex: 50%;
                }
                .col-xs-12 {
                    flex: 100%;
                }
            }
        }
    }
    .table-header {
        font-weight: 700;
        div {
            text-align:center;
        }
    }
    .row > div {
        min-height: 40px;
        font-size: 0.7em;
        line-height: 20px;

        @media only screen and (min-width: 1025px) {
            font-size: 0.8em;
            &:not(:first-child) {
                border-left: 1px solid #e0e0e0;
                box-sizing: border-box;
            }
        }

        @media only screen and (min-width: 1441px) {
            font-size: 1em;
            line-height: 40px;
        }
    }
    .sub-line {
        font-size: 0.9em;
        &:before {
            content: ' '; display: block;
        }
    }
  }