@import url('https://fonts.googleapis.com/css2?family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap');
.template-pagella {
    * {
        font-family: 'Ubuntu', sans-serif;
        word-break: break-word;
    }
    .separator {
        clear:both;
    }

    .materia-title {
        font-weight: 700;
    }

    .materia-container {        
        border-left: 3px solid rgb(236, 197, 6);
        border-top: 0;
        margin-bottom: 20pt;
    }

    .col-2,
    .col-50 {
        float: left;
        width: 49.5%;
    }

    .col-4,
    .col-25 {
        float: left;
        width: 24.5%;
    }
    .col-3,
    .col-33 {
        float: left;
        width: 32.5%;
    }
    .col-5,
    .col-20 {
        float: left;
        width: 19.5%;
    }
    .voto {
        text-align: center;        
        border-right: 1pt solid #dadada;
        padding: 2pt 0;
    }

    .voto h6 {
        margin-top: 2pt;
        margin-bottom: 0pt;
        font-size: 8pt;
        text-transform: uppercase;
    }

    .voto .valore-voto {
        padding: 2pt;
        font-size: 12pt;
        font-weight: 300;
    }

    .materia-riga {
        padding: 4pt 2pt;
        overflow: auto;

        font-size: 10pt;
    }

    .materia-header {
        /*background-color: #e4e4e4;*/
        background-color: #f8f8f8;
        /* float: left; */
        /* clear: both; */
        overflow: auto;
        padding: 3pt 0;
    }
    /*
    .materia-1 .materia-header {
        background-color: #b0e2ff;
    }

    .materia-2 .materia-header {
        background-color: #b2dfee;
    }

    .materia-3 .materia-header,
    .materia-4 .materia-header {
        background-color:#83dcfc;
    }

    .materia-5 .materia-header,
    .materia-6 .materia-header {
        background-color: #25c1f9
    }
    */

    .materia-title {
        padding-left: 2pt;
        float: left;
        font-weight: 700;
        width: 75%;
        text-transform: uppercase;
        font-size: 10pt;
    }

    .materia-main {
        border-bottom: 1pt solid #f8f8f8;
        overflow: auto;
    }

    .voto-pagellina {
        background-color: #fff;
        float: right;
        width: 20%;
        text-align: center;
        font-size: 10pt;
        border: 1pt solid #f8f8f8;
    }

    .dati-pagella {
        font-size: 10pt;
    }

    .voto:last-child {
        border-right: 0;
    }

    .media-campi-liberi {
        border-left: 3px solid rgb(236, 197, 6);
    }

    .campi-liberi h4 {
        margin-top: 5pt;
        margin-bottom: 2pt;
    }
    .campi-liberi .materia-riga {
        border-bottom: 1pt solid #f8f8f8;
    }
    .campi-liberi .materia-riga.last {
        border-bottom: 0pt;
    }

    // .odd {
    //     background-color: #f8f8f8;
    //     background-color: #e0f3ff;
    // }

    .media-campi-liberi h5 {
        margin-bottom:0pt;
        margin-top: 3pt;
        padding: 2pt;
        font-weight: 700;
        text-transform: uppercase;

    }

    .media-campi-liberi h4 {
        font-size: 10pt;
    }
}
