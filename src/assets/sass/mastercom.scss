@import './mastercom_colors';
@import './mastercom_margin_utilities';
@import './mastercom_pagelle';
@import './mastercom_table_list';
@import './mastercom_tabs_nav';
*:focus {
    outline: 0;
}

.sidebar,
.off-canvas-sidebar{
    .sidebar-wrapper {
        padding-bottom: 10vh;
    }
    .logo {
        .logo-img {
            background-color: transparent;
            img {
                max-width: 34px;
            }
        }
        .logo-normal {
            float:left;
            width: 150px;
            line-height: 1.2em;
            white-space: normal;
        }
    }
    .nav {
        .sidebar-mini,
        .sidebar-normal {
            text-transform: capitalize;
        }
    }
}


.el-picker-panel__shortcut {
    font-size: 11px;
}

    .el-select {
        .el-input{
            &:hover {
                .el-input__icon,
                input {
                    color: #bbbbbb;
                }
            }
        }
    }

.popover-content {
    word-break: break-word;
}


.ellipsis {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.full-relative-width {
    width: 99.9%;
}

.Cookie--mastercom {
    &.Cookie--blood-orange {
        .<PERSON><PERSON>__button {
            background-color: #073;
            &:hover {
                background-color: #0A3;
            }
        }
    }
}

.navbar-brand {
    text-transform: capitalize;
}
.mastercom {

    .card-timeline {
        .timeline {
           &:before {
            left: 5%;
            }
            > li, > li > section {
                > .timeline-badge {
                    padding: 5px;
                    line-height: 18px;
                    left: 5%;
                    font-size: 0.8em;
                    background-color: $grey_2;
                    position: relative;
                    float: left;
                    text-transform: uppercase;
                    font-weight: 700;
                }

                > .timeline-panel {
                    width: 80%;
                    float: left;
                    left: 10%;
                }
                /*
                &:hover {
                    > .timeline-badge {
                        background-color: $color_5;
                    }
                    p {
                        color: #333;
                    }
                }
                */
            }
        }
        h6 {
            &.insegnante {
                color: #252422;
                font-weight: 700;
            }
        }
    }
    .materia {
        &.title {
            color: #ffffff;
            font-size: 0.8em;
            font-weight: 700;
            display: inline-flex;
            width: auto;
            &.bg_1 {
                background-color: $color_1;
            }
            &.bg_2 {
                background-color: $color_2;
            }
            &.bg_3 {
                background-color: $color_3;
            }
            &.bg_4 {
                background-color: $color_4;
            }
            &.bg_5 {
                background-color: $color_5;
            }
            &.bg_6 {
                background-color: $color_6;
            }
            &.bg_7 {
                background-color: $color_7;
            }
            &.bg_8 {
                background-color: $color_8;
            }
            &.bg_9 {
                background-color: $color_9;
            }
            &.bg_10 {
                background-color: $color_10;
            }
            &.bg_11 {
                background-color: $color_11;
            }
            &.bg_12 {
                background-color: $color_12;
            }
            &.bg_13 {
                background-color: $color_13;
            }
            &.bg_14 {
                background-color: $color_14;
            }
            &.bg_15 {
                background-color: $color_15;
            }
            &.bg_16 {
                background-color: $color_16;
            }
            &.bg_17 {
                background-color: $color_17;
            }
            &.bg_18 {
                background-color: $color_18;
            }
            &.bg_19 {
                background-color: $color_19;
            }
            &.bg_20 {
                background-color: $color_20;
            }
            &.bg_21 {
                background-color: $color_21;
            }
            &.bg_22 {
                background-color: $color_22;
            }
            &.bg_23 {
                background-color: $color_23;
            }
            &.bg_24 {
                background-color: $color_24;
            }
            &.bg_25 {
                background-color: $color_25;
            }
            &.bg_26 {
                background-color: $color_26;
            }
            &.bg_27 {
                background-color: $color_27;
            }
            &.bg_28 {
                background-color: $color_28;
            }
            &.bg_29 {
                background-color: $color_29;
            }
            &.bg_30 {
                background-color: $color_30;
            }
            &.bg_31 {
                background-color: $color_31;
            }
            &.bg_32 {
                background-color: $color_32;
            }
            &.bg_33 {
                background-color: $color_33;
            }
            &.bg_34 {
                background-color: $color_34;
            }
            &.bg_35 {
                background-color: $color_35;
            }
            &.bg_36 {
                background-color: $color_36;
            }
            &.bg_37 {
                background-color: $color_37;
            }
            &.bg_38 {
                background-color: $color_38;
            }
            &.bg_39 {
                background-color: $color_39;
            }
            &.bg_40 {
                background-color: $color_40;
            }
            &.bg_41 {
                background-color: $color_41;
            }
            &.bg_42 {
                background-color: $color_42;
            }
            &.bg_43 {
                background-color: $color_43;
            }
            &.bg_44 {
                background-color: $color_44;
            }
            &.bg_45 {
                background-color: $color_45;
            }
            &.bg_46 {
                background-color: $color_46;
            }
        }
    }
    .item-list {
        padding-left: 0px;
        li {
            list-style-type: none;
            margin: 0px;
            margin-bottom: 10px;
            padding-bottom: 10px;
            padding-top: 10px;
            border-bottom: 1px solid #F1EAE0;
            h5 {
                margin-top: 0;
                line-height: normal;
            }
        }
    }
}

.mastercom-popover {
    box-shadow: 0 3px 12px 0 rgba(0,0,0,.3);
    .el-popover__title {
        font-weight: 700;
        padding-bottom: 4px;
    }
    &.popover-compiti {

        .el-popover__title {
            color: #68B3C8;
            border-bottom: 1px solid #68B3C8;
        }
    }
    &.popover-argomenti {

        .el-popover__title {
            color: #F3BB45;
            border-bottom: 1px solid #F3BB45;
        }
    }

}

.mastercom-msg-box {
    @media screen and (max-width: 420px) {
        width: 95%;
    }
}


.vue-tabs {
    .nav-tabs {
        > li.active, .el-tabs__item.is-active{
            > a {
                color: #333;
            }
        }
    }
}
.mini {
    width: 25%;
}

.icon-large {
    font-size: 1.5em;
    font-weight: 300;
}

.icon-semi-opaque {
    opacity: 0.5;
}

.position-relative {
    position: relative;
}

.position-absolute {
    position: absolute;

}

.bottom-right {
    bottom: 0;
    top: none;
    right: 0;
    left: none;
}

.btn-xss {
    font-size: 8px;
    border-radius: 18px;
    padding: 2px 4px;
}

.presa-visione {
    padding-right: 5px;
}


.clickable {
    cursor: pointer;
}

.ellipsed {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }


.el-calendar-table .el-calendar-day {
    padding: 0px !important;
    height: 110px !important;
}