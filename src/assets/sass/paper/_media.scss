.media{
    border-bottom: 1px solid $medium-gray;
    padding-bottom: 30px;
    margin-top: 30px;
    
    .avatar{
        margin: 0 auto;
        width: 64px;
        height: 64px;
        overflow: hidden;
        border-radius: 50%;
        margin-right: 15px;
        border: 3px solid transparent;
        
        img{
            width: 100%;
        }
    }
    .media-heading{
        margin-bottom: 10px;
        margin-top: 5px;
        display: inline-block;
    }
    .btn-simple{
        padding: 0px 5px;
    }
    .media{
        margin-top: 30px;
    }
    .media:last-child{
        border: 0;
    }
}

.media-post{
    color: #555;
    border: 0;
    .media-heading{
        display: block;
        text-align: center;   
    }
    .author{
        width: 15%;
    }
    .media-body{
        width: 85%;
        float: left;
        display: inline-block;
    }
    textarea{
        margin: $margin-bottom;
        font-size: $font-paragraph;
    }
    .avatar{
        border-color: white;
    }
}


.media-area{
    .media:last-child{
        border: 0;
    }
    .pagination-area{
        padding: 10px 0;
        text-align: center;
    }
}
.media-area-small{
    p{
        font-size: 14px;
    }
    .btn-simple{
        font-size: 14px;
    }
    .avatar{
        width: 58px;
        height: 58px;
    }
}
