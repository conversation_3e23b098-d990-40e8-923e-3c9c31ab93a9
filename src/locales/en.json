{"main": {"attachment": "attachment", "date": "date", "detail": "detail", "description": "description", "type": "type", "no_content": "no content available", "class": "class", "loading": "loading", "close_modal": "close", "filter_solo_novita": "only unseen", "filter_vedi_tutti": "see all", "filter_materie": "filter subjects", "filter_periodo": "filter periods", "filter_data": "pick one date", "filter_tag": "filter tag"}, "nav": {"home": "home", "user": {"change-password": "change password", "portale-iscrizioni": "application portal", "logout": "logout"}, "student": {"past-years": "previous school years", "select-student": "select student"}, "func": {"function": "functions", "compiti": "assignment", "voti": "marks", "assenze": "absences", "competenze": "skills/goals", "annotazioni": "annotations", "note": "disciplinary notes", "agenda": "agenda", "argomenti": "topics", "pagelle": "school reports", "documenti": "documents", "materiale": "educational material", "comunicazioni": "messages", "colloqui-individuali": "individual interviews", "colloqui-generali": "general interviews", "pagamenti": "payments", "mense": "school lunch", "elezioni": "elections", "servizi-giornalieri": "daily services", "note_tutor": "tutor reports", "negozio": "shop"}}, "negozio": {"loading": "Loading shop...", "section_disabled_title": "Shop not available", "section_disabled_message": "The school shop is temporarily disabled.", "close": "Close", "add_to_cart": "Add to cart", "success": "Success", "error": "Error", "product_added_to_cart": "Product '{product}' added to cart", "mobile": {"auth_error_title": "Unauthorized Access", "close_app": "Close", "validating_access": "Validating access...", "missing_parameters": "Missing access parameters. Please try again from the mobile app.", "invalid_token": "Invalid access token. Please login again in the mobile app.", "token_validation_error": "Error validating token. Check your connection and try again.", "initialization_error": "Initialization error. Please try again later.", "student_data_error": "Error loading student data."}}, "assenze": {"modal_title": "Indica motivazione {0}", "label_altro": "Altro", "placeholder": "Inserisci motivazione nella casella", "button_giustifica": "Giustifica assenza", "button_list": "Giustifica", "motivazione_title_list": "Motivazione assenza"}, "compiti": {}, "mense": {"pasti_panel_title": "Prenotazione pasti", "scadenza_pasto": "Seleziona pasto entro", "no_lunch": "<PERSON>essun pasto disponibile", "reservation_ended": "Prenotazione conclusa", "consumed_lunches": "Pasti fruiti"}, "note_tutor": {"firme_title": "Firme", "button_firma": "Firma", "inserisci_firma": "Aggiungi firma genitore", "nessuna_firma": "nessuna firma presente", "firma_confirm": "<PERSON><PERSON><PERSON><PERSON>", "firma_cancel": "<PERSON><PERSON><PERSON>", "data_ora_inserimento": "In<PERSON><PERSON> da {0} il {1} alle ore {2}", "tutor": "Resoconto del tutor", "consiglio_classe": "Resoconto del consiglio di classe"}, "documenti": {"title": "Documenti", "didattici": {"title": "Documenti didattici"}, "amministrativi": {"title": "Documenti amministrativi"}}, "servizi": {"crediti": {"label_button": "Mostra credito residuo", "title_servizi_desc": "Ser<PERSON>zi <PERSON>i", "title_servizi_cup": "Costo unitario", "title_servizi_presenze": "Presenze", "title_servizi_assenze": "<PERSON><PERSON><PERSON>", "title_servizi_non_definite": "Non Def.", "title_servizi_da_pagare": "Da pagare", "title_credito_total": "Credito totale", "title_credito_rimanente": "<PERSON><PERSON>"}}, "pagamenti": {"title": "<PERSON><PERSON><PERSON>", "estratto_conto": {"title": "<PERSON><PERSON><PERSON> conto"}, "marketplace": {"title": "Negozio"}, "carrello": {"testo": "Hai selezionato i seguenti pagamenti/acquisti:", "label_aggiungi": "Acquista", "label_paga": "Paga", "label_rimuovi": "<PERSON><PERSON><PERSON><PERSON>", "expired": "scaduto il {0}", "totale": "totale", "no_payment": "", "paid": "pagato", "vuoto": "nessun pagamento/acquisto | un pagamento/acquisto | {count} pagamenti/acquisti", "carrello_btn": "<PERSON>ga ora", "carrello_btn_waiting": "Attendi...", "pagamento_attesa": "Il pagamento non è ancora stato elaborato", "pagamento_confermato": "Il pagamento è avvenuto con successo", "pagamento_annullato": "Il pagamento è stato annullato"}, "label_importo": "importo", "label_descrizione": "descrizione", "prefix_scadenza": "da pagare entro", "prefix_residuo": "da pagare"}, "login": {"title": "accesso utente", "label_username": "codice utente", "label_lost_password": "password dimenticata?", "label_password": "password", "label_button_submit": "accedi", "placeholder_username": "inserisci il tuo codice utente", "placeholder_password": "inserisci la password", "warning": {"required_fields": "Inserire codice utente e password", "login_failed": "Codice utente e/o password non validi", "school_config_error": "Errore configurazione parametri scuola", "student_config_error": "Errore configurazione dati studente", "school_updates_error": "Errore caricamento aggiornamenti scuola"}}}