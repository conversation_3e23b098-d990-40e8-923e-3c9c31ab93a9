{"main": {"attachment": "allegato", "date": "data", "time": "orario", "actions": "azioni", "edit": "modifica", "select": "seleziona", "detail": "<PERSON><PERSON><PERSON><PERSON>", "description": "descrizione", "type": "tipo", "no_content": "nessun contenuto disponibile", "class": "classe", "loading": "caricamento dati", "clos_modal": "chiudi", "novita": "novità", "filter_solo_novita": "solo novità", "filter_vedi_tutti": "vedi tutti", "filter_materie": "filtra materie", "filter_periodo": "filtra periodo", "filter_data": "seleziona un giorno", "filter_tag": "filtra tag", "label_or": "oppure", "confirm": "conferma", "cancel": "annulla"}, "api-error": {"generic": "attenzione, si è verificato un errore imprevisto", "401": "sessione scaduta", "403": "codice utente/password non corretti", "404": "contenuto non disponibile", "408": "il server ha impiegato troppo tempo per rispondere", "500": "attenzione, si è verificato un errore imprevisto"}, "nav": {"home": "home", "user": {"change-password": "modifica password", "portale-iscrizioni": "portale iscrizioni", "logout": "uscita"}, "student": {"past-years": "visualizza anni storici", "select-student": "seleziona altro studente"}, "func": {"function": "funzioni", "compiti": "compiti", "voti": "voti", "assenze": "assenze", "competenze": "competenze/obiettivi", "annotazioni": "annotazioni", "note": "note", "agenda": "agenda", "argomenti": "argomenti", "pagelle": "pagelle", "documenti": "documenti", "materiale": "materiale didattico", "verifiche": "verifiche", "comunicazioni": "comunicazioni", "comunicazioni2": "comunicazioni", "colloqui-individuali": "colloqui individuali", "colloqui-generali": "colloqui generali", "pagamenti": "pagamenti", "mense": "mense", "elezioni": "elezioni", "servizi-giornalieri": "servi<PERSON> gior<PERSON>", "note_tutor": "resoconti tutor", "ae": "dichiarazione agenzia entrate", "bacheca": "bacheca", "negozio": "ne<PERSON><PERSON>"}}, "negozio": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Acquista materiali e prodotti per la scuola", "loading": "Caricamento prodotti...", "section_disabled_title": "Negozio non disponibile", "section_disabled_message": "Il negozio scolastico è temporaneamente disabilitato.", "close": "<PERSON><PERSON>", "add_to_cart": "Aggiungi al carrello", "success": "Successo", "error": "Errore", "product_added_to_cart": "Prodotto '{product}' aggiunto al carrello", "empty_state": {"title": "<PERSON><PERSON>un prodotto disponibile", "message": "Al momento non ci sono prodotti disponibili nel negozio."}, "product": {"discount_badge": "SCONTO", "options_available": "{count} opzioni disponibili"}, "errors": {"loading_failed": "Errore nel caricamento dei prodotti", "server_error": "Errore nel caricamento dei prodotti dal server", "unknown_error": "<PERSON><PERSON><PERSON> scon<PERSON>", "api_response_error": "Errore risposta API: {message}", "cors_fallback": "Errore CORS/Network rilevato, utilizzo dati di fallback", "import_fallback": "Impossibile caricare il servizio API, utilizzo dati di fallback:"}, "fallback_products": {"notebook": {"name": "Quaderno A4", "description": "Quaderno a righe per appunti", "category": "Cancelleria"}, "pen": {"name": "<PERSON><PERSON>", "description": "Penna a sfera colore blu", "category": "Cancelleria"}, "calculator": {"name": "Calcolatrice Scientifica", "description": "Calcolatrice per matematica e scienze", "category": "Elettronica"}, "backpack": {"name": "<PERSON><PERSON><PERSON>", "description": "Zaino resistente per libri e materiali", "category": "Accessori"}}, "mobile": {"auth_error_title": "Accesso non autorizzato", "close_app": "<PERSON><PERSON>", "validating_access": "Validazione accesso in corso...", "missing_parameters": "Parametri di accesso mancanti. Riprova dall'app mobile.", "invalid_token": "Token di accesso non valido. Effettua nuovamente il login nell'app mobile.", "token_validation_error": "Errore durante la validazione del token. Verifica la connessione e riprova.", "initialization_error": "Errore durante l'inizializzazione. Riprova più tardi.", "student_data_error": "Errore nel caricamento dei dati studente."}}, "assenze": {"prenotazione_entrate": "Prenotazione entrate in ritardo", "prenotazione_uscite": "Prenotazione uscite anticipate", "modal_title": "Indica motivazione {0}", "label_altro": "Altro", "placeholder": "Inserisci motivazione nella casella", "button_giustifica": "Giustifica assenza", "button_list": "Giustifica", "motivazione_title_list": "Motivazione assenza", "btn_prenotazione_entrate": "Prenota entrata in ritardo", "btn_prenotazione_uscite": "Prenota uscita anticipata", "btn_invia_prenotazione": "Invia", "btn_annulla_prenotazione": "<PERSON><PERSON><PERSON>", "btn_annulla_elenco": "<PERSON><PERSON><PERSON>notazi<PERSON>", "titolo_form_entrate": "Prenotazione entrata in ritardo", "titolo_form_uscite": "Prenotazione uscita anticipata", "seleziona_data_label": "Seleziona data", "seleziona_data_placeholder": "data", "seleziona_orario_label": "Seleziona ora", "seleziona_orario_placeholder": "ora", "motivazione_label": "Motivazione", "motivazione_placeholder": "inserire la motivazione della richiesta", "conferma_invio_richiesta": "Conferma invio richiesta", "conferma_annullamento_richiesta": "Vuoi annullare la prenotazione?", "titolo_messaggio_errore": "Attenzione", "filter_solo_da_giustificare": "solo da giustificare", "filter_solo_presenze_corsi": "solo presenze corsi"}, "compiti": {"filter_tutti": "tutti i compiti", "filter_prossimi": "prossime assegnazioni"}, "comunicazioni": {"title": "comunicazioni", "new_message": "scrivi messaggio", "addAttachment": "Aggiungi allegati"}, "voti": {"titolo": "Voti", "label_note_competenze": "Note del docente", "label_dimensioni": "Dimensioni", "scritto": "<PERSON><PERSON><PERSON>", "orale": "<PERSON><PERSON>", "pratico": "<PERSON><PERSON><PERSON>", "presa_visione": "Clicca per la presa visione", "data_presa_visione": "Presa visione il {0} alle ore {1}", "msg_conferma": "Confermi la presa visione?", "btn_conferma_richiesta": "conferma", "btn_annulla_richiesta": "annulla", "descrizione_peso": "Questa valutazione pesa al {0}"}, "mense": {"pasti_panel_title": "Prenotazione pasti", "scadenza_pasto": "Seleziona pasto entro", "no_lunch": "<PERSON>essun pasto disponibile", "reservation_ended": "Prenotazione conclusa", "consumed_lunches": "Prenotazioni", "residual_credit": "Credito residuo", "total_lunches": "Totale pasti fruiti", "panel_footer": "in base ai pasti consumati alla data odierna", "no_reservable": "prenotazione pasti non disponibile", "consumed_lunch": "fruito", "crediti_panel_titolo": "Movimenti contabili mensa", "pagamento_da_eseguire": "Pagamento da effettuare secondo le modalità concordate con la scuola", "pagamento_parziale": "Ultimo pagamento effettuato da {0} con modalità {1} - importo residuo: {2}", "pagamento_effettuato": "Pagamento effettuato da {0} con modalità {1}", "rimborso": "Pagamento effettuato a favore di {0} con modalità {1}"}, "note_tutor": {"firme_title": "Firme", "button_firma": "Firma", "inserisci_firma": "Aggiungi firma genitore", "nessuna_firma": "nessuna firma presente", "firma_confirm": "<PERSON><PERSON><PERSON><PERSON>", "firma_cancel": "<PERSON><PERSON><PERSON>", "data_ora_inserimento": "In<PERSON><PERSON> da {0} il {1} alle ore {2}", "tutor": "Resoconto del tutor", "consiglio_classe": "Resoconto del consiglio di classe"}, "documenti": {"title": "Documenti", "didattici": {"title": "Documenti didattici"}, "amministrativi": {"title": "Documenti amministrativi"}}, "colloqui": {"title": "colloqui", "text_conferma_richiesta_colloquio": "inviare la richiesta di colloquio?", "text_conferma_richiesta_inviata": "la richiesta è stata inviata con successo.", "btn_richiesta_colloquio_individuale": "richiesta colloquio", "btn_conferma_richiesta": "conferma", "btn_annulla_richiesta": "annulla", "btn_gestione_prenotato": "modifica/annulla", "label_print_elenco": "stampa elenco pre<PERSON>azioni", "label_insegnante": "insegnante", "label_ultimo_colloquio": "ultimo colloquio", "label_prossimo_colloquio": "prossimo colloquio prenotato", "label_seleziona_data": "seleziona", "label_seleziona_data_step": "seleziona data colloquio", "label_seleziona_slot": "posti disponibili", "label_prenotazione_slot": "prenotazione posto", "label_orario_note": "orario e note", "label_stato": "stato", "label_descrizione": "descrizione", "btn_prenota": "prenota", "btn_cancella": "cancella", "btn_indietro": "torna", "text_non_disponibili": "nessun colloquio disponibile"}, "servizi": {"crediti": {"label_button": "Mostra credito residuo", "title_servizi_desc": "Ser<PERSON>zi <PERSON>i", "title_servizi_cup": "Costo unitario", "title_servizi_presenze": "Presenze", "title_servizi_assenze": "<PERSON><PERSON><PERSON>", "title_servizi_non_definite": "Non Def.", "title_servizi_da_pagare": "Da pagare", "title_credito_total": "Credito totale", "title_credito_rimanente": "<PERSON><PERSON>"}}, "pagamenti": {"title": "<PERSON><PERSON><PERSON>", "estratto_conto": {"title": "<PERSON><PERSON><PERSON> conto"}, "app_satispay_title": "Pagamento con App <PERSON>", "credit_card_title": "Pagamento con carta di credito", "marketplace": {"title": "Negozio"}, "estratto_conto_crediti": {"tipo": "Tipo", "title": "Crediti", "tipo_rimborso_pagamento": "<PERSON><PERSON><PERSON><PERSON> paga<PERSON>", "tipo_pagamento": "Pagamento", "tipo_restituzione": "<PERSON><PERSON>", "tipo_integrazione": "Versamento", "tipo_deposito": "Versamento", "saldo": "<PERSON><PERSON> residuo"}, "carrello": {"testo": "Hai selezionato i seguenti pagamenti/acquisti:", "label_aggiungi": "Acquista", "label_paga": "Paga", "label_rimuovi": "<PERSON><PERSON><PERSON><PERSON>", "expired": "scaduto il {0}", "totale": "totale", "no_payment": "", "paid": "pagato", "vuoto": "nessun pagamento/acquisto | un pagamento/acquisto | {count} pagamenti/acquisti", "carrello_btn": "<PERSON>ga ora", "carrello_btn_waiting": "Attendi...", "pagamento_attesa": "Il pagamento non è ancora stato elaborato", "pagamento_confermato": "Il pagamento è avvenuto con successo", "pagamento_annullato": "Il pagamento è stato annullato", "paga_stripe": "Paga con carta di credito"}, "label_importo": "importo", "label_descrizione": "descrizione", "prefix_scadenza": "da pagare entro", "prefix_residuo": "da pagare"}, "login": {"title": "accesso utente", "label_username": "codice utente", "label_lost_password": "password dimenticata?", "label_password": "password", "label_button_submit": "accedi", "label_SPID": "Entra con SPID", "placeholder_username": "inserisci il tuo codice utente", "placeholder_password": "inserisci la password", "ongoing_auth": "autenticazione in corso...", "warning": {"required_fields": "Inserire codice utente e password", "login_failed": "Codice utente e/o password non validi", "school_config_error": "Errore configurazione parametri scuola", "student_config_error": "Errore configurazione dati studente", "school_updates_error": "Errore caricamento aggiornamenti scuola", "spid_login_error": "Non è stato possibile effettuare l'accesso con SPID", "spid_login_not_found": "Il Codice Fiscale fornito con SPID non è associato a nessun utente della scuola, contattare la segreteria della scuola per verificare i propri dati.", "spid_login_duplicate": "Il Codice Fiscale fornito con SPID è associato a più utenti della scuola, contattare la segreteria della scuola per verificare i propri dati."}}, "note": {"title": "Note", "presa_visione": "Clicca per la presa visione", "data_presa_visione": "Presa visione il {0} alle ore {1}", "msg_conferma": "Confermi la presa visione?", "btn_conferma_richiesta": "conferma", "btn_annulla_richiesta": "annulla"}, "materiale": {"title_descrizione": "descrizione", "title_caricato_da": "caricato da"}, "servizi_giornalieri": {"text_conferma_adesione": "Conferma l'adesione giornaliera per questi servizi:", "text_cancellazione": "cancellazione possibile entro le ore {0} di {1}", "text_inserimento": "inserimento possibile entro le ore {0} di {1}", "text_nessun_servizio": "nessun servizio disponibile per {0} e {1}"}, "alternanza": {"title": "Alternanza scuola-lavoro", "title_data": "Data inizio/fine", "title_progetto": "Progetto", "title_stato": "Stato", "btn_dettagli": "Info", "btn_iscriviti": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "btn_rimuovi": "Cancella iscrizione", "label_no_projects": "<PERSON><PERSON><PERSON> progetto disponibile", "label_no_date": "Non sono state indicate date"}}