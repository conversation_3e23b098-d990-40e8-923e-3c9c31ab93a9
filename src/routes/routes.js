function lazyLoad(componentPath) {
  return () => import(`@/components/${componentPath}.vue`)
}

import MastercomLayout from '@/components/Mastercom/Layout/MastercomLayout.vue'
// Mobile layout is now handled by web components
// GeneralViews
import NotFound from '@/components/GeneralViews/NotFoundPage.vue'

import Vue from 'vue'
import i18n from '../i18n'
let vue = new Vue({ i18n })

let loginPage = {
  path: '/login',
  name: 'Login',
  component: lazyLoad('Mastercom/Views/Login')
}
let homePage = {
  path: '/home',
  name: 'Home',
  component: lazyLoad('Mastercom/Views/Home'),
  meta: {
    authRequired: true,
    conditionalRoute: false,
    routeName: 'home'
  }
}

let compitiPage = {
  path: '/compiti',
  name: vue.$i18n.t('nav.func.compiti'),
  component: lazyLoad('Mastercom/Views/Compiti'),
  meta: {
    authRequired: true,
    conditionalRoute: true,
    routeName: 'compiti'
  }
}

let agendaPage = {
  path: '/agenda',
  name: vue.$i18n.t('nav.func.agenda'),
  component: lazyLoad('Mastercom/Views/Agenda'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'agenda'
  }
}

let argomentiPage = {
  path: '/argomenti',
  name: vue.$i18n.t('nav.func.argomenti'),
  component: lazyLoad('Mastercom/Views/Argomenti'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'argomenti'
  }
}

let PagellePage = {
  path: '/pagelle',
  name: vue.$i18n.t('nav.func.pagelle'),
  component: lazyLoad('Mastercom/Views/Pagelle'),
  meta: {
    conditionalRoute: false,
    authRequired: true,
    routeName: 'pagelle'
  }
}

let pagellePagePreview = {
  path: '/anteprima_pagelle',
  name: 'pagelle',
  component: lazyLoad('Mastercom/Views/AnteprimaPagelle'),
  meta: {
    conditionalRoute: false,
    authRequired: true,
    routeName: 'pagelle'
  }
}

let votiPage = {
  path: '/voti',
  name: vue.$i18n.t('nav.func.voti'),
  component: lazyLoad('Mastercom/Views/Voti'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'voti'
  }
}

let competenzePage = {
  path: '/competenze-obiettivi',
  name: vue.$i18n.t('nav.func.competenze'),
  component: lazyLoad('Mastercom/Views/Competenze'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'competenze'
  }
}

let annotazioniPage = {
  path: '/annotazioni',
  name: vue.$i18n.t('nav.func.annotazioni'),
  component: lazyLoad('Mastercom/Views/Annotazioni'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'annotazioni'
  }
}

let noteTutorPage = {
  path: '/note-tutor',
  name: vue.$i18n.t('nav.func.note_tutor'),
  component: lazyLoad('Mastercom/Views/NoteTutor'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'note_tutor'
  }
}

let assenzePage = {
  path: '/assenze',
  name: vue.$i18n.t('nav.func.assenze'),
  component: lazyLoad('Mastercom/Views/Assenze'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'assenze'
  }
}

let notePage = {
  path: '/note-disciplinari',
  name: vue.$i18n.t('nav.func.note'),
  component: lazyLoad('Mastercom/Views/NoteDisciplinari'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'note'
  }
}

let colloquiPage = {
  path: '/colloqui',
  name: vue.$i18n.t('nav.func.colloqui-individuali'),
  component: lazyLoad('Mastercom/Views/Colloqui'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'colloqui_individuali'
  }
}

let colloquiGeneraliPage = {
  path: '/colloqui-generali',
  name: vue.$i18n.t('nav.func.colloqui-generali'),
  component: lazyLoad('Mastercom/Views/ColloquiGenerali'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'colloqui_generali'
  }
}

let alternanzaPage = {
  path: '/alternanza',
  name: 'Alternanza Scuola/Lavoro',
  component: lazyLoad('Mastercom/Views/Alternanza'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'alternanza'
  }
}

let pagamentiPage = {
  path: '/pagamenti',
  name: vue.$i18n.t('nav.func.pagamenti'),
  component: lazyLoad('Mastercom/Views/Pagamenti'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'pagamenti'
  }
}

let comunicazioniPage = {
  path: '/comunicazioni',
  name: vue.$i18n.t('nav.func.comunicazioni'),
  component: lazyLoad('Mastercom/Views/Comunicazioni'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'comunicazioni'
  }
}

let messengerPage = {
  path: '/messenger',
  name: vue.$i18n.t('nav.func.comunicazioni'),
  component: lazyLoad('Mastercom/Views/Messenger'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'comunicazioni'
  }
}

let bachecaPage = {
  path: '/bacheca',
  name: vue.$i18n.t('nav.func.bacheca'),
  component: lazyLoad('Mastercom/Views/Bacheca'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'notizie'
  }
}

let mensePage = {
  path: '/mense',
  name: vue.$i18n.t('nav.func.mense'),
  component: lazyLoad('Mastercom/Views/Mense'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'mense'
  }
}

let elezioniPage = {
  path: '/elezioni',
  name: vue.$i18n.t('nav.func.elezioni'),
  component: lazyLoad('Mastercom/Views/Elezioni'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'elezioni'
  }
}

let documentiPage = {
  path: '/documenti',
  name: vue.$i18n.t('nav.func.documenti'),
  component: lazyLoad('Mastercom/Views/Documenti'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'documenti'
  }
}

let materialeDidatticoPage = {
  path: '/materiale_didattico',
  name: vue.$i18n.t('nav.func.materiale'),
  component: lazyLoad('Mastercom/Views/MaterialeDidattico'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'materiale_didattico'
  }
}

let verifichePage = {
  path: '/verifiche',
  name: vue.$i18n.t('nav.func.verifiche'),
  component: lazyLoad('Mastercom/Views/Verifiche'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    //TODO: Modificare permesso
    routeName: 'verifiche'
  }
}

let serviziGiornalieriPage = {
  path: '/servizi-giornalieri',
  name: vue.$i18n.t('nav.func.servizi-giornalieri'),
  component: lazyLoad('Mastercom/Views/ServiziGiornalieri'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'servizi_giornalieri'
  }
}

let formAgenziaEntratePage = {
  path: '/agenzia-entrate',
  name: vue.$i18n.t('nav.func.ae'),
  component: lazyLoad('Mastercom/Views/AdE'),
  meta: {
    conditionalRoute: false,
    authRequired: true,
    routeName: 'agenzia_entrate'
  }
}

let negozioPage = {
  path: '/negozio',
  name: vue.$i18n.t('nav.func.negozio'),
  component: lazyLoad('Mastercom/Views/Negozio'),
  meta: {
    conditionalRoute: true,
    authRequired: true,
    routeName: 'pagamenti'
  }
}

// Mobile app route for negozio (token-based authentication)
let negozioMobilePage = {
  path: '/mobile/negozio',
  name: 'NegozioMobile',
  component: lazyLoad('Mastercom/Views/Mobile/NegozioMobile'),
  meta: {
    authRequired: false // Token validation handled within component
  }
}
// let tablesMenu = {
//   path: '/table-list',
//   component: MastercomLayout,
//   redirect: '/table-list/regular',
//   children: [
//     {
//       path: 'regular',
//       name: 'Regular Tables',
//       component: RegularTables
//     },
//     {
//       path: 'extended',
//       name: 'Extended Tables',
//       component: ExtendedTables
//     },
//     {
//       path: 'paginated',
//       name: 'Paginated Tables',
//       component: PaginatedTables
//     }]
// }

const routes = [
  {
    path: '/',
    component: MastercomLayout,
    redirect: '/home',
    children: [
      homePage,
      formAgenziaEntratePage,
      compitiPage,
      votiPage,
      competenzePage,
      argomentiPage,
      agendaPage,
      annotazioniPage,
      assenzePage,
      PagellePage,
      colloquiPage,
      colloquiGeneraliPage,
      comunicazioniPage,
      messengerPage,
      bachecaPage,
      mensePage,
      elezioniPage,
      documentiPage,
      materialeDidatticoPage,
      verifichePage,
      serviziGiornalieriPage,
      noteTutorPage,
      alternanzaPage,
      pagamentiPage,
      notePage,
      negozioPage
      // tablesMenu,
      // {
      //   path: '/admin',
      //   component: MastercomLayout,
      //   redirect: '/admin/overview',
      //   children: [
      //     {
      //       path: 'overview',
      //       name: 'Overview',
      //       component: Overview
      //     },
      //     {
      //       path: 'stats',
      //       name: 'Stats',
      //       component: Stats
      //     }
      //   ]
      // }
    ]
  },
  // Mobile app routes (using web component layout)
  negozioMobilePage,
  pagellePagePreview,
  loginPage,
  // tablesMenu,
  // lockPage,
  { path: '*', component: NotFound }
]

/**
 * Asynchronously load view (Webpack Lazy loading compatible)
 * The specified component must be inside the Views folder
 * @param  {string} name  the filename (basename) of the view to load.
 function view(name) {
   var res= require('../components/Dashboard/Views/' + name + '.vue');
   return res;
};**/

export default routes
