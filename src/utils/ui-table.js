export { sortAscending, sortDescending, columnSorter }

function columnSorter(data, prop, type) {
  let mapped = data.map(function(el, i) {
    return { index: i, value: el[prop] }
  })
  if (type === 'ascending') {
    mapped.sort(sortAscending)
  }
  if (type === 'descending') {
    mapped.sort(sortDescending)
  }
  let result = mapped.map(function(prop) {
    return data[prop.index]
  })
  return result
}

function sortAscending(a, b) {
  if (a.value < b.value) {
    return -1
  }
  if (a.value > b.value) {
    return 1
  }
  return 0
}

function sortDescending(a, b) {
  if (a.value < b.value) {
    return 1
  }
  if (a.value > b.value) {
    return -1
  }
  return 0
}
