import Vue from 'vue'
// import * as Sentry from '@sentry/browser'
// import * as Integrations from '@sentry/integrations'
import './pollyfills'
import './appFocus'
import App from './App.vue'
import router from './router'
import store from '@/store'
import './registerServiceWorker'
import './plugins/element.js'

import VueNotify from 'vue-notifyjs'
import Transitions from 'vue2-transitions'
// import ElementLocale from 'element-ui/lib/locale'

import SideBar from '@/components/UIComponents/SidebarPlugin'

// Plugins
import GlobalComponents from '@/plugins/globalComponents'
import GlobalDirectives from '@/plugins/globalDirectives'
import GlobalFilters from '@/plugins/globalFilters'
import VueDayJs from '@/plugins/VueDayJs'

// i18n setup

import 'dayjs/locale/it'

import i18n from '@/i18n'
// library imports
import './assets/sass/paper-dashboard.scss'
import './assets/sass/element_variables.scss'
import './assets/sass/mastercom.scss'

Vue.config.productionTip = false

// Raven.config('https://<EMAIL>/19')
//   .addPlugin(RavenVue, Vue)
//   .install()

// Sentry.init({
//   dsn: 'https://<EMAIL>/19',
//   integrations: [new Integrations.Vue({ Vue, attachProps: true })]
// })

Vue.use(SideBar)
Vue.use(GlobalDirectives)
Vue.use(GlobalComponents)
Vue.use(GlobalFilters)
Vue.use(Transitions)
Vue.use(VueNotify)
Vue.use(VueDayJs, {
  locale: 'it'
})

// ElementLocale.i18n((key, value) => i18n.t(key, value))

new Vue({
  el: '#app',
  router,
  store,
  i18n,
  data: {},
  render: h => h(App)
})
