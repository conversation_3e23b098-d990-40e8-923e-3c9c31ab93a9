// Import required web components
import './NegozioWebComponent.js'
import './MobileAppLayoutWebComponent.js'
// Import the shared notification component
import './NotificationComponent.js'

/**
 * Negozio Mobile Web Component
 * Mobile wrapper for the negozio view with token-based authentication
 */
class NegozioMobileWebComponent extends HTMLElement {
  constructor() {
    super()

    // Create shadow DOM
    this.attachShadow({ mode: 'open' })

    // Component state
    this.state = {
      // Token validation
      validatingToken: true,
      tokenValid: false,
      showAuthErrorModal: false,
      authErrorMessage: '',

      // URL parameters
      accessToken: null,
      studentId: null,

      // Student data (will be fetched after token validation)
      studentData: null,

      // API parameters extracted from token
      mastercomId: null,
      schoolYear: null
    }

    // Reference to notification component
    this.notificationComponent = null

    // Bind methods
    this.handleError = this.handleError.bind(this)
    this.handleAuthError = this.handleAuthError.bind(this)
  }

  // Lifecycle: Component connected to DOM
  connectedCallback() {
    this.setupNotificationComponent()
    this.render()
    this.initializeMobileView()
  }

  // Lifecycle: Component disconnected from DOM
  disconnectedCallback() {
    this.cleanup()
  }

  // Initialize mobile view
  async initializeMobileView() {
    try {
      // Extract parameters from URL
      this.extractUrlParameters()

      // Validate required parameters
      if (
        !this.state.accessToken ||
        !this.state.studentId ||
        !this.state.schoolYear ||
        !this.state.nextApiUrl
      ) {
        this.showMissingParametersInfo()
        return
      }

      // All parameters are validated via querystring, no need for token validation
      this.state.tokenValid = true
      this.setupStudentData()
      this.state.loading = false
    } catch (error) {
      console.error('Error initializing mobile view:', error)
      this.showAuthError(
        "Errore durante l'inizializzazione. Riprova più tardi."
      )
    } finally {
      this.state.validatingToken = false
      this.render()
    }
  }

  // Extract URL parameters
  extractUrlParameters() {
    const urlParams = new URLSearchParams(window.location.search)
    this.state.accessToken = urlParams.get('access-token')
    this.state.studentId = urlParams.get('id_studente')
    this.state.schoolYear = urlParams.get('id_anno_scolastico')
    this.state.nextApiUrl = urlParams.get('next_api_url')

    // Also check hash parameters (in case they're passed that way)
    if (
      !this.state.accessToken ||
      !this.state.studentId ||
      !this.state.schoolYear ||
      !this.state.nextApiUrl
    ) {
      const hashParams = new URLSearchParams(window.location.hash.substring(1))
      this.state.accessToken =
        this.state.accessToken || hashParams.get('access-token')
      this.state.studentId =
        this.state.studentId || hashParams.get('id_studente')
      this.state.schoolYear =
        this.state.schoolYear || hashParams.get('id_anno_scolastico')
      this.state.nextApiUrl =
        this.state.nextApiUrl || hashParams.get('next_api_url')
    }

    console.log('Extracted parameters:', {
      accessToken: this.state.accessToken ? '***' : null,
      studentId: this.state.studentId,
      schoolYear: this.state.schoolYear,
      nextApiUrl: this.state.nextApiUrl
    })
  }

  // Set up basic student data (since all validation is done via querystring)
  setupStudentData() {
    this.state.studentData = {
      id: this.state.studentId,
      nome: 'Studente Mobile',
      classe: '3A',
      id_scuola: '1'
    }
  }

  // Show authentication error
  showAuthError(message) {
    this.state.authErrorMessage = message
    this.state.showAuthErrorModal = true
    this.state.tokenValid = false
    this.render()
  }

  // Show missing parameters info
  showMissingParametersInfo() {
    this.state.missingParameters = true
    this.state.loading = false
    this.render()
  }

  // Handle authentication error
  handleAuthError() {
    // Close the webview or redirect back to mobile app
    if (window.ReactNativeWebView) {
      // React Native WebView
      window.ReactNativeWebView.postMessage(
        JSON.stringify({
          type: 'AUTH_ERROR',
          message: this.state.authErrorMessage
        })
      )
    } else if (window.webkit && window.webkit.messageHandlers) {
      // iOS WKWebView
      window.webkit.messageHandlers.authError.postMessage({
        message: this.state.authErrorMessage
      })
    } else if (window.Android) {
      // Android WebView
      window.Android.onAuthError(this.state.authErrorMessage)
    } else {
      // Fallback for testing in browser
      alert(this.state.authErrorMessage)
      window.close()
    }
  }
  // Setup notification component
  setupNotificationComponent() {
    if (!this.notificationComponent) {
      this.notificationComponent = document.createElement(
        'notification-component'
      )
      document.body.appendChild(this.notificationComponent)
    }
  }

  // Handle errors
  handleError(event) {
    const { message } = event.detail
    console.error('Negozio error:', message)

    // Show notification using shared component
    if (this.notificationComponent) {
      this.notificationComponent.error('Errore', message)
    }
  }

  // Setup event listeners
  setupEventListeners() {
    // Listen for clicks on modal background
    this.addEventListener('click', this.handleBackgroundClick.bind(this))
  }

  // Handle background clicks to close modal
  handleBackgroundClick(event) {
    if (event.target.classList.contains('modal-background')) {
      this.closeModal()
    }
  }

  // Cleanup
  cleanup() {
    // Remove event listeners and cleanup resources
    if (this.notificationComponent && this.notificationComponent.parentNode) {
      this.notificationComponent.parentNode.removeChild(
        this.notificationComponent
      )
      this.notificationComponent = null
    }
  }

  // Get component styles
  getStyles() {
    return `
      <style>
        :host {
          display: block;
          font-family: 'Ubuntu', sans-serif;
          background-color: white;
          min-height: 100vh;
        }

        .negozio-mobile-wrapper {
          font-family: 'Ubuntu', sans-serif;
          padding: 0;
          background-color: white;
          min-height: 100vh;
        }

        .negozio-mobile-content {
          /* The web component will handle its own styling */
        }

        /* Modal styles */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1000;
        }

        .modal-content {
          background: white;
          border-radius: 8px;
          width: 95%;
          max-width: 500px;
          max-height: 90vh;
          overflow-y: auto;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .modal-header {
          padding: 20px;
          border-bottom: 1px solid #e0e0e0;
          background: #f8f9fa;
        }

        .modal-title {
          font-family: 'Ubuntu', sans-serif;
          font-weight: 600;
          font-size: 1.3rem;
          margin: 0;
        }

        .modal-body {
          padding: 20px;
        }

        .auth-error-content {
          text-align: center;
        }

        .auth-error-icon {
          font-size: 4rem;
          color: #ff6b6b;
          margin-bottom: 20px;
        }

        .auth-error-message {
          font-size: 1.1rem;
          color: #666;
          line-height: 1.5;
          margin: 0;
        }



        .modal-footer {
          padding: 20px;
          border-top: 1px solid #e0e0e0;
          display: flex;
          justify-content: flex-end;
          gap: 10px;
        }

        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          font-family: 'Ubuntu', sans-serif;
          cursor: pointer;
          font-size: 0.9rem;
          min-width: 100px;
        }

        .btn-secondary {
          background: #6c757d;
          color: white;
        }

        .btn-primary {
          background: #2c5aa0;
          color: white;
        }

        .btn:hover {
          opacity: 0.9;
        }

        .hidden {
          display: none;
        }

        .missing-parameters-info {
          padding: 40px 20px;
          text-align: center;
          max-width: 600px;
          margin: 0 auto;
          background: white;
          border-radius: 12px;
          box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .missing-parameters-info .info-icon {
          font-size: 3rem;
          margin-bottom: 20px;
        }

        .missing-parameters-info h3 {
          color: #333;
          font-size: 1.5rem;
          margin-bottom: 16px;
          font-weight: 600;
        }

        .missing-parameters-info p {
          color: #666;
          font-size: 1.1rem;
          line-height: 1.5;
          margin-bottom: 20px;
        }

        .missing-parameters-info ul {
          text-align: left;
          max-width: 400px;
          margin: 20px auto;
          padding: 0;
          list-style: none;
        }

        .missing-parameters-info li {
          color: #555;
          font-size: 1rem;
          line-height: 1.6;
          margin-bottom: 12px;
          padding-left: 20px;
          position: relative;
        }

        .missing-parameters-info li:before {
          content: "•";
          color: #2c5aa0;
          font-weight: bold;
          position: absolute;
          left: 0;
        }

        .missing-parameters-info strong {
          color: #2c5aa0;
          font-weight: 600;
        }
      </style>
    `
  }

  // Render component
  render() {
    const styles = this.getStyles()

    const userDataJson = this.state.studentData
      ? JSON.stringify({
          id: this.state.studentData.id,
          nome: this.state.studentData.nome,
          classe: this.state.studentData.classe,
          scuola: this.state.studentData.id_scuola
        })
      : '{}'

    const apiParamsJson =
      this.state.studentId && this.state.schoolYear && this.state.nextApiUrl
        ? JSON.stringify({
            studentId: this.state.studentId,
            schoolYear: this.state.schoolYear,
            nextApiUrl: this.state.nextApiUrl
          })
        : '{}'

    // Main content
    let mainContent = ''
    if (this.state.missingParameters) {
      mainContent = `
        <div class="missing-parameters-info">
          <div class="info-icon">ℹ️</div>
          <h3>Parametri richiesti mancanti</h3>
          <p>Per accedere al negozio sono necessari i seguenti parametri:</p>
          <ul>
            <li><strong>access-token</strong>: Token di autenticazione</li>
            <li><strong>id_studente</strong>: ID dello studente</li>
            <li><strong>id_anno_scolastico</strong>: Anno scolastico</li>
            <li><strong>next_api_url</strong>: URL dell'API (URL encoded)</li>
          </ul>
          <p>Assicurati che tutti i parametri siano presenti nell'URL.</p>
        </div>
      `
    } else if (this.state.tokenValid) {
      mainContent = `
        <div class="negozio-mobile-content">
          <negozio-web-component
            user-data='${userDataJson}'
            auth-token="${this.state.accessToken || ''}"
            api-params='${apiParamsJson}'
            loading="false"
          ></negozio-web-component>
        </div>
      `
    }

    // Auth error modal
    const authErrorModal = this.state.showAuthErrorModal
      ? `
        <div class="modal-overlay modal-background">
          <div class="modal-content">
            <div class="modal-header">
              <h3 class="modal-title">Accesso non autorizzato</h3>
            </div>
            <div class="modal-body">
              <div class="auth-error-content">
                <div class="auth-error-icon">
                  <i class="ti-lock"></i>
                </div>
                <p class="auth-error-message">${this.state.authErrorMessage}</p>
              </div>
            </div>
            <div class="modal-footer">
              <button class="btn btn-primary" onclick="this.getRootNode().host.handleAuthError()">
                Chiudi
              </button>
            </div>
          </div>
        </div>
      `
      : ''
    this.shadowRoot.innerHTML = `
      ${styles}
      <mobile-app-layout
        show-header="true"
        show-footer="false"
        page-title="Negozio"
        global-loading="${this.state.validatingToken}"
      >
        <div class="negozio-mobile-wrapper">
          ${mainContent}
        </div>
      </mobile-app-layout>
      ${authErrorModal}
    `

    // Add event listeners to the negozio component
    if (this.state.tokenValid) {
      const negozioComponent = this.shadowRoot.querySelector(
        'negozio-web-component'
      )
      if (negozioComponent) {
        negozioComponent.addEventListener('error', this.handleError)
      }
    }
  }
}

// Register the custom element
if (!customElements.get('negozio-mobile-component')) {
  customElements.define('negozio-mobile-component', NegozioMobileWebComponent)
}

export default NegozioMobileWebComponent
