# Shared Notification System

The `NotificationComponent` provides a shared notification system that can be used across all web components in the application.

## Features

- **Framework Independent**: Works in any environment (Vue, React, Angular, plain JS)
- **Shared Instance**: Single notification container for all components
- **Auto-dismiss**: Notifications automatically disappear after a set duration
- **Manual Dismiss**: Users can manually close notifications
- **Multiple Types**: Support for success, error, warning, and info notifications
- **Responsive**: Adapts to mobile screens
- **Accessible**: Proper contrast and readable text sizes

## Usage

### 1. Import the Component

```javascript
import './NotificationComponent.js'
```

### 2. Setup in Your Web Component

```javascript
class MyWebComponent extends HTMLElement {
  constructor() {
    super()
    this.notificationComponent = null
  }

  connectedCallback() {
    this.setupNotificationComponent()
    // ... rest of your setup
  }

  disconnectedCallback() {
    this.cleanup()
  }

  setupNotificationComponent() {
    if (!this.notificationComponent) {
      this.notificationComponent = document.createElement('notification-component')
      document.body.appendChild(this.notificationComponent)
    }
  }

  cleanup() {
    if (this.notificationComponent && this.notificationComponent.parentNode) {
      this.notificationComponent.parentNode.removeChild(this.notificationComponent)
      this.notificationComponent = null
    }
  }

  // Show notifications
  showSuccess(title, message) {
    if (this.notificationComponent) {
      this.notificationComponent.success(title, message)
    }
  }

  showError(title, message) {
    if (this.notificationComponent) {
      this.notificationComponent.error(title, message)
    }
  }
}
```

### 3. Available Methods

```javascript
// Add notifications with different types
notificationComponent.success('Success!', 'Operation completed successfully')
notificationComponent.error('Error!', 'Something went wrong')
notificationComponent.warning('Warning!', 'Please check your input')
notificationComponent.info('Info', 'Here is some information')

// Add notification with custom duration (in milliseconds)
notificationComponent.success('Success!', 'Custom duration', 10000)

// Generic method
notificationComponent.addNotification('success', 'Title', 'Message', 5000)

// Clear all notifications
notificationComponent.clearAll()

// Remove specific notification (returns notification ID)
const id = notificationComponent.success('Title', 'Message')
notificationComponent.removeNotification(id)
```

## Styling

The notification component uses a fixed position in the top-right corner and includes:

- **Success**: Green left border (#67c23a)
- **Error**: Red left border (#f56c6c)  
- **Warning**: Orange left border (#e6a23c)
- **Info**: Blue left border (#409eff)

The component is fully responsive and adapts to mobile screens by expanding to full width with smaller margins.

## Examples

### Basic Error Handling

```javascript
try {
  await someApiCall()
  this.notificationComponent.success('Success', 'Data loaded successfully')
} catch (error) {
  this.notificationComponent.error('Error', error.message)
}
```

### Form Validation

```javascript
validateForm() {
  if (!this.isValid) {
    this.notificationComponent.warning('Validation Error', 'Please fill all required fields')
    return false
  }
  return true
}
```

### Information Messages

```javascript
onDataUpdate() {
  this.notificationComponent.info('Data Updated', 'Your changes have been saved automatically')
}
```

## Current Usage

- **NegozioDesktopWebComponent**: Uses for API errors and general notifications
- **NegozioMobileWebComponent**: Uses for API errors and general notifications

## Best Practices

1. **Single Instance**: Only create one notification component per web component
2. **Cleanup**: Always remove the notification component in `disconnectedCallback()`
3. **Appropriate Types**: Use the correct notification type for the message context
4. **Clear Messages**: Keep titles short and messages descriptive but concise
5. **Duration**: Use longer durations for error messages, shorter for success messages
