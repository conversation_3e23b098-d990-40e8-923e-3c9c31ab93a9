/**
 * Notification Web Component
 * Shared notification system for all web components
 * Can be used independently of any framework
 */
class NotificationComponent extends HTMLElement {
  constructor() {
    super()

    // Create shadow DOM
    this.attachShadow({ mode: 'open' })

    // Component state
    this.state = {
      notifications: []
    }

    // Bind methods
    this.removeNotification = this.removeNotification.bind(this)
  }

  // Lifecycle: Component connected to DOM
  connectedCallback() {
    this.render()
  }

  // Add a new notification
  addNotification(type, title, message, duration = null) {
    const notification = {
      id: Date.now() + Math.random(),
      type,
      title,
      message,
      timestamp: Date.now()
    }

    this.state.notifications.push(notification)
    this.render()

    // Auto-remove notification after delay
    const autoRemoveDuration = duration || (type === 'error' ? 5000 : 3000)
    setTimeout(() => {
      this.removeNotification(notification.id)
    }, autoRemoveDuration)

    return notification.id
  }

  // Remove notification by ID
  removeNotification(notificationId) {
    this.state.notifications = this.state.notifications.filter(
      n => n.id !== notificationId
    )
    this.render()
  }

  // Clear all notifications
  clearAll() {
    this.state.notifications = []
    this.render()
  }

  // Convenience methods for different notification types
  success(title, message, duration) {
    return this.addNotification('success', title, message, duration)
  }

  error(title, message, duration) {
    return this.addNotification('error', title, message, duration)
  }

  warning(title, message, duration) {
    return this.addNotification('warning', title, message, duration)
  }

  info(title, message, duration) {
    return this.addNotification('info', title, message, duration)
  }

  // Get component styles
  getStyles() {
    return `
      <style>
        :host {
          position: fixed;
          top: 20px;
          right: 20px;
          z-index: 10000;
          max-width: 400px;
          pointer-events: none;
        }

        .notification {
          background: #fff;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          margin-bottom: 10px;
          padding: 16px;
          border-left: 4px solid;
          font-family: 'Ubuntu', sans-serif;
          animation: slideIn 0.3s ease-out;
          pointer-events: auto;
          position: relative;
        }

        .notification.success {
          border-left-color: #67c23a;
        }

        .notification.error {
          border-left-color: #f56c6c;
        }

        .notification.warning {
          border-left-color: #e6a23c;
        }

        .notification.info {
          border-left-color: #409eff;
        }

        .notification-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 8px;
        }

        .notification-title {
          font-weight: 600;
          font-size: 14px;
          color: #303133;
          margin: 0;
        }

        .notification-close {
          background: none;
          border: none;
          font-size: 18px;
          color: #909399;
          cursor: pointer;
          padding: 0;
          width: 20px;
          height: 20px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          transition: all 0.2s ease;
        }

        .notification-close:hover {
          color: #606266;
          background-color: #f5f7fa;
        }

        .notification-message {
          font-size: 13px;
          color: #606266;
          line-height: 1.4;
          margin: 0;
        }

        @keyframes slideIn {
          from {
            transform: translateX(100%);
            opacity: 0;
          }
          to {
            transform: translateX(0);
            opacity: 1;
          }
        }

        @keyframes slideOut {
          from {
            transform: translateX(0);
            opacity: 1;
          }
          to {
            transform: translateX(100%);
            opacity: 0;
          }
        }

        .notification.removing {
          animation: slideOut 0.3s ease-in forwards;
        }

        /* Responsive design */
        @media (max-width: 480px) {
          :host {
            left: 10px;
            right: 10px;
            top: 10px;
            max-width: none;
          }

          .notification {
            margin-bottom: 8px;
            padding: 12px;
          }

          .notification-title {
            font-size: 13px;
          }

          .notification-message {
            font-size: 12px;
          }
        }
      </style>
    `
  }

  // Render component
  render() {
    const styles = this.getStyles()

    const notificationsHtml = this.state.notifications
      .map(
        notification => `
      <div class="notification ${notification.type}" data-id="${
          notification.id
        }">
        <div class="notification-header">
          <h4 class="notification-title">${this.escapeHtml(
            notification.title
          )}</h4>
          <button class="notification-close" onclick="this.getRootNode().host.removeNotification(${
            notification.id
          })">
            ×
          </button>
        </div>
        <p class="notification-message">${this.escapeHtml(
          notification.message
        )}</p>
      </div>
    `
      )
      .join('')

    this.shadowRoot.innerHTML = `
      ${styles}
      ${notificationsHtml}
    `
  }

  // Escape HTML to prevent XSS
  escapeHtml(text) {
    const div = document.createElement('div')
    div.textContent = text
    return div.innerHTML
  }
}

// Register the custom element
if (!customElements.get('notification-component')) {
  customElements.define('notification-component', NotificationComponent)
}

export default NotificationComponent
