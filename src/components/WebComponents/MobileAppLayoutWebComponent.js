/**
 * Mobile App Layout Web Component
 * A pure JavaScript web component for mobile app webview layouts
 */
class MobileAppLayoutWebComponent extends HTMLElement {
  constructor() {
    super()

    // Create shadow DOM
    this.attachShadow({ mode: 'open' })

    // Component state
    this.state = {
      showHeader: true,
      showFooter: false,
      pageTitle: '',
      globalLoading: false,
      appVersion: '1.0.0'
    }

    // Bind methods
    this.setupMobileViewport = this.setupMobileViewport.bind(this)
    this.handleSlotChange = this.handleSlotChange.bind(this)
  }

  // Define observed attributes
  static get observedAttributes() {
    return [
      'show-header',
      'show-footer',
      'page-title',
      'global-loading',
      'app-version'
    ]
  }

  // Lifecycle: Component connected to DOM
  connectedCallback() {
    this.setupMobileViewport()
    this.addMobileAppClass()
    this.render()
    this.setupEventListeners()
  }

  // Lifecycle: Component disconnected from DOM
  disconnectedCallback() {
    this.cleanup()
  }

  // Lifecycle: Attribute changed
  attributeChangedCallback(name, oldValue, newValue) {
    if (oldValue !== newValue) {
      switch (name) {
        case 'show-header':
          this.state.showHeader = newValue !== 'false'
          break
        case 'show-footer':
          this.state.showFooter = newValue === 'true'
          break
        case 'page-title':
          this.state.pageTitle = newValue || ''
          break
        case 'global-loading':
          this.state.globalLoading = newValue === 'true'
          break
        case 'app-version':
          this.state.appVersion = newValue || '1.0.0'
          break
      }
      this.render()
    }
  }

  // Setup mobile viewport
  setupMobileViewport() {
    let viewport = document.querySelector('meta[name=viewport]')
    if (!viewport) {
      viewport = document.createElement('meta')
      viewport.name = 'viewport'
      document.head.appendChild(viewport)
    }
    viewport.content =
      'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no'
  }

  // Add mobile app class to body
  addMobileAppClass() {
    document.body.classList.add('mobile-app-view')
  }

  // Setup event listeners
  setupEventListeners() {
    // Listen for slot changes to re-render if needed
    const slots = this.shadowRoot.querySelectorAll('slot')
    slots.forEach(slot => {
      slot.addEventListener('slotchange', this.handleSlotChange)
    })
  }

  // Handle slot changes
  handleSlotChange() {
    // Re-render if slot content changes
    this.render()
  }

  // Cleanup
  cleanup() {
    document.body.classList.remove('mobile-app-view')

    // Remove event listeners
    const slots = this.shadowRoot.querySelectorAll('slot')
    slots.forEach(slot => {
      slot.removeEventListener('slotchange', this.handleSlotChange)
    })
  }

  // Get component styles
  getStyles() {
    return `
      <style>
        :host {
          display: flex;
          flex-direction: column;
          min-height: 100vh;
          font-family: 'Ubuntu', sans-serif;
          background-color: white;
        }

        .mobile-header {
          background: #fff;
          border-bottom: 1px solid #e0e0e0;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
          position: sticky;
          top: 0;
          z-index: 1000;
        }

        .mobile-header-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 12px 16px;
          max-width: 100%;
        }

        .mobile-title {
          font-size: 1.2rem;
          font-weight: 600;
          color: #333;
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .mobile-actions {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .mobile-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          position: relative;
        }

        .mobile-content.with-header {
          /* Account for header height if needed */
        }

        .mobile-content-inner {
          flex: 1;
          position: relative;
          overflow-x: hidden;
        }

        .mobile-loading-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(255, 255, 255, 0.9);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 999;
        }

        .mobile-loading-spinner {
          text-align: center;
          color: #666;
        }

        .mobile-loading-spinner i {
          font-size: 2rem;
          margin-bottom: 10px;
          animation: spin 1s linear infinite;
        }

        .mobile-loading-spinner p {
          font-size: 1rem;
          margin: 0;
        }

        @keyframes spin {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }

        .mobile-footer {
          background: #fff;
          border-top: 1px solid #e0e0e0;
          padding: 8px 16px;
        }

        .mobile-footer-content {
          text-align: center;
        }

        .mobile-app-version {
          font-size: 0.8rem;
          color: #999;
        }

        /* Responsive adjustments */
        @media (max-width: 480px) {
          .mobile-header-content {
            padding: 10px 12px;
          }

          .mobile-title {
            font-size: 1.1rem;
          }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
          :host {
            background-color: #1a1a1a;
            color: #fff;
          }

          .mobile-header {
            background: #2d2d2d;
            border-bottom-color: #404040;
          }

          .mobile-footer {
            background: #2d2d2d;
            border-top-color: #404040;
          }
        }

        /* Hide elements based on state */
        .mobile-header.hidden {
          display: none;
        }

        .mobile-footer.hidden {
          display: none;
        }

        .mobile-loading-overlay.hidden {
          display: none;
        }
      </style>
    `
  }

  // Render component
  render() {
    const styles = this.getStyles()

    const headerHtml = this.state.showHeader
      ? `
        <div class="mobile-header">
          <div class="mobile-header-content">
            <div class="mobile-title">
              <slot name="header-title">${this.state.pageTitle}</slot>
            </div>
            <div class="mobile-actions">
              <slot name="header-actions"></slot>
            </div>
          </div>
        </div>
      `
      : ''

    const footerHtml = this.state.showFooter
      ? `
        <div class="mobile-footer">
          <slot name="footer">
            <div class="mobile-footer-content">
              <span class="mobile-app-version">v${this.state.appVersion}</span>
            </div>
          </slot>
        </div>
      `
      : ''

    const loadingOverlay = this.state.globalLoading
      ? `
        <div class="mobile-loading-overlay">
          <div class="mobile-loading-spinner">
            <i class="ti-reload"></i>
            <p>Loading...</p>
          </div>
        </div>
      `
      : ''

    this.shadowRoot.innerHTML = `
      ${styles}
      ${headerHtml}
      <div class="mobile-content ${this.state.showHeader ? 'with-header' : ''}">
        <div class="mobile-content-inner">
          ${loadingOverlay}
          <slot></slot>
        </div>
      </div>
      ${footerHtml}
    `
  }

  // Public methods for external control
  setLoading(loading) {
    this.state.globalLoading = loading
    this.render()
  }

  setTitle(title) {
    this.state.pageTitle = title
    this.render()
  }

  showHeader(show = true) {
    this.state.showHeader = show
    this.render()
  }

  showFooter(show = true) {
    this.state.showFooter = show
    this.render()
  }
}

// Register the custom element
if (!customElements.get('mobile-app-layout')) {
  customElements.define('mobile-app-layout', MobileAppLayoutWebComponent)
}

export default MobileAppLayoutWebComponent
