/**
 * ModalWebComponent - General purpose modal component
 * Displays any content in a modal overlay with customizable options
 */
class ModalWebComponent extends HTMLElement {
  constructor() {
    super()

    // Create shadow DOM
    this.attachShadow({ mode: 'open' })

    // Component state
    this.state = {
      isOpen: false,
      title: '',
      showCloseButton: true,
      showFooter: false,
      footerButtons: []
    }

    // Bind methods
    this.close = this.close.bind(this)
    this.handleBackgroundClick = this.handleBackgroundClick.bind(this)
    this.handleKeyDown = this.handleKeyDown.bind(this)
  }

  // Observed attributes
  static get observedAttributes() {
    return ['open', 'title', 'show-close-button', 'show-footer']
  }

  // Handle attribute changes
  attributeChangedCallback(name, oldValue, newValue) {
    switch (name) {
      case 'open':
        this.state.isOpen = newValue === 'true'
        break
      case 'title':
        this.state.title = newValue || ''
        break
      case 'show-close-button':
        this.state.showCloseButton = newValue !== 'false'
        break
      case 'show-footer':
        this.state.showFooter = newValue === 'true'
        break
    }
    this.render()
  }

  // Lifecycle: Component connected to DOM
  connectedCallback() {
    this.render()
    this.setupEventListeners()
  }

  // Lifecycle: Component disconnected from DOM
  disconnectedCallback() {
    this.cleanup()
  }

  // Setup event listeners
  setupEventListeners() {
    document.addEventListener('keydown', this.handleKeyDown)
  }

  // Cleanup
  cleanup() {
    document.removeEventListener('keydown', this.handleKeyDown)
  }

  // Open modal
  open(options = {}) {
    this.state.isOpen = true
    this.state.title = options.title || ''
    this.state.showCloseButton = options.showCloseButton !== false
    this.state.showFooter = options.showFooter || false
    this.state.footerButtons = options.footerButtons || []
    this.render()

    // Dispatch open event
    this.dispatchEvent(new CustomEvent('modal-opened', { bubbles: true }))
  }

  // Close modal
  close() {
    this.state.isOpen = false
    this.render()

    // Dispatch close event
    this.dispatchEvent(new CustomEvent('modal-closed', { bubbles: true }))
  }

  // Handle background click
  handleBackgroundClick(event) {
    if (event.target.classList.contains('modal-overlay')) {
      this.close()
    }
  }

  // Handle keyboard events
  handleKeyDown(event) {
    if (this.state.isOpen && event.key === 'Escape') {
      this.close()
    }
  }

  // Add footer button
  addFooterButton(button) {
    this.state.footerButtons.push(button)
    this.render()
  }

  // Clear footer buttons
  clearFooterButtons() {
    this.state.footerButtons = []
    this.render()
  }

  // Get component styles
  getStyles() {
    return `
      <style>
        :host {
          display: ${this.state.isOpen ? 'block' : 'none'};
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          z-index: 1000;
        }

        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          animation: fadeIn 0.2s ease-out;
        }

        .modal-content {
          background: white;
          border-radius: 8px;
          width: 95%;
          max-width: 600px;
          max-height: 90vh;
          overflow: hidden;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
          animation: slideIn 0.2s ease-out;
          display: flex;
          flex-direction: column;
        }

        .modal-header {
          padding: 20px;
          border-bottom: 1px solid #e0e0e0;
          background: #f8f9fa;
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-shrink: 0;
        }

        .modal-title {
          font-family: 'Ubuntu', sans-serif;
          font-weight: 600;
          font-size: 1.3rem;
          margin: 0;
          color: #333;
        }

        .close-button {
          background: none;
          border: none;
          font-size: 1.5rem;
          cursor: pointer;
          color: #666;
          padding: 0;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          transition: background-color 0.2s;
        }

        .close-button:hover {
          background-color: #e9ecef;
        }

        .modal-body {
          padding: 20px;
          overflow-y: auto;
          flex: 1;
        }

        .modal-footer {
          padding: 20px;
          border-top: 1px solid #e0e0e0;
          display: flex;
          justify-content: flex-end;
          gap: 10px;
          flex-shrink: 0;
        }

        .btn {
          padding: 8px 16px;
          border: none;
          border-radius: 4px;
          font-family: 'Ubuntu', sans-serif;
          cursor: pointer;
          font-size: 0.9rem;
          min-width: 100px;
          transition: opacity 0.2s;
        }

        .btn:hover {
          opacity: 0.9;
        }

        .btn-primary {
          background: #2c5aa0;
          color: white;
        }

        .btn-secondary {
          background: #6c757d;
          color: white;
        }

        .btn-danger {
          background: #dc3545;
          color: white;
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes slideIn {
          from { 
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
          }
          to { 
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        @media (max-width: 768px) {
          .modal-content {
            width: 98%;
            max-height: 95vh;
          }

          .modal-header,
          .modal-body,
          .modal-footer {
            padding: 15px;
          }
        }
      </style>
    `
  }

  // Render component
  render() {
    const styles = this.getStyles()

    const headerContent =
      this.state.title || this.state.showCloseButton
        ? `
      <div class="modal-header">
        ${
          this.state.title
            ? `<h3 class="modal-title">${this.state.title}</h3>`
            : '<div></div>'
        }
        ${
          this.state.showCloseButton
            ? `
          <button class="close-button" onclick="this.getRootNode().host.close()">
            ×
          </button>
        `
            : ''
        }
      </div>
    `
        : ''

    const footerContent =
      this.state.showFooter && this.state.footerButtons.length > 0
        ? `
      <div class="modal-footer">
        ${this.state.footerButtons
          .map(
            button => `
          <button class="btn ${button.class ||
            'btn-secondary'}" onclick="${button.onclick || ''}">
            ${button.text}
          </button>
        `
          )
          .join('')}
      </div>
    `
        : ''

    this.shadowRoot.innerHTML = `
      ${styles}
      <div class="modal-overlay" onclick="this.getRootNode().host.handleBackgroundClick(event)">
        <div class="modal-content">
          ${headerContent}
          <div class="modal-body">
            <slot></slot>
          </div>
          ${footerContent}
        </div>
      </div>
    `
  }
}

// Register the custom element
if (!customElements.get('modal-web-component')) {
  customElements.define('modal-web-component', ModalWebComponent)
}

export default ModalWebComponent
