// Import the negozio web component
import './NegozioWebComponent.js'
// Import the shared notification component
import './NotificationComponent.js'

/**
 * Negozio Desktop Web Component
 * Desktop wrapper for the negozio view with standard authentication
 */
class NegozioDesktopWebComponent extends HTMLElement {
  constructor() {
    super()

    // Create shadow DOM
    this.attachShadow({ mode: 'open' })

    // Component state
    this.state = {
      active: false,
      authObject: null,
      studenteCorrente: null
    }

    // Reference to notification component
    this.notificationComponent = null

    // Bind methods
    this.handleError = this.handleError.bind(this)
  }

  // Define observed attributes
  static get observedAttributes() {
    return ['auth-object', 'studente-corrente']
  }

  // Lifecycle: Component connected to DOM
  connectedCallback() {
    this.setupNotificationComponent()
    this.render()
    this.setupEventListeners()
    // this.doLoadNegozio()
  }

  // Lifecycle: Component disconnected from DOM
  disconnectedCallback() {
    this.cleanup()
  }

  // Lifecycle: Attribute changed
  attributeChangedCallback(name, oldValue, newValue) {
    if (oldValue !== newValue) {
      switch (name) {
        case 'auth-object':
          this.state.authObject = newValue ? JSON.parse(newValue) : null
          break
        case 'studente-corrente':
          this.state.studenteCorrente = newValue ? JSON.parse(newValue) : null
          break
      }
    }
  }

  // // Load negozio data and check permissions
  // async doLoadNegozio() {
  //   this.state.loading = true
  //   this.render()

  //   try {
  //     // Check if negozio section is enabled (using pagamenti permission for early dev)
  //     this.state.active = this.isSectionEnabled('pagamenti')

  //     if (this.state.active) {
  //       await this.loadNegozioData()
  //     }
  //   } catch (error) {
  //     console.error('Error loading negozio:', error)
  //     this.dispatchEvent(
  //       new CustomEvent('error', {
  //         detail: { message: 'Errore nel caricamento del negozio' }
  //       })
  //     )
  //   } finally {
  //     this.state.loading = false
  //     this.render()
  //   }
  // }

  // Check if section is enabled (simplified version)
  isSectionEnabled() {
    // This is a simplified version - in a real implementation,
    // this would check the user's permissions
    return true // For development, always return true
  }

  // Handle errors from the negozio component
  handleError(event) {
    const { message } = event.detail
    this.showNotification('error', 'Errore', message)
  }

  // Setup notification component
  setupNotificationComponent() {
    if (!this.notificationComponent) {
      this.notificationComponent = document.createElement(
        'notification-component'
      )
      document.body.appendChild(this.notificationComponent)
    }
  }

  // Show notification using shared component
  showNotification(type, title, message) {
    if (this.notificationComponent) {
      this.notificationComponent.addNotification(type, title, message)
    }
  }

  // Setup event listeners
  setupEventListeners() {
    // Event listeners for negozio component will be set up in render()
  }

  // Cleanup
  cleanup() {
    // Remove event listeners and cleanup resources
    if (this.notificationComponent && this.notificationComponent.parentNode) {
      this.notificationComponent.parentNode.removeChild(
        this.notificationComponent
      )
      this.notificationComponent = null
    }
  }

  // Get component styles
  getStyles() {
    return `
      <style>
        :host {
          display: block;
          font-family: 'Ubuntu', sans-serif;
          padding: 20px;
          background-color: #fff;
          min-height: 500px;
        }

        .section-disabled {
          padding: 40px;
          text-align: center;
          border: 1px solid #e0e0e0;
          border-radius: 8px;
        }

        .section-disabled h3 {
          color: #333;
          margin-bottom: 15px;
          font-family: 'Ubuntu', sans-serif;
        }

        .section-disabled p {
          color: #666;
          font-family: 'Ubuntu', sans-serif;
        }

        .negozio-wrapper {
          /* The negozio web component will handle its own styling */
        }

      </style>
    `
  }

  // Render component
  render() {
    const styles = this.getStyles()
    const userDataJson = this.state.studenteCorrente
      ? JSON.stringify({
          id: this.state.studenteCorrente.id,
          nome: this.state.studenteCorrente.nome,
          classe: this.state.studenteCorrente.classe,
          scuola: this.state.studenteCorrente.id_scuola
        })
      : '{}'

    const authToken =
      this.state.authObject && this.state.authObject.token
        ? this.state.authObject.token
        : null

    // Construct API parameters for desktop (nextApiUrl is null for desktop API calls)
    const apiParamsJson =
      this.state.studenteCorrente && this.state.authObject
        ? JSON.stringify({
            studentId: this.state.studenteCorrente.id,
            schoolYear: this.state.authObject.anno,
            nextApiUrl: 'https://demo4.registroelettronico.com' // Desktop uses standard API endpoint, not next-api
          })
        : '{}'

    const content = `
      <div class="negozio-wrapper">
        <negozio-web-component
          user-data='${userDataJson}'
          auth-token="${authToken || ''}"
          api-params='${apiParamsJson}'
        ></negozio-web-component>
      </div>
    `

    this.shadowRoot.innerHTML = `
      ${styles}
      <div class="app-genitori" id="container-negozio">
        ${content}
      </div>
    `

    // Add event listeners to the negozio component
    const negozioComponent = this.shadowRoot.querySelector(
      'negozio-web-component'
    )
    if (negozioComponent) {
      negozioComponent.addEventListener('error', this.handleError)
    }
  }
}

// Register the custom element
if (!customElements.get('negozio-desktop-component')) {
  customElements.define('negozio-desktop-component', NegozioDesktopWebComponent)
}

export default NegozioDesktopWebComponent
