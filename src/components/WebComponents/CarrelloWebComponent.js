/**
 * <PERSON>elloWebComponent - Shopping cart component
 * Displays a floating cart icon with item count and shows cart contents in a modal
 */
class CarrelloWebComponent extends HTMLElement {
  constructor() {
    super()

    // Create shadow DOM
    this.attachShadow({ mode: 'open' })

    // Component state
    this.state = {
      cartItems: [], // Array of cart items
      isModalOpen: false
    }

    // Bind methods
    this.handleCartClick = this.handleCartClick.bind(this)
    this.closeModal = this.closeModal.bind(this)
    this.handleBackgroundClick = this.handleBackgroundClick.bind(this)
    this.handleKeyDown = this.handleKeyDown.bind(this)
  }

  // Lifecycle: Component connected to DOM
  connectedCallback() {
    this.render()
    this.setupEventListeners()
  }

  // Lifecycle: Component disconnected from DOM
  disconnectedCallback() {
    this.cleanup()
  }

  // Setup event listeners
  setupEventListeners() {
    document.addEventListener('keydown', this.handleKeyDown)
  }

  // Cleanup
  cleanup() {
    document.removeEventListener('keydown', this.handleKeyDown)
  }

  // Add item to cart
  addItem(item) {
    // Check if item with same product and options already exists
    const existingItemIndex = this.state.cartItems.findIndex(
      cartItem =>
        cartItem.productId === item.productId &&
        JSON.stringify(cartItem.selectedOptions) ===
          JSON.stringify(item.selectedOptions)
    )

    if (existingItemIndex !== -1) {
      // Update quantity of existing item
      this.state.cartItems[existingItemIndex].quantity += item.quantity
    } else {
      // Add new item to cart
      this.state.cartItems.push({
        id: Date.now() + Math.random(), // Simple unique ID
        productId: item.productId,
        productName: item.productName,
        quantity: item.quantity,
        selectedOptions: item.selectedOptions || {},
        price: item.price
      })
    }

    this.render()

    // Animate cart icon when item is added
    this.animateCartIcon()

    // Dispatch cart updated event
    this.dispatchEvent(
      new CustomEvent('cart-updated', {
        detail: {
          cartItems: this.state.cartItems,
          totalItems: this.getTotalItems()
        },
        bubbles: true
      })
    )
  }

  // Animate cart icon when item is added
  animateCartIcon() {
    const cartBox = this.shadowRoot.querySelector('.cart-floating-box')
    if (cartBox) {
      // Add animation class
      cartBox.classList.add('cart-item-added')

      // Remove animation class after animation completes
      setTimeout(() => {
        cartBox.classList.remove('cart-item-added')
      }, 800) // Match the animation duration
    }
  }

  // Remove item from cart
  removeItem(itemId) {
    this.state.cartItems = this.state.cartItems.filter(
      item => item.id !== itemId
    )
    this.render()

    // Dispatch cart updated event
    this.dispatchEvent(
      new CustomEvent('cart-updated', {
        detail: {
          cartItems: this.state.cartItems,
          totalItems: this.getTotalItems()
        },
        bubbles: true
      })
    )
  }

  // Get total number of items in cart
  getTotalItems() {
    return this.state.cartItems.reduce(
      (total, item) => total + item.quantity,
      0
    )
  }

  // Get cart label text
  getCartLabel() {
    const totalItems = this.getTotalItems()
    if (totalItems === 0) {
      return 'no product in cart'
    } else if (totalItems === 1) {
      return '1 product in cart'
    } else {
      return `${totalItems} products in cart`
    }
  }

  // Handle cart click
  handleCartClick() {
    this.state.isModalOpen = true
    this.render()
  }

  // Close modal
  closeModal() {
    this.state.isModalOpen = false
    this.render()
  }

  // Handle background click
  handleBackgroundClick(event) {
    if (event.target.classList.contains('modal-overlay')) {
      this.closeModal()
    }
  }

  // Handle keyboard events
  handleKeyDown(event) {
    if (this.state.isModalOpen && event.key === 'Escape') {
      this.closeModal()
    }
  }

  // Format selected options for display
  formatSelectedOptions(selectedOptions) {
    if (!selectedOptions || Object.keys(selectedOptions).length === 0) {
      return ''
    }

    return Object.entries(selectedOptions)
      .map(([key, value]) => `${key}: ${value}`)
      .join(', ')
  }

  // Get component styles
  getStyles() {
    return `
      <style>
        :host {
          position: fixed;
          bottom: 20px;
          right: 20px;
          z-index: 999;
          font-family: 'Ubuntu', sans-serif;
        }

        .cart-floating-box {
          background: #2c5aa0;
          color: white;
          padding: 12px 16px;
          border-radius: 25px;
          cursor: pointer;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          display: flex;
          align-items: center;
          gap: 8px;
          transition: all 0.3s ease;
          min-width: 160px;
          justify-content: center;
        }

        .cart-floating-box:hover {
          background: #1e3d6f;
          transform: translateY(-2px);
          box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
        }

        /* Animation when item is added to cart */
        .cart-floating-box.cart-item-added {
          animation: cartItemAdded 0.8s ease-out;
        }

        @keyframes cartItemAdded {
          0% {
            transform: scale(1) translateY(0);
            background: #2c5aa0;
          }
          15% {
            transform: scale(1.1) translateY(-3px);
            background: #28a745;
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.4);
          }
          30% {
            transform: scale(1.15) translateY(-5px);
            background: #28a745;
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.5);
          }
          50% {
            transform: scale(1.05) translateY(-2px);
            background: #28a745;
            box-shadow: 0 6px 16px rgba(40, 167, 69, 0.3);
          }
          70% {
            transform: scale(1.02) translateY(-1px);
            background: #2c5aa0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }
          100% {
            transform: scale(1) translateY(0);
            background: #2c5aa0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          }
        }

        .cart-icon {
          font-size: 1.2rem;
          display: inline-block;
          transition: transform 0.2s ease;
        }

        /* Cart icon bounce animation when item is added */
        .cart-item-added .cart-icon {
          animation: cartIconBounce 0.8s ease-out;
        }

        @keyframes cartIconBounce {
          0%, 20%, 50%, 80%, 100% {
            transform: translateY(0) rotate(0deg);
          }
          10% {
            transform: translateY(-8px) rotate(-5deg);
          }
          30% {
            transform: translateY(-6px) rotate(3deg);
          }
          40% {
            transform: translateY(-4px) rotate(-2deg);
          }
          60% {
            transform: translateY(-2px) rotate(1deg);
          }
        }

        .cart-label {
          font-size: 0.9rem;
          font-weight: 500;
          white-space: nowrap;
        }

        /* Modal styles */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba(0, 0, 0, 0.5);
          display: ${this.state.isModalOpen ? 'flex' : 'none'};
          align-items: center;
          justify-content: center;
          z-index: 1000;
          animation: fadeIn 0.2s ease-out;
        }

        .modal-content {
          background: white;
          border-radius: 8px;
          width: 95%;
          max-width: 600px;
          max-height: 80vh;
          overflow: hidden;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
          animation: slideIn 0.2s ease-out;
          display: flex;
          flex-direction: column;
        }

        .modal-header {
          padding: 20px;
          border-bottom: 1px solid #e0e0e0;
          background: #f8f9fa;
          display: flex;
          justify-content: space-between;
          align-items: center;
          flex-shrink: 0;
        }

        .modal-title {
          font-weight: 600;
          font-size: 1.3rem;
          margin: 0;
          color: #333;
          display: flex;
          align-items: center;
          gap: 10px;
        }

        .close-button {
          background: none;
          border: none;
          font-size: 1.5rem;
          cursor: pointer;
          color: #666;
          padding: 0;
          width: 30px;
          height: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 50%;
          transition: background-color 0.2s;
        }

        .close-button:hover {
          background-color: #e9ecef;
        }

        .modal-body {
          padding: 20px;
          overflow-y: auto;
          flex: 1;
        }

        .cart-empty {
          text-align: center;
          color: #666;
          font-style: italic;
          padding: 40px 20px;
        }

        .cart-item {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          padding: 15px 0;
          border-bottom: 1px solid #f0f0f0;
        }

        .cart-item:last-child {
          border-bottom: none;
        }

        .item-info {
          flex: 1;
        }

        .item-name {
          font-weight: 600;
          color: #333;
          margin-bottom: 5px;
        }

        .item-quantity {
          color: #666;
          font-size: 0.9rem;
          margin-bottom: 3px;
        }

        .item-options {
          color: #888;
          font-size: 0.85rem;
          font-style: italic;
        }

        .item-actions {
          margin-left: 15px;
        }

        .btn-remove {
          background: #dc3545;
          color: white;
          border: none;
          padding: 6px 12px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 0.8rem;
          transition: background-color 0.2s;
        }

        .btn-remove:hover {
          background: #c82333;
        }

        @keyframes fadeIn {
          from { opacity: 0; }
          to { opacity: 1; }
        }

        @keyframes slideIn {
          from { 
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
          }
          to { 
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        @media (max-width: 768px) {
          :host {
            bottom: 15px;
            right: 15px;
          }

          .cart-floating-box {
            padding: 10px 14px;
            min-width: 140px;
          }

          .cart-label {
            font-size: 0.8rem;
          }

          .modal-content {
            width: 98%;
            max-height: 90vh;
          }

          .modal-header,
          .modal-body {
            padding: 15px;
          }

          .cart-item {
            flex-direction: column;
            align-items: flex-start;
            gap: 10px;
          }

          .item-actions {
            margin-left: 0;
            align-self: flex-end;
          }
        }
      </style>
    `
  }

  // Render component
  render() {
    const styles = this.getStyles()
    const totalItems = this.getTotalItems()

    // Cart items content
    let cartContent = ''
    if (this.state.cartItems.length === 0) {
      cartContent = '<div class="cart-empty">Your cart is empty</div>'
    } else {
      cartContent = this.state.cartItems
        .map(item => {
          const optionsText = this.formatSelectedOptions(item.selectedOptions)
          return `
          <div class="cart-item">
            <div class="item-info">
              <div class="item-name">${item.productName}</div>
              <div class="item-quantity">Quantity: ${item.quantity}</div>
              ${
                optionsText
                  ? `<div class="item-options">${optionsText}</div>`
                  : ''
              }
            </div>
            <div class="item-actions">
              <button class="btn-remove" onclick="this.getRootNode().host.removeItem('${
                item.id
              }')">
                Remove
              </button>
            </div>
          </div>
        `
        })
        .join('')
      cartContent = `
        <div class="cart-items">
          ${cartContent}
        </div>
        <div class="cart-total">
          Total: €${totalItems.toFixed(2)}
        </div>
      `
    }

    // Modal content
    const modalContent = this.state.isModalOpen
      ? `
      <div class="modal-overlay" onclick="this.getRootNode().host.handleBackgroundClick(event)">
        <div class="modal-content">
          <div class="modal-header">
            <h3 class="modal-title">
              🛒 Shopping Cart
            </h3>
            <button class="close-button" onclick="this.getRootNode().host.closeModal()">
              ×
            </button>
          </div>
          <div class="modal-body">
            ${cartContent}
          </div>
        </div>
      </div>
    `
      : ''

    this.shadowRoot.innerHTML = `
      ${styles}
      <div class="cart-floating-box" onclick="this.getRootNode().host.handleCartClick()">
        <span class="cart-icon">🛒</span>
        <span class="cart-label">${this.getCartLabel()}</span>
      </div>
      ${modalContent}
    `
  }
}

// Register the custom element
if (!customElements.get('carrello-web-component')) {
  customElements.define('carrello-web-component', CarrelloWebComponent)
}

export default CarrelloWebComponent
