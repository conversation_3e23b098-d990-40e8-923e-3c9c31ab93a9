<template>
  <ul class="pagination" :class="paginationClass">
    <li class="page-item first-page" :class="{ disabled: value === 1 }">
      <a class="page-link" aria-label="First" @click="firstPage">
        <i class="fa fa-backward" aria-hidden="true"></i>
      </a>
    </li>
    <li class="page-item prev-page" :class="{ disabled: value === 1 }">
      <a class="page-link" aria-label="Previous" @click="prevPage">
        <i class="fa fa-step-backward" aria-hidden="true"></i>
      </a>
    </li>
    <li
      class="page-item"
      :class="{ active: value === item }"
      v-for="item in range(minPage, maxPage)"
      :key="item"
    >
      <a class="page-link" @click="changePage(item)">{{ item }}</a>
    </li>
    <li class="page-pre next-page" :class="{ disabled: value === totalPages }">
      <a class="page-link" aria-label="Next" @click="nextPage">
        <i class="fa fa-step-forward" aria-hidden="true"></i>
      </a>
    </li>
    <li class="page-pre last-page" :class="{ disabled: value === totalPages }">
      <a class="page-link" aria-label="Next" @click="lastPage">
        <i class="fa fa-forward" aria-hidden="true"></i>
      </a>
    </li>
  </ul>
</template>
<script>
export default {
  name: 'PPagination',
  props: {
    type: {
      type: String,
      default: 'default'
    },
    pageCount: {
      type: Number,
      default: 0
    },
    perPage: {
      type: Number,
      default: 10
    },
    total: {
      type: Number,
      default: 0
    },
    value: {
      type: Number,
      default: 1
    },
    defaultPagesToDisplay: {
      type: Number,
      default: 5
    }
  },
  computed: {
    paginationClass() {
      return `pagination-${this.type}`
    },
    from() {
      return this.perPage * this.value - 1
    },
    totalPages() {
      if (this.pageCount > 0) return this.pageCount
      if (this.total > 0) {
        return Math.ceil(this.total / this.perPage)
      }
      return 1
    },
    pagesToDisplay() {
      if (this.totalPages > 0 && this.totalPages < this.defaultPagesToDisplay) {
        return this.totalPages
      }
      return this.defaultPagesToDisplay
    },
    minPage() {
      if (this.value >= this.pagesToDisplay) {
        const pagesToAdd = Math.floor(this.pagesToDisplay / 2)
        const newMaxPage = pagesToAdd + this.value
        if (newMaxPage > this.totalPages) {
          return this.totalPages - this.pagesToDisplay + 1
        }
        return this.value - pagesToAdd
      } else {
        return 1
      }
    },
    maxPage() {
      if (this.value >= this.pagesToDisplay) {
        const pagesToAdd = Math.floor(this.pagesToDisplay / 2)
        const newMaxPage = pagesToAdd + this.value
        if (newMaxPage < this.totalPages) {
          return newMaxPage
        } else {
          return this.totalPages
        }
      } else {
        return this.pagesToDisplay
      }
    }
  },
  data() {
    return {}
  },
  methods: {
    range(min, max) {
      let arr = []
      for (let i = min; i <= max; i++) {
        arr.push(i)
      }
      return arr
    },
    changePage(item) {
      this.$emit('input', item)
    },
    nextPage() {
      if (this.value < this.totalPages) {
        this.$emit('input', this.value + 1)
      }
    },
    prevPage() {
      if (this.value > 1) {
        this.$emit('input', this.value - 1)
      }
    },
    firstPage() {
      if (this.value > 1) {
        this.$emit('input', 1)
      }
    },
    lastPage() {
      if (this.value < this.totalPages) {
        this.$emit('input', this.totalPages)
      }
    }
  },
  watch: {
    perPage() {
      this.$emit('input', 1)
    },
    total() {
      this.$emit('input', 1)
    }
  }
}
</script>
