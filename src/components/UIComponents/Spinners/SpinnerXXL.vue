<template>
  <div>
    <span
      class="glyphicon glyphicon-repeat normal-right-spinner spinner-size-xxl"
    ></span>
    <h1>{{ title }}</h1>
  </div>
</template>

<script>
export default {
  name: 'SpinnerXxl',
  props: {
    title: {
      type: String,
      default: ''
    }
  }
}
</script>

<style>
h1 {
  display: inline;
  vertical-align: text-bottom;
  padding-left: 0.5em;
}

.glyphicon.normal-right-spinner {
  -webkit-animation: glyphicon-spin-r 2s infinite linear;
  animation: glyphicon-spin-r 2s infinite linear;
}

.spinner-size-xxl {
  font-size: 3em;
}

@-webkit-keyframes glyphicon-spin-r {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}

@keyframes glyphicon-spin-r {
  0% {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(359deg);
    transform: rotate(359deg);
  }
}
</style>
