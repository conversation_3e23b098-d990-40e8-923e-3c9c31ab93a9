<template>
  <div
    :is="tag"
    :class="[getSlideClass, { open: isOpen }]"
    @click="toggleDropDown"
    v-click-outside="closeDropDown"
  >
    <slot name="title">
      <a
        :class="['dropdown-toggle', getRotate]"
        data-toggle="dropdown"
        href="javascript:void(0)"
      >
        <i :class="icon"></i>
        <p class="notification" v-html="title">
          <b class="caret"></b>
        </p>
      </a>
    </slot>
    <ul class="dropdown-menu">
      <slot></slot>
    </ul>
  </div>
</template>
<script>
export default {
  name: 'DropDown',
  props: {
    title: String,
    icon: String,
    slide: {
      type: String,
      default: 'down'
    },
    rotate: {
      type: Boolean,
      default: true
    },
    tag: {
      type: String,
      default: 'div'
    }
  },
  computed: {
    getSlideClass() {
      return this.slide === 'down' ? 'dropdown' : 'dropup'
    },
    getRotate() {
      return this.rotate === true ? 'btn-rotate' : ''
    }
  },
  data() {
    return {
      isOpen: false
    }
  },
  methods: {
    toggleDropDown() {
      this.isOpen = !this.isOpen
    },
    closeDropDown() {
      this.isOpen = false
    }
  }
}
</script>
