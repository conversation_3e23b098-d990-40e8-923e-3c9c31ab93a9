<template>
  <div class="pagination">
    <div class="col-sm-6 pagination-info">
      <p class="category">
        Showing {{ from + 1 }} to {{ to }} of {{ total }} entries
      </p>
    </div>
    <div class="col-sm-6">
      <p-pagination
        class="pull-right"
        v-model="pagination.currentPage"
        :per-page="pagination.perPage"
        :total="pagination.total"
        :default-pages-to-display="pagination.defaultPagesToDisplay"
      ></p-pagination>
    </div>
  </div>
</template>

<script>
import PPagination from './Pagination.vue'

export default {
  name: 'TablePagination',
  components: {
    PPagination
  },
  computed: {
    to() {
      let highBound = this.from + this.pagination.perPage
      if (this.total < highBound) {
        highBound = this.total
      }
      return highBound
    },
    from() {
      return this.pagination.perPage * (this.pagination.currentPage - 1)
    },
    total() {
      // this.pagination.total = this.tableData.length
      return this.pagination.total
    }
  },
  data() {
    return {
      pagination: {
        perPage: 10,
        currentPage: 1,
        perPageOptions: [10, 25, 50],
        total: 0,
        defaultPagesToDisplay: 3
      }
    }
  }
}
</script>
