<template>
  <div
    :class="sidebarClasses"
    :data-background-color="backgroundColor"
    :data-active-color="activeColor"
  >
    <div class="logo">
      <a class="simple-text logo-mini" href="/home">
        <div class="logo-img">
          <img :src="logo" alt="" />
        </div>
      </a>
      <a class="simple-text logo-normal" href="/home" v-html="title"></a>
    </div>
    <div class="sidebar-wrapper" ref="sidebarScrollArea">
      <slot></slot>
      <ul :class="navClasses">
        <slot name="links">
          <sidebar-item
            v-for="(link, index) in sidebarLinks"
            :key="link.name + index"
            :link="link"
          >
            <sidebar-item
              v-for="(subLink, iindex) in link.children"
              :key="subLink.name + iindex"
              :link="subLink"
            ></sidebar-item>
          </sidebar-item>
        </slot>
      </ul>
    </div>
  </div>
</template>
<script>
import { mapGetters } from 'vuex'
export default {
  props: {
    title: {
      type: String,
      default: process.env.VUE_APP_MASTERCOM_ID
    },
    type: {
      type: String,
      default: 'sidebar',
      validator: value => {
        let acceptedValues = ['sidebar', 'navbar']
        return acceptedValues.indexOf(value) !== -1
      }
    },
    backgroundColor: {
      type: String,
      default: 'black',
      validator: value => {
        let acceptedValues = ['white', 'brown', 'black']
        return acceptedValues.indexOf(value) !== -1
      }
    },
    activeColor: {
      type: String,
      default: 'success',
      validator: value => {
        let acceptedValues = ['primary', 'info', 'success', 'warning', 'danger']
        return acceptedValues.indexOf(value) !== -1
      }
    },
    logo: {
      type: String,
      default: 'static/img/mastercom_logo.png'
    }
  },
  computed: {
    sidebarClasses() {
      if (this.type === 'sidebar') {
        return 'sidebar'
      } else {
        return 'collapse navbar-collapse off-canvas-sidebar'
      }
    },
    navClasses() {
      if (this.type === 'sidebar') {
        return 'nav'
      } else {
        return 'nav navbar-nav'
      }
    },
    sidebarLinks() {
      return this._sidebarLinks.map(item => {
        if (item.meta) {
          item.meta.badge = 0
          if (this._aggiornamenti[item.meta.linkName]) {
            item.meta.badge = this._aggiornamenti[item.meta.linkName]['numero']
          }
        }
        if ('children' in item) {
          item.children = item.children
            .filter(x => {
              if ('meta' in x) {
                return x.meta.visible
              }
              return true
            })
            .map(child => {
              if ('meta' in child) {
                if (child.meta.linkName in this._aggiornamenti) {
                  child.meta.badge = this._aggiornamenti[child.meta.linkName][
                    'numero'
                  ]
                }
              }
              return child
            })
        }
        return item
      })
    },
    ...mapGetters({
      _sidebarLinks: 'main/sidebarLinks',
      _aggiornamenti: 'aggiornamenti/aggiornamenti'
    })
  },
  methods: {
    async initScrollBarAsync() {
      await import('perfect-scrollbar/dist/css/perfect-scrollbar.css')
      const PerfectScroll = await import('perfect-scrollbar')
      PerfectScroll.initialize(this.$refs.sidebarScrollArea)
    }
  },
  mounted() {
    this.initScrollBarAsync()
  },
  beforeDestroy() {
    if (this.$sidebar.showSidebar) {
      this.$sidebar.showSidebar = false
    }
  }
}
</script>
<style>
@media (min-width: 992px) {
  .navbar-search-form-mobile,
  .nav-mobile-menu {
    display: none;
  }
}
</style>
