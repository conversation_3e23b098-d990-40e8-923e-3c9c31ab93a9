<template>
  <component
    :is="baseComponent"
    :to="link.path ? link.path : '/'"
    :class="{ active: isActive }"
    tag="li"
  >
    <a
      v-if="isMenu"
      data-toggle="collapse"
      href="javascript:void(0)"
      @click="collapseMenu"
    >
      <i :class="link.icon"></i>
      <p>
        {{ $t(link.name) }}
        <b class="caret"></b>
      </p>
    </a>
    <div v-if="$slots.default || this.isMenu">
      <el-collapse-transition>
        <ul class="nav" v-show="!collapsed">
          <slot></slot>
        </ul>
      </el-collapse-transition>
    </div>
    <slot
      name="title"
      v-if="children.length === 0 && !$slots.default && link.path"
    >
      <component
        :to="link.path"
        :is="elementType(link, false)"
        :class="{ active: link.active }"
        :target="link.target"
        :href="link.path"
      >
        <template v-if="addLink">
          <span class="sidebar-mini"><i :class="link.meta.icon"></i></span>
          <span class="sidebar-normal">{{ $t(link.name) }}</span>
          <span
            class="sidebar-badge btn btn-icon btn-fill btn-xs btn-danger"
            v-if="link.meta.badge"
          >
            {{ link.meta.badge }}
          </span>
        </template>
        <template v-else>
          <i :class="link.icon"></i>
          <p>{{ $t(link.name) }}</p>
          <span
            class="sidebar-badge badge-parent btn btn-icon btn-fill btn-sm btn-warning"
            v-if="showBadgeNumber(link)"
          >
            {{ link.meta.badge }}
          </span>
        </template>
      </component>
    </slot>
  </component>
</template>
<script>
import CollapseTransition from 'element-ui/lib/transitions/collapse-transition'

export default {
  components: {
    [CollapseTransition.name]: CollapseTransition
  },
  props: {
    menu: {
      type: Boolean,
      default: false
    },
    link: {
      type: Object,
      default: () => {
        return {
          name: '',
          path: '',
          children: []
        }
      }
    }
  },
  provide() {
    return {
      addLink: this.addChild,
      removeLink: this.removeChild
    }
  },
  inject: {
    addLink: { default: null },
    removeLink: { default: null }
  },
  data() {
    return {
      children: [],
      collapsed: true
    }
  },
  computed: {
    baseComponent() {
      return this.isMenu || this.link.isRoute ? 'li' : 'router-link'
    },
    isMenu() {
      return this.children.length > 0 || this.menu === true
    },
    isActive() {
      if (this.$route) {
        let matchingRoute = this.children.find(c =>
          this.$route.path.startsWith(c.link.path)
        )
        if (matchingRoute !== undefined) {
          return true
        }
      }
      return false
    }
  },
  methods: {
    showBadgeNumber(link) {
      return link.meta.badge
    },
    addChild(item) {
      const index = this.$slots.default.indexOf(item.$vnode)
      this.children.splice(index, 0, item)
    },
    removeChild(item) {
      const tabs = this.children
      const index = tabs.indexOf(item)
      tabs.splice(index, 1)
    },
    elementType(link, isParent = true) {
      if (link.isRoute === false) {
        return isParent ? 'li' : 'a'
      } else {
        return 'router-link'
      }
    },
    linkAbbreviation(name) {
      const matches = name.match(/\b(\w)/g)
      return matches.join('')
    },
    collapseMenu() {
      this.collapsed = !this.collapsed
    },
    collapseSubMenu(link) {
      link.collapsed = !link.collapsed
    }
  },
  mounted() {
    if (this.addLink) {
      this.addLink(this)
    }
    if (this.link.collapsed !== undefined) {
      this.collapsed = this.link.collapsed
    }
    if (this.isActive && this.isMenu) {
      this.collapsed = false
    }
  },
  destroyed() {
    if (this.$el && this.$el.parentNode) {
      this.$el.parentNode.removeChild(this.$el)
    }
    if (this.removeLink) {
      this.removeLink(this)
    }
  }
}
</script>
<style scoped lang="scss">
.sidebar-badge {
  position: absolute;
  left: 40px;
  top: -5px;
  width: 25px;
  padding: 0;
  text-align: center;

  &.badge-parent {
    top: 10px;
  }
}
a {
  .sidebar-normal,
  p {
    text-transform: uppercase;
  }
}
</style>
