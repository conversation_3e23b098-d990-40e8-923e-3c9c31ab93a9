<template>
  <div class="user">
    <div class="photo">
      <img :src="null" v-if="false" />
      <div class="btn btn-fill btn-success btn-sm" v-else>
        <span class="ti-user"></span>
      </div>
    </div>
    <div class="info">
      <a data-toggle="collapse" @click="toggleMenu" href="javascript:void(0)">
        <span>
          <span class="student-info">
            <span class="name">{{ studenteCorrente.nome }}</span>
            <br />
            <span class="info">
              A.S. {{ annoCorrenteDisplay }}
              <br />
              {{ studenteCorrente.classe }}
              {{ studenteCorrente.indirizzo }}
            </span>
          </span>
          <b class="caret"></b>
        </span>
      </a>
      <div class="clearfix"></div>
      <div>
        <el-collapse-transition>
          <ul class="nav" v-show="!isClosed">
            <li class="anni-storici" v-if="anniStorici.length">
              <h6 class="text-warning">{{ $t('nav.student.past-years') }}</h6>
              <ul>
                <li v-for="anno in anniStorici" :key="anno.id">
                  <a
                    href="#"
                    @click.stop.prevent="doCambiaAnnoScolastico(anno.id)"
                  >
                    {{ anno.descrizione }}
                  </a>
                </li>
              </ul>
            </li>
            <li class="altri-studenti" v-if="altriStudenti.length">
              <h6 class="text-warning">
                {{ $t('nav.student.select-student') }}
              </h6>
              <ul>
                <li v-for="studente in altriStudenti" :key="studente.id">
                  <a href="#" @click.stop.prevent="doCambiaStudente(studente)">
                    {{ studente.nome }}
                  </a>
                </li>
              </ul>
            </li>
          </ul>
        </el-collapse-transition>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common'
import { Popover } from 'element-ui'
import CollapseTransition from 'element-ui/lib/transitions/collapse-transition'
import { mapState, mapGetters, mapActions } from 'vuex'

Vue.use(Popover)

export default {
  mixins: [Common],
  components: {
    [CollapseTransition.name]: CollapseTransition
  },
  data() {
    return {
      isClosed: true,
      showDialogAnni: false
    }
  },
  computed: {
    altriStudenti() {
      return this.studenti.filter(x => x.id !== this.studenteCorrente.id)
    },
    ...mapState({
      studenteCorrente: state => state.main.studenteCorrente,
      annoStudente: state => state.main.annoCorrente,
      studenti: state => state.auth.user.studenti
    }),
    ...mapGetters({
      authObject: 'main/authObject',
      anniStorici: 'main/anniStorici',
      annoCorrenteDisplay: 'main/annoCorrenteDisplay'
    })
  },
  methods: {
    toggleMenu() {
      this.isClosed = !this.isClosed
    },
    logOutBtn() {
      this.logout().then(() => {
        this.$router.push('/')
      })
    },
    doCambiaAnnoScolastico(anno) {
      this.showLoader()
      this.$router.push('home')
      this.displayOrarioPanel(false)
      this.setAnnoCorrente(anno)

      this.initStudente(this.studenteCorrente)
        .then(() => this.initScuolaStudente(this.authObject))
        .then(() => this.loadMaterie(this.authObject))
        .finally(() => {
          this.doLoadAggiornamenti()
          this.isClosed = true
          this.hideLoader()
          this.displayOrarioPanel(true)
        })
    },
    doCambiaStudente(studente) {
      this.showLoader()
      this.$router.push('home')
      this.displayOrarioPanel(false)
      this.initStudente(studente)
        .then(() => this.initScuolaStudente(this.authObject))
        .then(() => this.loadMaterie(this.authObject))
        .then(() => this.loadOrario(this.authObject))
        .finally(() => {
          this.doLoadAggiornamenti()
          this.isClosed = true
          this.hideLoader()
          this.displayOrarioPanel(true)
          this.emptyCart()
        })
    },
    ...mapActions({
      logout: 'auth/logout',
      initStudente: 'main/initStudente',
      initScuolaStudente: 'main/initScuolaStudente',
      setAnnoCorrente: 'main/setAnnoCorrente',
      loadMaterie: 'materie/load',
      displayOrarioPanel: 'main/displayOrarioPanel',
      emptyCart: 'pagamenti/emptyCart'
    })
  }
}
</script>
<style lang="scss" scoped>
.collapsed {
  transition: opacity 1s;
}
.user {
  .photo {
    .btn {
      padding: 4px 7px;
    }
  }
  .student-info {
    float: left;
    width: 150px;
    white-space: initial;
    .name {
      font-weight: 700;
    }
    .info {
      font-size: 0.8em;
    }
    @media screen and (max-width: 768px) {
      width: 120px;
    }
  }
}

.sidebar {
  .user {
    .info {
      li {
        span {
          padding-left: 25px;
        }
      }
      .anni-storici,
      .altri-studenti {
        h6 {
          padding-left: 25px;
          font-size: 12px;
        }
        ul {
          margin-left: 0;
          position: relative;
          padding-left: 45px;
          li {
            list-style-type: disc;
            padding-bottom: 5px;
            a {
              padding: 0;
              font-size: 0.9em;
            }
          }
        }
      }
    }
  }
}
</style>
