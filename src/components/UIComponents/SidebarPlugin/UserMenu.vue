<template>
  <div class="user">
    <div class="info">
      <h5 class="name">
        <el-avatar>{{ iniziali }}</el-avatar>
        <span data-toggle="collapse">{{ userName }}</span>
      </h5>
      <div class="clearfix"></div>
      <div class="user-menu" data-toggle="collapse">
        <ul class="nav">
          <li>
            <a href="#" @click.stop="doShowChangePasswordWindow()">
              <i class="ti-shield"></i>
              <span>{{ $t('nav.user.change-password') }}</span>
            </a>
          </li>
          <li v-if="portaleIscrizioniEnabled">
            <a :href="linkPortale" target="_blank">
              <i class="ti-package"></i>
              <span>{{ $t('nav.user.portale-iscrizioni') }}</span>
            </a>
          </li>
          <li>
            <a href="#" @click.stop="doLogout">
              <i class="ti-shift-right"></i>
              <span>{{ $t('nav.user.logout') }}</span>
            </a>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common'
import { Popover, Avatar } from 'element-ui'
import CollapseTransition from 'element-ui/lib/transitions/collapse-transition'
import { mapState, mapGetters, mapActions } from 'vuex'

Vue.use(Popover)
Vue.use(Avatar)
export default {
  mixins: [Common],
  components: {
    [CollapseTransition.name]: CollapseTransition
  },
  data() {
    return {
      isClosed: true,
      showDialogAnni: false
    }
  },
  computed: {
    altriStudenti() {
      return this.studenti.filter(x => x.id !== this.studenteCorrente.id)
    },
    iniziali() {
      return this.nome_utente[0] + this.cognome_utente[0]
    },
    portaleIscrizioniEnabled() {
      return this.studenteCorrente.servizi['portale_iscrizioni']
    },
    linkPortale() {
      return this.urlScuola + '/preiscrizioni/index.html?a=' + this.jwtToken
    },
    ...mapState({
      nome_utente: state => state.auth.user.nome,
      cognome_utente: state => state.auth.user.cognome
    }),
    ...mapGetters({
      userName: 'auth/userName'
    })
  },
  methods: {
    toggleMenu() {
      this.isClosed = !this.isClosed
    },
    logOutBtn() {
      this.logout().then(() => {
        this.$router.push('/')
      })
    },
    ...mapActions({
      logout: 'auth/logout',
      initStudente: 'main/initStudente',
      initScuolaStudente: 'main/initScuolaStudente',
      setAnnoCorrente: 'main/setAnnoCorrente',
      loadMaterie: 'materie/load',
      displayOrarioPanel: 'main/displayOrarioPanel'
    })
  }
}
</script>
<style lang="scss" scoped>
.collapsed {
  transition: opacity 1s;
}
.user {
  .info {
    h5 {
      float: left;
      padding-left: 15px;
      justify-content: center;
      display: flex;
      align-items: center;
      .el-avatar {
        max-width: 40px;
        min-width: 40px;
        margin-right: 10px;
        float: left;
        background-color: #ffa534;
      }
      color: #f8f8f8;
      font-weight: 800;
    }
  }
}

.sidebar {
  .user {
    .info {
      .user-menu {
        ul {
          margin-left: 0;
          position: relative;
          padding-left: 0;
          li {
            list-style-type: none;
            padding-bottom: 5px;
            a {
              padding: 0;
              font-size: 0.9em;
              text-transform: uppercase;
              color: #fff;
              margin: 10px 0px 0px;
              padding-left: 25px;
              padding-right: 25px;
              white-space: nowrap;
              justify-content: left;
              display: flex;
              align-items: center;
              i {
                margin-right: 15px;
                font-size: 17px;
                line-height: 20px;
                width: 26px;
              }
            }
          }
        }
      }
    }
  }
}

.sidebar-mini {
  .user {
    .info {
      h5 {
        [data-toggle='collapse'] {
          @media (min-width: 992px) {
            transform: translate3d(-25px, 0, 0);
            opacity: 0;
          }
        }
      }
    }
    [data-toggle='collapse'] {
      .nav {
        span {
          @media (min-width: 992px) {
            transform: translate3d(-25px, 0, 0);
            opacity: 0;
          }
        }
      }
    }
  }
}
.sidebar-mini {
  .sidebar:hover {
    .user {
      .info {
        h5 {
          [data-toggle='collapse'] {
            @media (min-width: 992px) {
              transform: translate3d(0, 0, 0);
              opacity: 1;
            }
          }
        }
      }
    }
    [data-toggle='collapse'] {
      .nav {
        span {
          @media (min-width: 992px) {
            transform: translate3d(0, 0, 0);
            opacity: 1;
          }
        }
      }
    }
  }
}
</style>
