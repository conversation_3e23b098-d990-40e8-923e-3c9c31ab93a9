<template>
  <div class="mastercom" id="container-note-disciplinari">
    <div v-if="loaded && active">
      <filters
        :tags="tags"
        :filtra-tags="true"
        :filtra-materie="false"
        :filtra-date="false"
        v-if="loaded"
      ></filters>
      <div class="local-filters row">
        <div class="col-xs-11 col-lg-4">
          <el-switch v-model="_showSoloNovita"></el-switch>
          {{ $t('main.filter_solo_novita') }}
        </div>
      </div>
      <div v-if="note.length">
        <item-list
          :oggetti="noteDisplay"
          :titolo="$t('note.title')"
          class="col-xs-12"
          :tag="true"
          :on-confirm-action="
            studenteCorrente.presa_visione_note ? doPresaVisione : null
          "
          :confirm-action-text="
            studenteCorrente.presa_visione_note
              ? $t('note.presa_visione')
              : null
          "
        ></item-list>
      </div>
      <div v-else-if="loaded">
        <div class="card">
          <div class="card-content">
            {{ $t('main.no_content') }}
            <span v-if="descFiltroMateria">{{ descFiltroMateria }}</span>
            <span v-if="descFiltroPeriodo">{{ descFiltroPeriodo }}</span>
          </div>
        </div>
      </div>
    </div>
    <div v-else>{{ $t('main.loading') }}</div>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common.js'
import Tags from '@/components/Mastercom/Mixins/Tags.js'
import ItemList from '@/components/Mastercom/UIComponents/ItemList.vue'
import { mapState, mapGetters, mapActions } from 'vuex'
import Filters from '@/components/Mastercom/UIComponents/Filters.vue'
import { Switch, MessageBox } from 'element-ui'
export default {
  name: 'NoteDisciplinari',
  mixins: [Common, Tags],
  components: {
    ItemList,
    Filters,
    ElSwitch: Switch
  },
  data() {
    return {
      active: false,
      loaded: false
    }
  },
  computed: {
    noteDisplay() {
      let data = this.note

      if (this.showSoloNovita) {
        data = data.filter(a => {
          return a.novita
        })
      }
      if (this.selectedTag) {
        data = data.filter(a => {
          return a.tag.codice == this.selectedTag
        })
      }
      data = data.map(x => {
        x.confirm_text = `${this.$i18n.t('note.data_presa_visione', [
          x.confirm_date,
          x.confirm_time
        ])}`
        return x
      })
      return data
    },
    _showSoloNovita: {
      get() {
        return this.showSoloNovita
      },
      set(val) {
        this.setSoloNovita(val)
      }
    },
    ...mapState({
      showSoloNovita: state => state.noteDisciplinari.solo_novita
    }),
    ...mapGetters({
      note: 'noteDisciplinari/all',
      tags: 'noteDisciplinari/tags',
      selectedTag: 'tag/selectedTag'
    })
  },
  methods: {
    doPresaVisione(item) {
      MessageBox.confirm(this.$i18n.t('note.msg_conferma'), {
        confirmButtonText: this.$i18n.t('note.btn_conferma_richiesta'),
        cancelButtonText: this.$i18n.t('note.btn_annulla_richiesta'),
        closeOnClickModal: false,
        type: 'warning',
        customClass: 'mastercom-msg-box',
        beforeClose: (action, instance, done) => {
          if (action == 'confirm') {
            // console.log(item)
            this.setPresaVisione(item.id)
            instance.confirmButtonLoading = true
            this.inviaPresaVisione(this.authObject)
              .then(() => {
                this.loadNoteDisciplinari(this.authObject)
              })
              .then(() => done())
              .catch(error => {
                this.hideLoader()
                this.setError(error)
              })
              .finally(() => {
                instance.confirmButtonLoading = false
              })
          } else {
            done()
          }
        }
      })
    },
    doLoadNoteDiscipinari(aggiornamenti = false) {
      this.showLoader()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          this.active = this.isSectionEnabled('note')
        })
        .catch(error => {
          this.hideLoader()
          this.setError(error)
        })
      this.loadNoteDisciplinari(this.authObject)
        .then(enabled => {
          this.loaded = enabled
        })
        .catch(error => {
          this.setError(error)
        })
        .finally(() => {
          this.hideLoader()
        })
      if (aggiornamenti) {
        this.doLoadAggiornamenti()
      }
    },
    ...mapActions({
      loadNoteDisciplinari: 'noteDisciplinari/loadNoteDisciplinari',
      inviaPresaVisione: 'noteDisciplinari/inviaPresaVisione',
      setPresaVisione: 'noteDisciplinari/setPresaVisione',
      setSoloNovita: 'noteDisciplinari/setSoloNovita',
      reset: 'noteDisciplinari/reset'
    })
  },
  mounted() {
    this.doLoadNoteDiscipinari(true)
  },
  unmounted() {
    this.reset()
  }
}
</script>

<style scoped lang="scss">
.local-filters {
  padding: 10px;
}
</style>
