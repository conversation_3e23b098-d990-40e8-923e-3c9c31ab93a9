<template>
  <div class="mastercom" id="container-argomenti">
    <filters v-if="loaded && active"></filters>
    <div v-if="loaded && active" class="col-xs-12">
      <time-line v-if="argomentiDisplay.length">
        <time-line-item
          v-for="(item, index) in argomentiDisplay"
          class="timeline-inverted"
          :key="index"
          :month-header="
            index > 0
              ? displayMonthHeader(item, argomentiDisplay[index - 1])
              : $dayjs(item.data).format('MMMM')
          "
          :badge-text="
            index > 0
              ? displayDataItem(item, argomentiDisplay[index - 1])
              : $dayjs(item.data).format('ddd DD')
          "
          :data="$dayjs(item.data).format('DDMMYYYY')"
        >
          <div slot="news" v-if="item.novita">
            <div class="btn btn-danger btn-fill btn-xs" slot="novita">
              <span class="ti-shine"></span>
              {{ $t('main.novita') }}
            </div>
          </div>
          <div slot="header" class="full-relative-width">
            <materia
              :titolo="item.materia"
              :indice-colore="item.indice_colore"
            ></materia>
            <insegnante :nome="item.insegnante"></insegnante>
          </div>

          <p slot="body" class="text-primary" v-html="item.body"></p>

          <h6 slot="footer">
            <i class="ti-time"></i>
            {{ item.data | simpledate }}
          </h6>
        </time-line-item>
      </time-line>
      <div v-else>
        <div class="card">
          <div class="card-content">
            {{ $t('main.no_content') }}
            <span v-if="descFiltroMateria">{{ descFiltroMateria }}</span>
            <span v-if="descFiltroPeriodo">{{ descFiltroPeriodo }}</span>
          </div>
        </div>
      </div>
    </div>
    <div v-else>Loading</div>
  </div>
</template>

<script>
import TimeLine from '@/components/Mastercom/UIComponents/TimeLine.vue'
import TimeLineItem from '@/components/Mastercom/UIComponents/TimeLineItem3.vue'
import Materia from '@/components/Mastercom/UIComponents/Materia.vue'
import Filters from '@/components/Mastercom/UIComponents/Filters.vue'
import Common from '@/components/Mastercom/Mixins/Common.js'
import Insegnante from '@/components/Mastercom/UIComponents/Insegnante.vue'
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'Argomenti',
  mixins: [Common],
  components: {
    Insegnante,
    TimeLine,
    TimeLineItem,
    Materia,
    Filters
  },
  data() {
    return {
      active: false,
      loaded: false,
      oldBadgeText: '',
      today: this.$dayjs()
    }
  },
  computed: {
    argomentiDisplay() {
      let data = this.argomenti

      if (this.filtriMateria.length) {
        data = data.filter(a => {
          return this.filtriMateria.includes(a.id_materia)
        })
      }

      if (this.filtriData.length) {
        data = data.filter(a => {
          return (
            this.$dayjs(a.data) >= this.filtriData[0] &&
            this.$dayjs(a.data) <= this.filtriData[1]
          )
        })
      }
      return data
    },
    firstAvailableDate() {
      let items = this.agenda.filter(a => a.data >= this.today.toISOString())
      let date = items.length > 0 ? items[0].date : this.agenda.last().data
      return this.$dayjs(date)
    },
    firstAvailableArgomentoItem() {
      return `date_${this.firstAvailableDate.format('YYYY-MM-DD')}`
    },
    ...mapGetters({
      argomenti: 'argomenti/all'
    })
  },
  methods: {
    doLoadArgomenti(aggiornamenti = false) {
      this.setArgomentiDataInizio(null)
      this.setArgomentiDataFine(null)
      this.loaded = false
      this.showLoader()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          this.active = this.isSectionEnabled('argomenti')
        })
        .catch(error => {
          this.hideLoader()
          this.doSetError(error)
        })
      this.loadArgomenti(this.authObject)
        .then(enabled => {
          this.loaded = enabled
          // let element = document.getElementsByClassName(this.firstAvailableCompitoItem)[0]
        })
        .catch(error => {
          this.setError(error)
        })
        .finally(() => {
          this.hideLoader()
        })
      if (aggiornamenti === true) {
        this.doLoadAggiornamenti()
      }
    },
    badgeText() {
      return ''
    },
    ...mapActions({
      // loadArgomenti: 'argomenti/loadArgomenti',
      setArgomentiDataInizio: 'argomenti/setDataInizio',
      setArgomentiDataFine: 'argomenti/setDataFine',
      loadArgomenti: 'argomenti/loadArgomenti'
    })
  },
  mounted() {
    this.doClearFiltri()
    this.doLoadArgomenti(true)
  }
}
</script>

<style lang="scss" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: all 1s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
</style>
