<template>
  <div class="mastercom" id="container-pagamenti">
    <documenti-panel></documenti-panel>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common'
import DocumentiPanel from '@/components/Mastercom/UIComponents/Panels/DocumentiPanel.vue'

export default {
  name: 'Documenti',
  components: {
    DocumentiPanel
  },
  mixins: [Common]
}
</script>

<style lang="scss" scoped>
#container-pagamenti {
  .text-primary {
    float: left;
    width: 100%;
    margin-bottom: 1em;
    .importo {
      display: inline-block;
      font-size: 1.4em;
      font-weight: 700;
      padding-right: 2em;
      line-height: 1.4em;
    }
    .descrizione {
      display: inline-block;
      line-height: 1.4em;
    }
  }
}
</style>
