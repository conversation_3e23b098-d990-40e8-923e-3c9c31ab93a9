<template>
  <div class="mastercom" id="container-annotazioni">
    <div v-if="loaded && active">
      <filters></filters>
      <div>
        <item-list
          :oggetti="itemDisplay"
          :titolo="$t('nav.func.annotazioni')"
          class="col-xs-12 col-sm-11"
        ></item-list>
      </div>
    </div>
    <div v-else>
      {{ $t('main.loading') }}
    </div>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common.js'
import ItemList from '@/components/Mastercom/UIComponents/ItemList.vue'
import Filters from '@/components/Mastercom/UIComponents/Filters.vue'

import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'Annotazioni',
  mixins: [Common],
  components: {
    ItemList,
    Filters
  },
  data() {
    return {
      active: false,
      loaded: false
    }
  },
  computed: {
    itemDisplay() {
      let data = this.annotazioni
      if (this.filtriMateria.length) {
        data = data.filter(a => {
          return this.filtriMateria.includes(a.id_materia)
        })
      }

      if (this.filtriData.length) {
        data = data.filter(a => {
          return (
            this.$dayjs(a.data) >= this.filtriData[0] &&
            this.$dayjs(a.data) <= this.filtriData[1]
          )
        })
      }
      return data
    },
    ...mapGetters({
      annotazioni: 'annotazioni/all'
    })
  },
  methods: {
    doLoadAnnotazioni(aggiornamenti = false) {
      this.reset()
      this.showLoader()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          this.active = this.isSectionEnabled('annotazioni')
        })
        .catch(error => {
          this.hideLoader()
          this.setError(error)
        })
      this.loadAnnotazioni(this.authObject)
        .then(enabled => {
          this.loaded = enabled
        })
        .catch(error => {
          this.setError(error)
        })
        .finally(() => {
          this.hideLoader()
        })
      if (aggiornamenti === true) {
        this.doLoadAggiornamenti()
      }
    },
    ...mapActions({
      loadAnnotazioni: 'annotazioni/loadAnnotazioni',
      reset: 'annotazioni/reset'
    })
  },
  mounted() {
    this.doLoadAnnotazioni(true)
  }
}
</script>

<style></style>
