<template>
  <div class="mastercom" id="container-elezioni">
    <div v-if="loaded" class="col-xs-12">
      <a
        v-if="showButton"
        class="btn btn-info btn-fill btn-lg btn-magnify static-right"
        :href="showButton"
        target="_blank"
      >
        <i class="ti-pencil-alt"></i>
        VOTA ADESSO
      </a>
      <time-line v-if="elezioniDisplay.length">
        <time-line-item
          v-for="(item, index) in elezioniDisplay"
          class="timeline-inverted"
          :key="index"
          :month-header="
            index > 0
              ? displayMonthHeader(item, elezioniDisplay[index - 1])
              : $dayjs(item.data_inizio).format('MMMM')
          "
          :badge-text="
            index > 0
              ? displayDataItem(item, elezioniDisplay[index - 1])
              : $dayjs(item.data_inizio).format('ddd DD/MM')
          "
          :data="$dayjs(item.data_inizio).format('DDMMYYYY')"
        >
          <div slot="news" v-if="item.novita">
            <div class="btn btn-danger btn-fill btn-xs" slot="novita">
              <span class="ti-shine"></span>
              Novità
            </div>
          </div>
          <div slot="header" class="full-relative-width">
            <h5>{{ item.nome }}</h5>
          </div>

          <div slot="body" class="text-primary extra-margin">
            &Egrave; possibile votare da
            <strong>
              {{ item.data_inizio | longdate }}, ore
              {{ item.data_inizio | simpletime }}
            </strong>
            fino a
            <strong>
              {{ item.data_fine | longdate }}, ore
              {{ item.data_fine | simpletime }}
            </strong>
          </div>
          <h6 slot="footer">
            <div class="text-info" v-if="item.status == 'active'">
              <i class="ti-pencil-alt"></i>
              Puoi ancora inviare il tuo voto
            </div>

            <div class="text-success" v-if="item.status == 'cast'">
              <i class="ti-check"></i>
              Hai gi&agrave; partecipato a questa votazione
            </div>
          </h6>
        </time-line-item>
      </time-line>
      <div v-else>
        <div class="card">
          <div class="card-content">
            {{ $t('main.no_content') }}
          </div>
        </div>
      </div>
    </div>
    <div v-else>Loading</div>
  </div>
</template>

<script>
import TimeLine from '@/components/Mastercom/UIComponents/TimeLine.vue'
import TimeLineItem from '@/components/Mastercom/UIComponents/TimeLineItem3.vue'
// import Materia from '@/components/Mastercom/UIComponents/Materia.vue'
// import Filters from '@/components/Mastercom/UIComponents/Filters.vue'
import Common from '@/components/Mastercom/Mixins/Common.js'
// import Insegnante from '@/components/Mastercom/UIComponents/Insegnante.vue'
import { mapGetters, mapActions } from 'vuex'
// import { mapCacheActions } from 'vuex-cache'

export default {
  name: 'Elezioni',
  mixins: [Common],
  components: {
    TimeLine,
    TimeLineItem
  },
  data() {
    return {
      loaded: false,
      oldBadgeText: '',
      today: this.$dayjs()
    }
  },
  computed: {
    elezioniDisplay() {
      let data = this.elezioni
      return data
    },
    elezioniAperte() {
      return this.elezioniDisplay.filter(x => {
        return x.status == 'active'
      })
    },
    showButton() {
      if (this.elezioniAperte.length > 0) {
        return this.elezioniAperte[0]['link']
      }
      return false
    },
    ...mapGetters({
      elezioni: 'elezioni/all'
    })
  },
  methods: {
    doLoadElezioni(aggiornamenti = false) {
      this.loaded = false
      this.showLoader()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          return this.isSectionEnabled('elezioni')
        })
        .then(enabled => {
          if (enabled === true) {
            this.loadElezioni(this.authObject)
          }
          return enabled
        })
        .then(enabled => {
          this.loaded = enabled
          if (aggiornamenti === true) {
            this.doLoadAggiornamenti()
          }
        })
        .catch(error => {
          this.setError(error)
        })
        .finally(() => {
          this.hideLoader()
        })
    },
    badgeText() {
      return ''
    },
    ...mapActions({
      loadElezioni: 'elezioni/loadElezioni'
    })
  },
  mounted() {
    this.doClearFiltri()
    this.doLoadElezioni(true)
  }
}
</script>

<style lang="scss" scoped>
.fade-enter-active,
.fade-leave-active {
  transition: all 1s;
}
.fade-enter,
.fade-leave-to {
  opacity: 0;
}
.static-right {
  position: fixed;
  left: calc(-50vw + 50%);
  right: calc(-50vw + 50%);
  margin-left: auto;
  margin-right: auto;
  z-index: 2;
  width: 20%;
  min-width: 200px;
}

.extra-margin {
  margin-bottom: 2em;
}
</style>
