<template>
  <div class="mastercom" id="container-pagamenti">
    <pagamenti-panel v-if="showPanels"></pagamenti-panel>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common'
// import TimeLine from '@/components/Mastercom/UIComponents/TimeLine.vue'
// import TimeLineItem from '@/components/Mastercom/UIComponents/TimeLineItem3.vue'
import Pagamenti from '@/components/Mastercom/Mixins/Pagamenti'
// import PagamentiPanel from '@/components/Mastercom/UIComponents/Panels/PagamentiPanel.vue'
import PagamentiPanel from '@/components/Mastercom/UIComponents/Panels/PagamentiPanelNew.vue'
// import AllegatiList from '@/components/Mastercom/UIComponents/AllegatiList.vue'
export default {
  name: 'Pagamenti',
  components: {
    // TimeLine,
    // TimeLineItem,
    // AllegatiList,
    PagamentiPanel
  },
  mixins: [Common, Pagamenti],
  mounted() {
    this.doCheckAreaActive()
  },
  unmounted() {
    this.reset()
  }
}
</script>

<style lang="scss" scoped>
#container-pagamenti {
  .text-primary {
    float: left;
    width: 100%;
    margin-bottom: 1em;
    .importo {
      display: inline-block;
      font-size: 1.4em;
      font-weight: 700;
      padding-right: 2em;
      line-height: 1.4em;
    }
    .descrizione {
      display: inline-block;
      line-height: 1.4em;
    }
  }
}
</style>
