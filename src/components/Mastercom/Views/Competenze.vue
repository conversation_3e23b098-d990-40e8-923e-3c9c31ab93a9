<template>
  <div class="mastercom" id="container-voti">
    <filters v-if="loaded"></filters>
    <div v-if="loaded">
      <voti-list
        :voti="competenzeDisplay"
        titolo="Competenze/Obiettivi"
        sottotitolo=""
        :has-filters="false"
        v-if="competenzeDisplay.length"
        class="col-xs-12 col-sm-12 col-md-12"
      ></voti-list>
      <div class="col-xs-12 col-sm-12 col-md-12" v-else>
        <div class="card">
          <div class="card-content">
            {{ $t('main.no_content') }}
            <span v-if="descFiltroMateria">{{ descFiltroMateria }}</span>
            <span v-if="descFiltroPeriodo">{{ descFiltroPeriodo }}</span>
          </div>
        </div>
      </div>
    </div>
    <div v-else>loading</div>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common.js'
import Voti from '@/components/Mastercom/Mixins/Voti.js'
import VotiList from '@/components/Mastercom/UIComponents/VotiList.vue'
import Filters from '@/components/Mastercom/UIComponents/Filters.vue'

// import Colors from '@/assets/sass/mastercom_colors.scss'

export default {
  name: 'Competenze',
  mixins: [Common, Voti],
  components: {
    VotiList,
    Filters
  },
  computed: {
    competenzeDisplay() {
      let data = this.competenze
      if (this.filtriMateria.length) {
        data = data.filter(a => {
          console.log(a.id_materia.toString())
          return this.filtriMateria
            .map(x => x.toString())
            .includes(a.id_materia.toString())
        })
      }

      if (this.filtriData.length) {
        data = data.filter(a => {
          return (
            this.$dayjs(a.data) >= this.$dayjs(this.filtriData[0]) &&
            this.$dayjs(a.data) <= this.$dayjs(this.filtriData[1])
          )
        })
      }
      return data
    }
  },
  methods: {
    doLoadCompetenze(aggiornamenti = false) {
      this.showLoader()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          return this.isSectionEnabled('competenze')
        })
        .then(enabled => {
          if (enabled === true) {
            this.loadVoti(this.authObject)
          }
          return enabled
        })
        .then(enabled => {
          this.loaded = enabled
          if (aggiornamenti === true) {
            this.doLoadAggiornamenti()
          }
        })
        .catch(error => {
          this.doSetError(error)
        })
        .finally(() => {
          this.hideLoader()
        })
    }
  },
  mounted() {
    this.doLoadCompetenze(true)
  }
}
</script>

<style lang="scss" scoped></style>
