<template>
  <div class="mastercom" id="container-pagelle">
    <item-list
      :oggetti="pagelle"
      titolo="Pagelle"
      class="col-xs-12 col-sm-11"
      :has-date="false"
      :open-in-msg-box="true"
      :download-item="true"
      v-if="pagelle.length"
    ></item-list>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common'
import ItemList from '@/components/Mastercom/UIComponents/ItemList.vue'
import { mapActions, mapGetters } from 'vuex'
export default {
  name: 'Pagelle',
  mixins: [Common],
  components: {
    ItemList
  },
  computed: {
    pagelle() {
      return this._pagelle.map(x => {
        let new_allegati = []
        for (let a of x.allegati) {
          a.allegato = a.allegato.replace('api/v3', 'v3')
          new_allegati.push(a)
        }
        x.allegati = new_allegati
        return x
      })
    },
    ...mapGetters({
      _pagelle: 'pagelle/pagelle'
    })
  },
  methods: {
    doLoadPagelle(aggiornamenti = false) {
      this.reset()
      this.showLoader()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          return this.isSectionEnabled('pagelle')
        })
        .then(enabled => {
          if (enabled === true) {
            return this.loadPagelle(this.authObject)
          }
          return enabled
        })
        .then(enabled => {
          if (enabled) {
            this.hideLoader()
          }
        })
        .then(() => {
          if (aggiornamenti === true) {
            this.doLoadAggiornamenti()
          }
        })
        .catch(() => {
          this.hideLoader()
        })
    },
    doDownload(item) {
      this.setUrlAllegato(item.allegati)
    },
    ...mapActions({
      loadPagelle: 'pagelle/loadPagelle',
      downloadPagella: 'pagelle/loadAllegato',
      setUrlAllegato: 'pagelle/setUrlAllegato',
      reset: 'pagelle/reset'
    })
  },
  mounted() {
    this.doLoadPagelle(true)
  }
}
</script>

<style lang="scss" scoped></style>
