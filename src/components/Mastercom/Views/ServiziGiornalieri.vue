<template>
  <div>
    <credito-servizi></credito-servizi>
    <el-calendar v-model="today" v-if="loaded">
      <template slot="dateCell" slot-scope="{ data }">
        <div
          class="full-cell"
          :class="giornoFestivo(data.day) ? 'festivo' : ''"
          @click="openDialog(data.day)"
        >
          <h5 :class="data.isSelected ? 'is-selected' : ''">
            {{ data.day.split('-')[2] }}
          </h5>
          <div v-if="hasPrenotazioni(data.day)">
            <div class="hidden-xs">
              <span
                class="label label-success"
                v-for="(item, index) in adesioniGiornaliere(data.day)"
                :key="`adesioni-${index}`"
              >
                {{ item.nome }}
              </span>
            </div>
            <div class="visible-xs">
              <span class="label label-success">
                {{ adesioniGiornaliere(data.day).length }}
              </span>
            </div>
          </div>
        </div>
      </template>
    </el-calendar>
    <div v-else>{{ $t('main.loading') }}</div>
    <el-dialog
      :append-to-body="true"
      :visible.sync="prenotazioniPanelVisible"
      top="5vh"
      :width="_dialogWidth()"
    >
      <div class="row">
        <div class="col-xs-12">
          <h6 class="nowrap">
            {{ $t('servizi_giornalieri.text_conferma_adesione') }}
          </h6>
        </div>
        <div
          v-loading="marketplaceLoading"
          class="col-xs-12 marketplace-loader"
        >
          <div
            v-for="(item, index) in marketplace"
            :key="index"
            class="marketplace"
            @click="doAdesioneServizio(data_adesione, item)"
          >
            <badge
              :background-color="item.adesione == 'SI' ? '#7AC29A' : '#FFF'"
              :mini="true"
              border-width="0"
              :outline="backgroundColor(item.adesione)"
            >
              <i class="ti-check" v-if="item.adesione == 'SI'"></i>
              <i class="ti-plus" v-else></i>
            </badge>
            {{ item.descrizione }}
            <span
              v-if="item.adesione == 'SI' && item.ora_limite_cancellazione"
              class="spec"
            >
              {{
                $t('servizi_giornalieri.text_cancellazione', [
                  item.ora_limite_cancellazione,
                  dataAdesioneLong
                ])
              }}
            </span>
            <div
              class="spec"
              v-if="item.adesione == 'NO' && item.ora_limite_inserimento"
            >
              {{
                $t('servizi.giornalieri.text_inserimento', [
                  item.ora_limite_inserimento,
                  dataAdesioneLong
                ])
              }}
            </div>
          </div>
          <div v-if="marketplace.length == 0" class="col-xs-12 marketplace">
            {{
              $t('servizi_giornalieri.text_nessun_servizio', [dataAdesioneFull])
            }}
          </div>
        </div>
        <div class="col-xs-12" v-if="errorMessage">
          <span class="label label-danger">{{ errorMessage }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common.js'
import ServiziGiornalieri from '@/components/Mastercom/Mixins/ServiziGiornalieri.js'

import Badge from '@/components/Mastercom/UIComponents/Badge'
import CreditoServizi from '@/components/Mastercom/UIComponents/CreditoServizi'
import { Dialog, Calendar } from 'element-ui'
Vue.use(Calendar)
Vue.use(Dialog)
export default {
  name: 'ServiziGiornalieri',
  mixins: [Common, ServiziGiornalieri],
  components: {
    Badge,
    CreditoServizi
  },
  data() {
    return {
      prenotazioniPanelVisible: false,
      marketplaceLoading: false,
      data_adesione: null,
      loaded: false,
      today: new Date()
    }
  },
  computed: {
    dataAdesioneLong() {
      return this.$options.filters.longdate(this.data_adesione)
    },
    dataAdesioneFull() {
      return this.$options.filters.fulldate(this.data_adesione)
    }
  },
  methods: {
    giornoFestivo(data) {
      if (this.dateFestivita.filter(x => x.date === data).length > 0) {
        return true
      }
      return false
    },
    giornoAttivo(data) {
      if (this.dateAttivita.filter(x => x.date === data).length > 0) {
        return true
      }
    },
    openDialog(data) {
      let cmpDate = this.$dayjs(data, 'YYYY-MM-DD', 'it')
      let today = this.$dayjs(
        this.$dayjs().format('YYYY-MM-DD'),
        'YYYY-MM-DD',
        'it'
      )
      if (cmpDate < today) {
        return false
      }
      if (!this.giornoAttivo(data)) {
        return false
      }
      this.prenotazioniPanelVisible = true
      this.data_adesione = null
      this.marketplaceLoading = true
      this.clearMarketplace()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          return this.isSectionEnabled('servizi_giornalieri')
        })
        .then(enabled => {
          if (enabled === true) {
            this.data_adesione = data
            this.setDataAdesione(data)
            return this.loadMarketplace(this.authObject)
          }
          return enabled
        })
        .then(() => {
          this.marketplaceLoading = false
        })
        .catch(error => {
          this.doSetError(error)
        })
    },
    doAdesioneServizio(data, item) {
      this.initScuolaStudente(this.authObject)
        .then(() => {
          return this.isSectionEnabled('servizi_giornalieri')
        })
        .then(enabled => {
          if (enabled === true) {
            this.marketplaceLoading = true
            this.setDataAdesione(data)
            this.setMarketplaceAdesione(item.id_marketplace)
            if (item.adesione === 'NO') {
              return this.adesioneServizio(this.authObject)
            } else {
              return this.cancellazioneServizio(this.authObject)
            }
          }
          return enabled
        })
        .then(() => {
          this.marketplaceLoading = false
          this.loadPrenotazioniGiornaliere(this.authObject)
        })
        .catch(error => {
          this.doSetError(error)
        })
    },
    doLoadPrenotazioniGiornaliere(aggiornamenti = false) {
      this.showLoader()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          return this.isSectionEnabled('servizi_giornalieri')
        })
        .then(enabled => {
          if (enabled === true) {
            return this.loadCalendario(this.authObject)
          }
          return enabled
        })
        .then(enabled => {
          if (enabled === true) {
            return this.loadPrenotazioniGiornaliere(this.authObject)
          }
          return enabled
        })
        .then(enabled => {
          this.loaded = enabled
        })
        .catch(error => {
          this.doSetError(error)
        })
        .finally(() => {
          this.hideLoader()
          if (aggiornamenti === true) {
            this.doLoadAggiornamenti()
          }
        })
    }
  },
  mounted() {
    this.doLoadPrenotazioniGiornaliere()
  }
}
</script>

<style lang="scss" scoped>
.el-calendar-table .el-calendar-day {
  min-height: 90px;
  padding: 0px;
  height: auto;
}

.el-calendar-table:not(.is-range) td.prev,
.el-calendar-table:not(.is-range) td.next,
.el-calendar-table:not(.is-range) td.next,
.el-calendar-table:not(.is-range) td.prev {
  .full-cell {
    &.festivo {
      background-color: #fbfbfb;
      h5 {
        color: #ff9999;
      }
    }
  }
}
.full-cell {
  width: 100%;
  height: 100%;
  padding: 8px;
  h5 {
    margin-top: 0px;
    font-weight: 700;
  }
  &.festivo {
    background-color: #f8f9f9;
    h5 {
      color: #ff0000;
    }
  }
  span {
    float: left;
    width: 100%;
    &.label {
      display: inline-block;
      width: auto;
      margin-right: 0.5em;
    }
  }
}
.marketplace-loader {
  min-height: 120px;
}
.marketplace {
  font-size: 1.2em;
  padding: 0.5em 0em 1.5em 0em;
  border-bottom: 1px solid #e8e8e8;
  cursor: pointer;
  .spec {
    font-size: 0.8em;
    padding: 0.3em 1.8em;
    float: left;
    width: 99%;
  }
}
.top-card {
  margin-bottom: 10px;
}
</style>
