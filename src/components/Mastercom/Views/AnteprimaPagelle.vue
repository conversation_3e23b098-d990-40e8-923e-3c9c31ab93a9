<template>
  <div class="app-genitori" id="container-pagelle">
    <h2 class="ubuntu-bold"><PERSON><PERSON> (riepilogo)</h2>
    <div v-if="pagelle.length > 0" class="row">
      <item-list
        :oggetti="pagelle"
        titolo=""
        class="col-xs-12 col-sm-11"
        :has-date="false"
        :open-in-msg-box="true"
        :download-item="true"
        :dialog-width="'96%'"
        :dialog-top="'1vh'"
        :dialog-class="'dialog-pagella'"
        :show-download="false"
        v-if="pagelle.length"
      ></item-list>
    </div>
    <div class="col-xs-12 col-sm-11" v-if="pagelle.length === 0 && !loading">
      <p class="text-center">Nessuna pagella disponibile.</p>
    </div>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common'
import ItemList from '@/components/Mastercom/UIComponents/ItemList.vue'
import { mapActions, mapGetters } from 'vuex'
export default {
  name: 'AnteprimaPagelle',
  mixins: [Common],
  components: {
    ItemList
  },
  data: function() {
    return {
      loading: false
    }
  },
  computed: {
    pagelle() {
      return this._pagelle.map(x => {
        let new_allegati = []
        for (let a of x.allegati) {
          a.allegato = a.allegato.replace('api/v3', 'v3')
          new_allegati.push(a)
        }
        x.allegati = new_allegati
        return x
      })
    },
    ...mapGetters({
      _pagelle: 'pagelle/pagelle'
    })
  },
  methods: {
    doLoadPagelle() {
      this.loading = true
      this.reset()
      this.showLoader()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          return this.isSectionEnabled('pagelle')
        })
        .then(enabled => {
          if (enabled === true) {
            return this.loadPagelle(this.authObject)
          }
          return enabled
        })
        .then(enabled => {
          if (enabled) {
            this.hideLoader()
            this.loading = false
          }
        })
        .catch(() => {
          this.hideLoader()
          this.loading = false
        })
    },
    doDownload(item) {
      this.setUrlAllegato(item.allegati)
    },
    ...mapActions({
      loadPagelle: 'pagelle/loadPagelle',
      downloadPagella: 'pagelle/loadAllegato',
      setUrlAllegato: 'pagelle/setUrlAllegato',
      reset: 'pagelle/reset'
    })
  },
  mounted() {
    this.doLoadPagelle(true)
  }
}
</script>

<style lang="scss" scoped>
@import url('https://fonts.googleapis.com/css2?family=Ubuntu:ital,wght@0,300;0,400;0,500;0,700;1,300;1,400;1,500;1,700&display=swap');
h2.ubuntu-bold {
  font-family: 'Ubuntu', sans-serif;
  font-weight: bold;
  margin-bottom: 20px;
}
.app-genitori {
  padding: 20px;
  background-color: #fff;
}
/deep/ ul {
  list-style-type: none;
  font-family: 'Ubuntu', sans-serif;
  li {
    border-left: 3px solid rgb(236, 197, 6);
    margin-bottom: 20px;
  }
  p,
  body,
  .el-popup-parent--hidden {
    font-family: 'Ubuntu', sans-serif;
  }
}
/deep/ .el-dialog {
  margin-top: 10px;
  * {
    font-family: 'Ubuntu', sans-serif;
  }
}
</style>
