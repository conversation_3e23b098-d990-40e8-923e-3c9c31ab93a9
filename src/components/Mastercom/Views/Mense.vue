<template>
  <div class="wrapper mastercom">
    <div class="row" v-show="loaded">
      <div class="col-xs-12 col-sm-6">
        <div class="col-xs-12" style="height: 550px">
          <elenco-pasti-fruiti-panel
            v-if="loadPanels"
          ></elenco-pasti-fruiti-panel>
          <div class="clearfix"></div>
        </div>

        <div class="col-xs-12">
          <mense-pagamenti-panel v-if="loadPanels"></mense-pagamenti-panel>
        </div>
      </div>
      <div class="col-xs-12 col-sm-6">
        <div class="col-xs-12">
          <credito-residuo-panel v-if="loadPanels"></credito-residuo-panel>
        </div>
        <div class="col-xs-12" v-if="showPastiPanel">
          <pasti-panel v-if="loadPanels"></pasti-panel>
          <div v-else>no panel</div>
        </div>
        <div v-else>No</div>
      </div>
    </div>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common.js'
import Mense from '@/components/Mastercom/Mixins/Mense.js'

import PastiPanel from '@/components/Mastercom/UIComponents/Panels/PastiPanel'
import MensePagamentiPanel from '@/components/Mastercom/UIComponents/Panels/MensePagamentiPanel'
import CreditoResiduoPanel from '@/components/Mastercom/UIComponents/Panels/CreditoResiduoPanel'
import ElencoPastiFruitiPanel from '@/components/Mastercom/UIComponents/Panels/ElencoPastiFruitiPanel'

export default {
  name: 'Mense',
  mixins: [Common, Mense],
  components: {
    PastiPanel,
    CreditoResiduoPanel,
    MensePagamentiPanel,
    ElencoPastiFruitiPanel
  },
  data() {
    return {
      loaded: false,
      showPastiPanel: false,
      loadPanels: false
    }
  },
  mounted() {
    this.doLoadMense(true)
  },
  methods: {
    doLoadMense(aggiornamenti = false) {
      this.loaded = false
      this.loadPanels = false
      this.showLoader()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          // console.log('MENSA 1')
          return this.isSectionEnabled('mense')
        })
        .then(enabled => {
          // console.log('MENSA 2 ', enabled)
          if (enabled === true) {
            enabled = this.loadMense(this.authObject)
          }
          return enabled
        })
        .then(enabled => {
          // console.log('MENSA 3 ', enabled, this.idPastoCorrente)
          if (enabled && this.idPastoCorrente) {
            // console.log('MENSA: CARICO PASTO CORRENTE ', this.idPastoCorrente)
            this.loadPastoCorrente(this.authObject)
          }
          return enabled
        })
        .then(enabled => {
          this.showPastiPanel = enabled
          this.loaded = enabled
          this.loadPanels = enabled
        })
        .catch(error => {
          console.log('ERRORE MENSE', error)
          this.doSetError(error)
        })
        .finally(() => {
          this.hideLoader()
          if (aggiornamenti === true) {
            this.doLoadAggiornamenti()
          }
        })
    }
  }
}
</script>

<style lang="scss" scoped></style>
