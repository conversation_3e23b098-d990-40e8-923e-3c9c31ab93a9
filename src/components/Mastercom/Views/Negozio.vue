<template>
  <negozio-desktop-component
    :auth-object="JSON.stringify(authObject)"
    :studente-corrente="JSON.stringify(studenteCorrente)"
  ></negozio-desktop-component>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common'

// Import the web component
import '@/components/WebComponents/NegozioDesktopWebComponent.js'

/**
 * Desktop wrapper for Negozio view
 * Simple router target that passes Vuex store auth to NegozioDesktopWebComponent
 * Web component handles all UI interactions including notifications
 * For mobile app access, see Mobile/NegozioMobile.vue
 */
export default {
  name: 'NegozioDesktop',
  mixins: [Common]
}
</script>

<style lang="scss" scoped>
/* Styles are handled by the web component */
</style>
