<template>
  <div class="mastercom" id="container-note-tutor">
    <div v-if="loaded">
      <div v-if="noteDisplay.length">
        <fade-transition group tag="div" :duration="250">
          <div v-for="nota in noteDisplay" :key="nota.id_nota">
            <el-card class="box-card card note-card">
              <div slot="header" class="row">
                <div class="col-xs-12 note-title">
                  <span class="note-date">{{ nota.data_ita }}</span>
                  {{ $t(`note_tutor.${nota.tipo_messaggio}`) }}
                </div>
                <div class="col-xs-12 note-status">
                  {{
                    $t('note_tutor.data_ora_inserimento', [
                      nota.nome_completo_tutor,
                      nota.data_inserimento_ita,
                      nota.ora_inserimento
                    ])
                  }}
                </div>
              </div>
              <div>
                {{ nota.messaggio }}
                <div class="firme">
                  <h6>{{ $t('note_tutor.firme_title') }}</h6>
                  <div v-if="nota.firme.length">
                    <div
                      v-for="firma in nota.firme"
                      :key="`${nota.id_nota}-${firma.id_parente}`"
                    >
                      <div>{{ firma.data_ita }} {{ firma.nome_completo }}</div>
                    </div>
                  </div>
                  <div v-else>
                    <div class="note-no-content">
                      {{ $t('note_tutor.nessuna_firma') }}
                    </div>
                  </div>
                  <div class="button">
                    <button
                      class="btn btn-icon btn-sm btn-fill"
                      @click="doAggiungiFirma(nota)"
                    >
                      <i class="ti-pencil"></i>
                      {{ $t('note_tutor.button_firma') }}
                    </button>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </fade-transition>
      </div>
      <div v-else-if="loaded">
        <div class="card">
          <div class="card-content">
            {{ $t('main.no_content') }}
          </div>
        </div>
      </div>
    </div>
    <div v-else>loading</div>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common.js'
import { mapGetters, mapActions } from 'vuex'

import { Card, MessageBox } from 'element-ui'

Vue.use(Card)
export default {
  name: 'NoteTutor',
  mixins: [Common],
  data() {
    return {
      loaded: false,
      showFirmeDialog: false
    }
  },
  computed: {
    noteDisplay() {
      let data = this.note
      return data
    },
    ...mapGetters({
      note: 'noteTutor/all'
    })
  },
  methods: {
    doAggiungiFirma(nota) {
      MessageBox.confirm(
        this.$t('note_tutor.inserisci_firma'),
        this.$t('note_tutor.firme_title'),
        {
          confirmButtonText: this.$t('note_tutor.firma_confirm'),
          cancelButtonText: this.$t('note_tutor.firma_cancel')
        }
      )
        .then(() => {
          this.showLoader()
          this.setNotaFirma(nota.id_nota)
          return this.postFirma(this.authObject)
        })
        .then(() => {
          return this.loadNoteTutor(this.authObject)
        })
        .finally(() => {
          this.hideLoader()
        })
    },
    doLoadNoteTutor(aggiornamenti = false) {
      this.showLoader()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          return this.isSectionEnabled('note_tutor')
        })
        .then(enabled => {
          if (enabled === true) {
            this.loadNoteTutor(this.authObject)
          }
          return enabled
        })
        .then(enabled => {
          this.loaded = enabled
          if (aggiornamenti) {
            this.doLoadAggiornamenti()
          }
        })
        .catch(error => {
          this.setError(error)
        })
        .finally(() => {
          this.hideLoader()
        })
    },
    ...mapActions({
      loadNoteTutor: 'noteTutor/loadNoteTutor',
      setNotaFirma: 'noteTutor/setNotaFirma',
      postFirma: 'noteTutor/postFirma'
    })
  },
  mounted() {
    this.doLoadNoteTutor(true)
  }
}
</script>

<style lang="scss">
.note-card {
  .el-card__header {
    background-color: #54b07d;
    color: #fff;
  }
}
</style>

<style scoped lang="scss">
.local-filters {
  padding: 10px;
}
.firme {
  margin-top: 0.9em;
  padding-top: 0.1em;
  border-top: 1px solid #ededed;
  .button {
    margin-top: 2em;
  }
}
.note-status {
  padding-top: 0.5em;
  padding-bottom: 0.3em;
}
.note-title {
  font-size: 1.2em;
  font-weight: 700;
}
.note-no-content {
  padding-bottom: 1em;
}
</style>
