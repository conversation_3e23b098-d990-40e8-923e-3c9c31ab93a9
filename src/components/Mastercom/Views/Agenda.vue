<template>
  <div class="mastercom" id="container-agenda">
    <div v-if="loaded && active">
      <time-line v-if="agendaDisplay.length">
        <time-line-item
          v-for="(item, index) in agendaDisplay"
          class="timeline-inverted"
          :key="index"
          :month-header="
            index > 0
              ? displayMonthHeader(item, agendaDisplay[index - 1])
              : $dayjs(item.data).format('MMMM')
          "
          :badge-text="
            index > 0
              ? displayDataItem(item, agendaDisplay[index - 1])
              : $dayjs(item.data).format('ddd DD/MM')
          "
          :data="$dayjs(item.data).format('DDMMYYYY')"
        >
          <div slot="header" class="full-relative-width">
            <materia
              :titolo="item.materia"
              :indice-colore="item.indice_colore"
            ></materia>
            <insegnante :nome="item.insegnante"></insegnante>
          </div>
          <h5 slot="title">{{ item.titolo }}</h5>
          <p slot="body" class="text-primary" v-html="item.sottotitolo"></p>

          <h6 slot="footer">
            <i class="ti-time"></i>
            {{ item.data | simpledate }}
          </h6>
        </time-line-item>
      </time-line>
      <div v-else>
        <div class="card">
          <div class="card-content">
            {{ $t('main.no_content') }}
            <span v-if="descFiltroMateria">{{ descFiltroMateria }}</span>
            <span v-if="descFiltroPeriodo">{{ descFiltroPeriodo }}</span>
          </div>
        </div>
      </div>
    </div>
    <div v-else>{{ $t('main.loading') }}</div>
  </div>
</template>

<script>
import TimeLine from '@/components/Mastercom/UIComponents/TimeLine.vue'
import TimeLineItem from '@/components/Mastercom/UIComponents/TimeLineItem3.vue'
import Materia from '@/components/Mastercom/UIComponents/Materia.vue'
import Insegnante from '@/components/Mastercom/UIComponents/Insegnante.vue'
import Common from '@/components/Mastercom/Mixins/Common.js'

import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'Agenda',
  mixins: [Common],
  components: {
    Insegnante,
    TimeLine,
    TimeLineItem,
    Materia
  },
  data() {
    return {
      active: false,
      loaded: false,
      oldBadgeText: '',
      today: this.$dayjs()
    }
  },
  computed: {
    agendaDisplay() {
      let data = this.agenda

      if (this.filtriMateria.length) {
        data = data.filter(a => {
          return this.filtriMateria.includes(a.id_materia)
        })
      }

      if (this.filtriData.length) {
        data = data.filter(a => {
          return a.data >= this.filtriData[0] && a.data <= this.filtriData[1]
        })
      }
      return data
    },
    firstAvailableDate() {
      let items = this.agenda.filter(a => a.data >= this.today.toISOString())
      // let date = items.length > 0 ? items[0].data : this.agenda.last().data
      return this.$dayjs(items[0].data)
    },
    firstAvailableAgendaItem() {
      return `date_${this.firstAvailableDate.format('YYYY-MM-DD')}`
    },
    ...mapGetters({
      agenda: 'agenda/all'
    })
  },
  methods: {
    goto(refName) {
      let panel = document.querySelector('.main-panel')
      let element = document.querySelector(`.${refName}`)
      // setTimeout(() => {
      //   let element2 = document.querySelector(`.${refName}`)
      //   console.log('GOTO ELEMENT 2', element2)
      // })
      let top = element.offsetTop
      panel.scrollTo(0, top)
    },
    doLoadAgenda(aggiornamenti = false) {
      this.showLoader()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          this.active = this.isSectionEnabled('agenda')
        })
        .catch(error => {
          this.hideLoader()
          this.setError(error)
        })
      this.loadAgenda(this.authObject)
        .then(enabled => {
          this.loaded = enabled
          if (enabled === true) {
            setTimeout(() =>
              this.goto(`date_${this.firstAvailableDate.format('DDMMYYYY')}`)
            )
          }
        })
        .catch(error => {
          this.setError(error)
        })
        .finally(() => {
          this.hideLoader()
        })
      if (aggiornamenti === true) {
        this.doLoadAggiornamenti()
      }
      return true
    },
    ...mapActions({
      loadAgenda: 'agenda/load'
    })
  },
  mounted() {
    this.doClearFiltri().then(() => this.doLoadAgenda(true))
  }
}
</script>

<style></style>
