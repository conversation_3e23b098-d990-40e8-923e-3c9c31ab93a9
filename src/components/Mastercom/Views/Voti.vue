<template>
  <div class="mastercom" id="container-voti">
    <filters v-if="loaded && active"></filters>
    <div v-if="loaded && active">
      <voti-list
        :voti="votiDisplayType"
        :titolo="$t('voti.titolo')"
        sottotitolo=""
        v-if="votiDisplay.length"
        :class="[mostraMediaVoti ? 'col-sm-6' : 'col-sm-12', 'col-xs-12']"
        :on-confirm-action="
          studenteCorrente.presa_visione_voti ? doPresaVisione : null
        "
        :confirm-action-text="
          studenteCorrente.presa_visione_voti ? $t('voti.presa_visione') : null
        "
      ></voti-list>
      <div class="col-xs-12 col-sm-12 col-md-6" v-else>
        <div class="card">
          <div class="card-content">
            {{ $t('main.no_content') }}
            <span v-if="descFiltroMateria">{{ descFiltroMateria }}</span>
            <span v-if="descFiltroPeriodo">{{ descFiltroPeriodo }}</span>
          </div>
        </div>
      </div>
      <div class="col-xs-12 col-md-6" v-if="mediaCalcolabile">
        <div class="row">
          <div
            v-for="(item, index) in mediaPeriodoMateria"
            :key="`mediaperiodomateria_${index}`"
            :class="[
              'col-sm-12',
              `col-lg-${mediaPeriodoMateria.length == 2 ? '6' : '12'}`,
              `medie-materia`
            ]"
          >
            <mini-card-horizontal>
              <h6 slot="header">Media {{ item.periodo }}</h6>
              <div class="numbers" slot="content">
                <div
                  v-if="item.media.scritto"
                  :class="
                    getGridClassMediaMateria(item.media, item.media.scritto)
                  "
                >
                  <h6>scritto</h6>
                  <strong>{{ item.media.scritto }}</strong>
                  <small>
                    {{ item.media.nvotiScritto }}
                    vot{{ item.media.nvotiScritto > 1 ? 'i' : 'o' }}
                  </small>
                </div>

                <div
                  v-if="item.media.orale"
                  :class="
                    getGridClassMediaMateria(item.media, item.media.orale)
                  "
                >
                  <h6>orale</h6>
                  <strong>{{ item.media.orale }}</strong>
                  <small>
                    {{ item.media.nvotiOrale }}
                    vot{{ item.media.nvotiOrale > 1 ? 'i' : 'o' }}
                  </small>
                </div>

                <div
                  v-if="item.media.pratico"
                  :class="
                    getGridClassMediaMateria(item.media, item.media.pratico)
                  "
                >
                  <h6>pratico</h6>
                  <strong>{{ item.media.pratico }}</strong>
                  <small>
                    {{ item.media.nvotiPratico }}
                    vot{{ item.media.nvotiPratico > 1 ? 'i' : 'o' }}
                  </small>
                </div>
              </div>
              <div class="footer" slot="footer">
                <div class="col-xs-6">
                  <h6>Media complessiva</h6>
                </div>
                <div
                  class="col-xs-6 numbers"
                  :class="getColorClassMediaMateria(mediaPeriodo[index].media)"
                >
                  <strong>{{ mediaPeriodo[index].media }}</strong>
                </div>
              </div>
            </mini-card-horizontal>
          </div>
        </div>
        <div class="row" v-show="false">
          <div
            v-for="(item, index) in mediaPeriodo"
            :key="`mediaperiodo_${index}`"
            :class="['col-xs-12', `col-sm-${12 / mediaPeriodo.length}`]"
          >
            <mini-card-horizontal>
              <div class="numbers" slot="content">
                <p>Media {{ item.periodo }}</p>
                {{ item.media }}
              </div>
            </mini-card-horizontal>
          </div>
        </div>
        <div class="clearfix">
          <div v-if="votiDisplay.length">
            <chart-card
              :chart-data="graficoAndamentoScolastico.data"
              :chart-options="graficoAndamentoScolastico.options"
              chart-type="BarLine"
            >
              <h6 slot="header">Andamento {{ getMateriaFiltrata() }}</h6>
            </chart-card>
          </div>
        </div>
        <div class="clearfix"></div>
        <div v-if="filtriMateria">
          <chart-card
            :chart-data="mediaMensileMateria.data"
            :chart-options="mediaMensileMateria.options"
            chart-type="BarLine"
          >
            <h6 slot="header">Media Mensile</h6>
          </chart-card>
        </div>
      </div>
      <div
        class="col-xs-12 col-md-6"
        v-if="
          votoPesato === true &&
            mediaPesataCalcolabile === false &&
            mostraMediavoti === true
        "
      >
        <div class="card">
          <div class="card-content text-primary">
            Per i voti in elenco non è possibile calcolare la media ponderata.
          </div>
        </div>
      </div>
    </div>
    <div v-else>loading</div>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common.js'
import { mediaReducer, mediaPesataReducer } from '@/reducers.js'

import VotiList from '@/components/Mastercom/UIComponents/VotiList.vue'
import MiniCardHorizontal from '@/components/Mastercom/UIComponents/MiniCardHorizontal.vue'
import Filters from '@/components/Mastercom/UIComponents/Filters.vue'
import ChartCard from '@/components/Mastercom/UIComponents/Charts/ChartCard.vue'
import { mapState, mapGetters, mapActions } from 'vuex'
import Colors from '@/assets/sass/mastercom_colors.scss'
import { MessageBox } from 'element-ui'

export default {
  name: 'Voti',
  mixins: [Common],
  components: {
    VotiList,
    ChartCard,
    MiniCardHorizontal,
    Filters
  },
  watch: {
    filtriMateria: function() {
      this.mediaMensileMateria.data.datasets = []
      this.graficoAndamentoScolastico.data.datasets = []
      let barDatasets = this.calcMediaVotiMese()
      this.mediaMensileMateria.data.labels = this.CommonLabelMesi
      if (barDatasets) {
        this.mediaMensileMateria.data.datasets = [...barDatasets]
      }
      this.drawAndamentoProgressivo(this.calcAndamentoProgressivo())
    }
  },
  computed: {
    // Calcola la media scolastica globale per periodo
    mediaReducerFunc() {
      return this.votoPesato ? mediaPesataReducer : mediaReducer
    },
    mediaCalcolabile() {
      let result = true
      if (this.mostraMediaVoti === false) {
        result = false
      }
      if (
        result === true &&
        this.votoPesato === true &&
        this.mediaPesataCalcolabile === false
      ) {
        result = false
      }
      return result
    },
    votiListClass() {
      let result = 'col-md-6'
      if (this.mediaCalcolabile === false) {
        if (this.votoPesato === false) {
          result = 'col-md-12'
        }
      }
      return result
    },
    mediaPeriodo() {
      return this.periodiScolastici.map(p => {
        let media = this.votiFiltroMateria
          .filter(v => this.isVoto(v))
          .filter(v => {
            return this.isVotoPeriodo(v.data, p)
          })
          .filter(v => v.voto_numerico > 0)

        media = media.reduce(this.mediaReducerFunc, 0) || '--'
        return {
          periodo: `${p.nome}`,
          media: media
        }
      })
    },
    // Calcola la media scolastica per periodo per Materia
    mediaPeriodoMateria() {
      return this.periodiScolastici.map(p => {
        let media = null
        let voti = this.votiFiltroMateria
          .filter(v => {
            return this.isVotoPeriodo(v.data, p)
          })
          .filter(v => v.voto_numerico > 0)
        if (voti.length) {
          let mediaScritto =
            voti
              .filter(v => v.sottotitolo.toUpperCase() === 'SCRITTO')
              .reduce(this.mediaReducerFunc, 0) || ''
          let mediaOrale =
            voti
              .filter(v => v.sottotitolo.toUpperCase() === 'ORALE')
              .reduce(this.mediaReducerFunc, 0) || ''

          let mediaPratico =
            voti
              .filter(v => v.sottotitolo.toUpperCase() === 'PRATICO')
              .reduce(this.mediaReducerFunc, 0) || ''

          let result = {
            tipiVoto: []
          }
          if (typeof mediaScritto === 'number') {
            result.scritto = mediaScritto
            let votiScritto = voti.filter(
              v => v.sottotitolo.toUpperCase() === 'SCRITTO'
            )
            if (this.votoPesato === true) {
              votiScritto = votiScritto.filter(
                v => v.voto_pesato === true && v.valore_peso > 0
              )
            }
            result.nvotiScritto = votiScritto.length
            result.tipiVoto.push('scritto')
          }

          if (typeof mediaOrale === 'number') {
            result.orale = mediaOrale
            let votiOrale = voti.filter(
              v => v.sottotitolo.toUpperCase() === 'ORALE'
            )
            if (this.votoPesato === true) {
              votiOrale = votiOrale.filter(
                v => v.voto_pesato === true && v.valore_peso > 0
              )
            }
            result.nvotiOrale = votiOrale.length
            result.tipiVoto.push('orale')
          }

          if (typeof mediaPratico === 'number') {
            result.pratico = mediaPratico

            let votiPratico = voti.filter(
              v => v.sottotitolo.toUpperCase() === 'PRATICO'
            )
            if (this.votoPesato === true) {
              votiPratico = votiPratico.filter(
                v => v.voto_pesato === true && v.valore_peso > 0
              )
            }
            result.nvotiPratico = votiPratico.length
            result.tipiVoto.push('pratico')
          }
          media = result
        } else {
          media = '--'
        }

        return {
          periodo: `${p.nome}`,
          media: media
        }
      })
    },
    // Mostra i voti filtrandoli per materia e per periodo
    votiDisplay() {
      let data = this.voti
      if (this.filtriMateria.length) {
        data = data.filter(a => {
          return this.filtriMateria.includes(a.id_materia)
        })
      }

      if (this.filtriData.length) {
        data = data.filter(a => {
          return (
            this.$dayjs(a.data) >= this.$dayjs(this.filtriData[0]) &&
            this.$dayjs(a.data) <= this.$dayjs(this.filtriData[1])
          )
        })
      }

      data = data.map(x => {
        x.confirm_text = `${this.$i18n.t('voti.data_presa_visione', [
          x.confirm_date,
          x.confirm_time
        ])}`
        return x
      })
      return data
    },
    votiDisplayType() {
      let data = this.votiDisplay.filter(v => {
        let show = true
        if (
          v.sottotitolo.toUpperCase() === 'SCRITTO' &&
          !this.showVotoScritto
        ) {
          show = false
        }

        if (v.sottotitolo.toUpperCase() === 'ORALE' && !this.showVotoOrale) {
          show = false
        }

        if (
          v.sottotitolo.toUpperCase() === 'PRATICO' &&
          !this.showVotoPratico
        ) {
          show = false
        }
        return show
      })
      return data
    },
    votiFiltroMateria() {
      let data = this.voti
      if (this.filtriMateria.length) {
        data = data.filter(a => {
          return this.filtriMateria.includes(a.id_materia)
        })
      }
      return data
    },
    mediaPesataCalcolabile() {
      return (
        this.votiDisplay.filter(
          v => v.voto_pesato === true && v.valore_peso > 0
        ).length > //=== this.votiDisplay.length
        this.votiDisplay.filter(
          v => v.voto_pesato === true && v.valore_peso === 0
        ).length
      )
    },
    ...mapState({
      showVotoScritto: state => state.voti.showVotoScritto,
      showVotoOrale: state => state.voti.showVotoOrale,
      showVotoPratico: state => state.voti.showVotoPratico
    }),
    ...mapGetters({
      voti: 'voti/all',
      votoPesato: 'voti/votoPesato',
      mostraMediaVoti: 'main/mediaVoti',
      settimaneScolastiche: 'main/settimaneScolastiche'
    })
  },
  data() {
    return {
      active: false,
      loaded: false,
      mediaVotiMesi: [],
      mediaMensileMateria: {
        data: {
          labels: this.CommonLabelMesi,
          datasets: []
        },
        options: {
          responsive: true,
          maintainAspectRatio: false
        }
      },
      graficoAndamentoScolastico: {
        data: {
          xLabels: [],
          datasets: []
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            yAxes: [
              {
                ticks: {
                  suggestedMin: 0,
                  suggestedMax: 10,
                  beginAtZero: true
                }
              }
            ]
          }
        }
      }
    }
  },
  methods: {
    doPresaVisione(item) {
      MessageBox.confirm(this.$i18n.t('voti.msg_conferma'), {
        confirmButtonText: this.$i18n.t('voti.btn_conferma_richiesta'),
        cancelButtonText: this.$i18n.t('voti.btn_annulla_richiesta'),
        closeOnClickModal: false,
        type: 'warning',
        customClass: 'mastercom-msg-box',
        beforeClose: (action, instance, done) => {
          if (action == 'confirm') {
            this.setPresaVisione(item.id_voto)
            instance.confirmButtonLoading = true
            this.inviaPresaVisione(this.authObject)
              .then(() => {
                this.loadVoti(this.authObject)
              })
              .then(() => done())
              .catch(error => {
                this.hideLoader()
                this.setError(error)
              })
              .finally(() => {
                instance.confirmButtonLoading = false
              })
          } else {
            done()
          }
        }
      })
    },
    // Controlla l'appartenenza di un voto al periodo
    isVotoPeriodo(data_voto, periodo) {
      return (
        this.$dayjs(data_voto).isSameOrAfter(
          this.$dayjs(periodo.data_inizio),
          'day'
        ) &&
        this.$dayjs(data_voto).isSameOrBefore(
          this.$dayjs(periodo.data_fine),
          'day'
        )
      )
    },
    isVoto(v) {
      const tipiVoto = ['SCRITTO', 'ORALE', 'PRATICO']
      return tipiVoto.includes(v.sottotitolo.toUpperCase())
    },
    getGridClassMediaMateria(media, valore) {
      let gridClass = `col-xs-${12 / media.tipiVoto.length}`
      let alignClass = 'text-center'
      let colorClass = this.getColorClassMediaMateria(valore)
      return `${gridClass} ${alignClass} ${colorClass}`
    },
    getColorClassMediaMateria(valore) {
      return parseFloat(valore) > 6 ? 'text-success' : 'text-danger'
    },
    getMateriaFiltrata() {
      let result = 'generale'
      if (this.filtriMateria.length) {
        result = `${
          this.materieStudenteCorrente.filter(
            m => m.id === this.filtriMateria[0]
          )[0]['descrizione']
        }`
      }
      return result
    },
    calcAndamentoProgressivo() {
      let inizio = this.settimaneScolastiche[0]['inizio']
      let media = []
      let data = this.voti.filter(v => v.voto_numerico > 0 && this.isVoto(v))

      if (this.filtriMateria.length) {
        data = data.filter(a => this.filtriMateria.includes(a.id_materia))
      }
      for (let s of this.settimaneScolastiche) {
        media.push(
          Math.round(
            data
              .filter(
                v =>
                  this.$dayjs(v.data) >= this.$dayjs(inizio) &&
                  this.$dayjs(v.data) <= this.$dayjs(s['fine'])
              )
              .reduce(this.mediaReducerFunc, 0) * 100
          ) / 100
        )
      }
      return media
    },
    calcMediaVotiMese() {
      if (!this.filtriMateria) {
        return
      }
      return this.filtriMateria.map(materia => {
        let data = this.CommonLabelMesi.map(mese => {
          return this.voti
            .filter(voto => {
              return (
                this.$dayjs(voto.data)
                  .format('MMM')
                  .toUpperCase() === mese.toUpperCase() &&
                voto.id_materia === materia
              )
            })
            .reduce(this.mediaReducerFunc, 0)
        })

        return {
          label: this.materieStudenteCorrente.filter(m => m.id === materia)[0]
            .descrizione,
          backgroundColor:
            Colors[
              `color_${
                this.materieStudenteCorrente.filter(m => m.id === materia)[0]
                  .indiceColore
              }`
            ],
          data: data,
          type: 'bar'
        }
      })
    },
    drawAndamentoProgressivo(media) {
      let labelMedia = `media ${this.getMateriaFiltrata()}`
      this.graficoAndamentoScolastico.data.xLabels = this.settimaneScolastiche.map(
        x => this.$dayjs(x.fine).format('DD MMM')
      )

      let backgroundColor =
        [...media].pop() >= 6
          ? this.hex2rgba('#22BB00', 0.4)
          : this.hex2rgba('#FF0000', 0.4)

      this.graficoAndamentoScolastico.data.datasets = [
        {
          label: labelMedia,
          data: media,
          type: 'line',
          backgroundColor: backgroundColor
        }
      ]
    },
    doLoadVoti(aggiornamenti = false) {
      this.showLoader()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          this.active = this.isSectionEnabled('voti')
        })
        .catch(error => {
          this.hideLoader()
          this.setError(error)
        })

      this.loadVoti(this.authObject)
        .then(enabled => {
          this.loaded = enabled
          if (enabled === true) {
            this.drawAndamentoProgressivo(this.calcAndamentoProgressivo())
          }
        })
        .catch(error => {
          this.setError(error)
        })
        .finally(() => {
          this.hideLoader()
        })
      if (aggiornamenti === true) {
        this.doLoadAggiornamenti()
      }
    },
    ...mapActions({
      loadVoti: 'voti/loadVoti',
      inviaPresaVisione: 'voti/inviaPresaVisione',
      setPresaVisione: 'voti/setPresaVisione',
      reset: 'voti/reset'
    })
  },
  mounted() {
    this.doLoadVoti(true)
  },
  unmounted() {
    this.reset()
  }
}
</script>

<style scoped lang="scss">
li {
  list-style-type: none;
}
.btn {
  width: 38px;
}

.medie-materia {
  .footer {
    overflow: hidden;
    h6 {
      margin-top: 0px;
    }
  }
  .numbers {
    strong {
      font-size: 0.8em;
      width: 99%;
      float: left;
      color: inherit;
    }
    div {
      text-align: center;
    }
    small {
      font-size: 12px;
      text-transform: uppercase;
    }
  }
}
</style>
