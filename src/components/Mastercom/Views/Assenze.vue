<template>
  <div class="mastercom" id="container-assenze">
    <filters v-if="loaded && active" :filtra-materie="false"></filters>
    <div v-if="loaded && active">
      <div v-if="prenotazioneEntrate || prenotazioneUscite" class="row">
        <div class="col-xs-12 col-lg-3" v-if="prenotazioneEntrate">
          <el-button
            type="primary"
            @click="openPrenotazioniEntrateDialog"
            class="btn-prenotazione entrate"
          >
            <i class="ti-arrow-circle-right"></i>
            {{ $t('assenze.btn_prenotazione_entrate') }}
          </el-button>
        </div>
        <div class="col-xs-12 col-lg-3" v-if="prenotazioneUscite">
          <el-button
            type="primary"
            @click="openPrenotazioniUsciteDialog"
            class="btn-prenotazione uscite"
          >
            <i class="ti-arrow-circle-left"></i>
            {{ $t('assenze.btn_prenotazione_uscite') }}
          </el-button>
        </div>
      </div>
    </div>

    <div v-if="loaded && active">
      <item-list
        :oggetti="itemDisplay"
        :parent-action="doAnnullaPrenotazione"
        titolo="Assenze - Uscite anticipate - Entrate in ritardo"
        class="col-xs-12 col-sm-12 col-md-6"
      >
        <div class="local-filters row" slot="filters">
          <div class="col-xs-6">
            <el-switch
              v-model="_showOnlyNonGiustificate"
              :disabled="_showOnlyPresenzeCorso"
            ></el-switch>
            {{ $t('assenze.filter_solo_da_giustificare') }}
          </div>
          <div class="col-xs-6" v-if="presenzeCorsi">
            <el-switch v-model="_showOnlyPresenzeCorso"></el-switch>
            {{ $t('assenze.filter_solo_presenze_corsi') }}
          </div>
        </div>
      </item-list>

      <div class="col-xs-12 col-md-6">
        <div class="col-xs-12 col-lg-4">
          <mini-card card-color="orange">
            <h6 slot="header">Assenze</h6>
            <div class="numbers" slot="content">
              {{ totaleAssenze }}
            </div>
          </mini-card>
        </div>
        <div class="col-xs-12 col-lg-4">
          <mini-card card-color="purple">
            <h6 slot="header">Entrate in ritardo</h6>
            <div class="numbers" slot="content">
              {{ totaleRitardi }}
            </div>
          </mini-card>
        </div>
        <div class="col-xs-12 col-lg-4">
          <mini-card card-color="brown">
            <h6 slot="header">Uscite in anticipo</h6>
            <div class="numbers" slot="content">
              {{ totaleUscite }}
            </div>
          </mini-card>
        </div>

        <div class="col-xs-12" v-if="itemDisplay.length">
          <chart-card
            :chart-data="graficoAssenze.data"
            :chart-options="graficoAssenze.options"
            chart-type="BarLine"
          >
            <h6 slot="header">Assenze</h6>
          </chart-card>
        </div>
      </div>
    </div>
    <div v-else>{{ $t('main.loading') }}</div>
    <el-dialog
      :append-to-body="true"
      :visible.sync="prenotazionePanelVisible"
      top="5vh"
      :width="_dialogWidth()"
      class="prenotazione-dialog"
    >
      <div class="row">
        <h4 class="col-xs-12">
          {{
            tipoPrenotazione == 2
              ? $t('assenze.titolo_form_entrate')
              : $t('assenze.titolo_form_uscite')
          }}
        </h4>
      </div>
      <div class="row">
        <h6 class="col-xs-12 col-md-4">
          {{ $t('assenze.seleziona_data_label') }}
        </h6>
        <div class="col-xs-12 col-md-8">
          <el-date-picker
            v-model="dataPrenotazione"
            type="date"
            format="dd MMMM yyyy"
            value-format="yyyy-MM-dd"
            :picker-options="datePickerOptions"
            :placeholder="$t('assenze.seleziona_data_placeholder')"
          ></el-date-picker>
        </div>
      </div>
      <div class="row">
        <h6 class="col-xs-12 col-md-4">
          {{ $t('assenze.seleziona_orario_label') }}
        </h6>
        <div class="col-xs-12 col-md-8">
          <el-select
            :disabled="caricamentoOpzioni"
            v-model="orarioPrenotazione"
            :placeholder="$t('assenze.seleziona_orario_placeholder')"
          >
            <el-option
              v-for="x in tipoPrenotazione == 2
                ? opzioniEntrata
                : opzioniUscita"
              :key="x.value"
              :label="x.label"
              :value="x.value"
            ></el-option>
          </el-select>
        </div>
      </div>
      <div class="row">
        <h6 class="col-xs-12 col-md-4">
          {{ $t('assenze.motivazione_label') }}
        </h6>
        <div class="col-xs-12 col-md-8">
          <el-input
            type="textfield"
            v-model="motivazionePrenotazione"
            :placeholder="$t('assenze.motivazione_placeholder')"
          ></el-input>
        </div>
      </div>
      <div class="row">
        <div class="col-xs-12 text-center">
          <el-button
            type="primary"
            @click="doInviaPrenotazione()"
            :disabled="checkPrenotazioneValid()"
            class="submit-button"
          >
            {{ $t('assenze.btn_invia_prenotazione') }}
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common.js'
import ItemList from '@/components/Mastercom/UIComponents/ItemList.vue'
import Filters from '@/components/Mastercom/UIComponents/Filters.vue'
import MiniCard from '@/components/Mastercom/UIComponents/MiniCard.vue'

import ChartCard from '@/components/Mastercom/UIComponents/Charts/ChartCard.vue'
import {
  Select,
  Option,
  Switch,
  Dialog,
  DatePicker,
  // TimeSelect,
  Input,
  MessageBox
} from 'element-ui'
import { mapState, mapGetters, mapActions } from 'vuex'

export default {
  name: 'Assenze',
  mixins: [Common],
  components: {
    ItemList,
    MiniCard,
    Filters,
    ChartCard,
    ElSelect: Select,
    ElOption: Option,
    ElSwitch: Switch,
    ElDatePicker: DatePicker,
    ElDialog: Dialog,
    // ElTimeSelect: TimeSelect,
    ElInput: Input
  },
  watch: {
    filtriData: function() {
      this.drawAssenze()
    }
  },
  data() {
    return {
      caricamentoOpzioni: false,
      dataPrenotazione: null,
      orarioPrenotazione: null,
      motivazionePrenotazione: null,
      tipoPrenotazione: null,
      prenotazionePanelVisible: false,
      datePickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 24 * 60 * 60 * 1000
        },
        firstDayOfWeek: 1
      },
      active: false,
      loaded: false,
      // _showOnlyNonGiustificate: false,
      bgOpacity: 1,
      bgFill: false,
      graficoAssenze: {
        data: {
          xLabels: [],
          datasets: []
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: {
            yAxes: [
              {
                ticks: {
                  beginAtZero: true,
                  stepSize: 1
                }
              }
            ]
          }
        }
      }
    }
  },
  computed: {
    totaleAssenze() {
      return this.itemDisplay.filter(a => {
        return a.simbolo === 'A'
      }).length
    },
    totaleUscite() {
      return this.itemDisplay.filter(a => {
        return a.simbolo === 'U' || a.simbolo === 'P'
      }).length
    },
    totaleRitardi() {
      return this.itemDisplay.filter(a => {
        return a.simbolo === 'E' || a.simbolo === 'M' || a.simbolo === 'e'
      }).length
    },
    itemDisplay() {
      let data = this.assenze
      if (this.showOnlyNonGiustificate) {
        data = data.filter(a => {
          return a.giustificata === 'NO' && a.simbolo !== 'C'
        })
      }
      if (this.showOnlyPresenzeCorso) {
        data = data.filter(a => {
          return a.simbolo === 'C'
        })
      }
      if (this.filtriData.length) {
        data = data.filter(a => {
          return (
            this.$dayjs(a.data) >= this.filtriData[0] &&
            this.$dayjs(a.data) <= this.filtriData[1]
          )
        })
      }
      return data
    },
    settimane() {
      if (this.filtriData.length) {
        return this.settimaneScolastiche.filter(v => {
          return (
            this.$dayjs(v.inizio) >= this.$dayjs(this.filtriData[0]) &&
            this.$dayjs(v.fine) <= this.$dayjs(this.filtriData[1])
          )
        })
      } else {
        return this.settimaneScolastiche
      }
    },
    _showOnlyPresenzeCorso: {
      get() {
        return this.showOnlyPresenzeCorso
      },
      set(val) {
        this.setOnlyPresenzeCorso(val)
      }
    },
    _showOnlyNonGiustificate: {
      get() {
        return this.showOnlyNonGiustificate
      },
      set(val) {
        this.setOnlyNonGiustificate(val)
      }
    },
    ...mapState({
      showOnlyNonGiustificate: state => state.assenze.showOnlyNonGiustificate,
      showOnlyPresenzeCorso: state => state.assenze.showOnlyPresenzeCorso
    }),
    ...mapGetters({
      assenze: 'assenze/all',
      presenzeCorsi: 'assenze/presenzeCorsi',
      settimaneScolastiche: 'main/settimaneScolasticheTrascorse',
      prenotazioneEntrate: 'main/prenotazioneEntrate',
      prenotazioneUscite: 'main/prenotazioneUscite',
      opzioniEntrata: 'assenze/opzioniEntrata',
      opzioniUscita: 'assenze/opzioniUscita'
    })
  },
  methods: {
    doInviaPrenotazione() {
      MessageBox.confirm(
        this.$t('assenze.conferma_invio_richiesta'),
        this.$t('main.confirm'),
        {
          confirmButtonText: this.$t('main.confirm'),
          cancelButtonText: this.$t('main.cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          this.setDatiPrenotazione({
            data: this.dataPrenotazione,
            orario: this.orarioPrenotazione,
            motivazione: this.motivazionePrenotazione,
            tipo: this.tipoPrenotazione
          })
          this.inviaPrenotazione(this.authObject)
            .then(result => {
              if (result['data']['result'] != 'OK') {
                MessageBox.alert(
                  result['data']['message'],
                  this.$t('assenze.titolo_messaggio_errore'),
                  {
                    type: 'error'
                  }
                )
              } else {
                this.doLoadAssenze()
              }
              this.prenotazionePanelVisible = false
            })
            .catch(error => {
              this.doSetError(error)
            })
            .finally(() => {
              this.hideLoader()
            })
        })
        .catch(() => {})
    },
    checkPrenotazioneValid() {
      return (
        this.dataPrenotazione === null ||
        this.orarioPrenotazione === null ||
        (this.motivazionePrenotazione === null ||
          this.motivazionePrenotazione === '')
      )
    },
    _dialogWidth() {
      return window.outerWidth > 1024 ? '30%' : '100%'
    },
    resetPrenotazione() {
      this.dataPrenotazione = null
      this.orarioPrenotazione = null
      this.motivazionePrenotazione = null
    },
    openPrenotazioniEntrateDialog() {
      this.resetPrenotazione()
      this.tipoPrenotazione = 2
      this.caricamentoOpzioni = true
      this.prenotazionePanelVisible = true
      this.loadOpzioniPrenotazione(this.authObject).then(() => {
        this.caricamentoOpzioni = false
      })
    },
    openPrenotazioniUsciteDialog() {
      this.resetPrenotazione()
      this.tipoPrenotazione = 3
      this.caricamentoOpzioni = true
      this.prenotazionePanelVisible = true
      this.loadOpzioniPrenotazione(this.authObject).then(() => {
        this.caricamentoOpzioni = false
      })
    },
    changeOnlyNonGiustificate(event) {
      this.setOnlyNonGiustificate(event)
    },
    calcTotaleProgressivo(data) {
      let result = []
      try {
        let inizio = this.$dayjs(this.settimaneScolastiche[0]['inizio'])
        for (let s of this.settimaneScolastiche) {
          result.push(
            data.filter(v => {
              return (
                this.$dayjs(v.data) >= inizio &&
                this.$dayjs(v.data) <= this.$dayjs(s['fine'])
              )
            }).length
          )
        }
      } catch (error) {
        result = []
      }
      return result
    },
    drawAssenze() {
      this.graficoAssenze.data.datasets = []
      let drawAssenze = this.calcTotaleProgressivo(
        this.itemDisplay.filter(item => {
          return item.simbolo === 'A'
        })
      )

      if (drawAssenze.length > 0) {
        this.graficoAssenze.data.datasets.push({
          label: 'Assenze',
          data: drawAssenze,
          type: 'line',
          borderColor: this.hex2rgba('#ff8f5e', 1),
          backgroundColor: this.hex2rgba('#ff8f5e', this.bgOpacity),
          lineTension: 0,
          pointRadius: 0,
          borderWidth: 4,
          fill: this.bgFill
        })
      }

      let drawRitardi = this.calcTotaleProgressivo(
        this.itemDisplay.filter(item => {
          return (
            item.simbolo === 'E' || item.simbolo === 'M' || item.simbolo === 'e'
          )
        })
      )
      if (drawRitardi.length > 0) {
        this.graficoAssenze.data.datasets.push({
          label: 'Entrate in ritardo',
          data: drawRitardi,
          type: 'line',
          borderColor: this.hex2rgba('#baa9ba', 1),
          backgroundColor: this.hex2rgba('#baa9ba', this.bgOpacity),
          lineTension: 0,
          pointRadius: 0,
          borderWidth: 4,
          fill: this.bgFill
        })
      }

      let drawUscite = this.calcTotaleProgressivo(
        this.itemDisplay.filter(item => {
          return item.simbolo === 'U' || item.simbolo === 'P'
        })
      )

      if (drawUscite.length > 0) {
        this.graficoAssenze.data.datasets.push({
          label: 'Uscite in anticipo',
          data: drawUscite,
          type: 'line',
          borderColor: this.hex2rgba('#d6c1ab', 1),
          backgroundColor: this.hex2rgba('#d6c1ab', this.bgOpacity),
          lineTension: 0,
          pointRadius: 0,
          borderWidth: 4,
          fill: this.bgFill
        })
      }

      this.graficoAssenze.data.xLabels = this.settimane.map(x =>
        this.$dayjs(x.fine).format('DD MMM')
      )
    },
    doLoadAssenze(aggiornamenti = false) {
      this.showLoader()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          if (this.isSectionEnabled('assenze')) {
            this.active = true
          }
        })
        .catch(error => {
          this.hideLoader()
          this.setError(error)
        })
      this.loadAssenze(this.authObject)
        .then(enabled => {
          this.loaded = enabled
          if (enabled === true) {
            this.drawAssenze()
          }
        })
        .catch(error => {
          this.setError(error)
        })
        .finally(() => {
          this.hideLoader()
        })
      if (aggiornamenti == true) {
        this.doLoadAggiornamenti()
      }
    },
    doAnnullaPrenotazione(item) {
      MessageBox.confirm(
        this.$t('assenze.conferma_annullamento_richiesta'),
        this.$t('main.confirm'),
        {
          confirmButtonText: this.$t('main.confirm'),
          cancelButtonText: this.$t('main.cancel'),
          type: 'warning'
        }
      )
        .then(() => {
          this.setIdPrenotazioneAnnullata(item.id)
          this.annullaPrenotazione(this.authObject)
            .then(result => {
              if (result['data']['result'] != 'OK') {
                MessageBox.alert(
                  result['data']['message'],
                  this.$t('assenze.titolo_messaggio_errore'),
                  {
                    type: 'error'
                  }
                )
              } else {
                this.doLoadAssenze()
              }
            })
            .catch(error => {
              this.doSetError(error)
            })
        })
        .catch(() => {})
    },
    ...mapActions({
      setOnlyNonGiustificate: 'assenze/setOnlyNonGiustificate',
      setOnlyPresenzeCorso: 'assenze/setOnlyPresenzeCorso',
      loadAssenze: 'assenze/loadAssenze',
      loadOpzioniPrenotazione: 'assenze/loadOpzioniPrenotazione',
      inviaPrenotazione: 'assenze/inviaPrenotazione',
      annullaPrenotazione: 'assenze/annullaPrenotazione',
      setDatiPrenotazione: 'assenze/setDatiPrenotazione',
      setIdPrenotazioneAnnullata: 'assenze/setIdPrenotazioneAnnullata',
      reset: 'assenze/reset'
    })
    // ...mapCacheActions('assenze', ['loadAssenze'])
  },
  mounted() {
    // this._showOnlyNonGiustificate = this.showOnlyNonGiustificate
    this.doLoadAssenze(true)
    // this.validateAuth()
  },
  unmounted() {
    this.reset()
  }
}
</script>

<style lang="scss" scoped>
.local-filters {
  padding: 10px 0;
}
.btn-prenotazione {
  margin: 10px 0;
  &.entrate {
    background-color: #baa9ba;
    border-color: #baa9ba;
  }
  &.uscite {
    background-color: #d6c1ab;
    border-color: #d6c1ab;
  }
}
.prenotazione-dialog {
  .row {
    padding: 5px 0;
    h6 {
      white-space: normal;
      word-break: normal;
      overflow-wrap: normal;
    }
  }
}
</style>
