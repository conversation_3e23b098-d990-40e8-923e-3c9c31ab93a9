<template>
  <div class="mastercom" id="container-comunicazioni">
    <filters
      v-if="loaded"
      :filtra-materie="false"
      :date-range="filtriData"
    ></filters>
    <div v-if="loaded">
      <div class="col-xs-12">
        <el-card class="box-card card">
          <div class="card-content table-list">
            <div class="row table-header">
              <div class="col-xs-2">
                <i class="ti-calendar"></i>
                {{ $t('alternanza.title_data') }}
              </div>
              <div class="col-xs-6">
                <i class="ti-clip"></i>
                {{ $t('alternanza.title_progetto') }}
              </div>
              <div class="col-xs-2">
                <i class="ti-flag-alt"></i>
                {{ $t('alternanza.title_stato') }}
              </div>
            </div>

            <div
              class="row alternanza-list"
              v-for="item in displayItems"
              :key="item.id"
            >
              <div class="col-xs-2 flex-cell" v-if="item.data_inizio">
                <div class="item inner-label">
                  dal:
                </div>
                <div class="item inner-content">
                  {{ item.data_inizio | simpledate }}
                </div>
                <div class="item inner-label" v-if="item.data_fine">
                  al:
                </div>
                <div class="item inner-content" v-if="item.data_fine">
                  {{ item.data_fine | simpledate }}
                </div>
              </div>
              <div class="col-xs-2 flex-cell" v-else>
                <div class="item full-content">
                  {{ $t('alternanza.label_no_date') }}
                </div>
              </div>
              <div class="col-xs-6 flex-cell">
                <div class="item full-content">
                  {{ item.titolo }}
                </div>
                <div class="item full-content">
                  <allegati-list
                    :allegati="item.allegati"
                    :item-id="item.id"
                  ></allegati-list>
                </div>
              </div>
              <div class="col-xs-2 text-center">
                {{ item.stato }}
              </div>
              <div class="col-xs-2 buttons">
                <button
                  class="btn btn-fill btn-info"
                  @click="viewDetails(item)"
                >
                  <span class="btn-label">
                    {{ $t('alternanza.btn_dettagli') }}
                  </span>
                </button>
                <button
                  v-if="item.show_iscrizione"
                  class="btn btn-fill"
                  @click="doSubscribe(item.id_progetto)"
                >
                  <span class="btn-label">
                    {{ $t('alternanza.btn_iscriviti') }}
                  </span>
                </button>
                <button
                  v-if="item.show_cancellazione"
                  class="btn btn-fill btn-danger"
                  @click="doUnsubscribe(item.id_progetto)"
                >
                  <span class="btn-label">
                    {{ $t('alternanza.btn_rimuovi') }}
                  </span>
                </button>
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>
    <div v-else>{{ $t('alternanza.label_no_content') }}</div>
    <el-dialog
      :append-to-body="true"
      :visible.sync="mostraDialogDettaglio"
      top="5vh"
      :width="_dialogWidth()"
      class="prenotazione-dialog"
    >
      <div class="row">
        <div class="col-xs-12 dialog-content" v-html="contenutoDialog"></div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common'
import { Dialog } from 'element-ui'
import Filters from '@/components/Mastercom/UIComponents/Filters'
import { mapGetters, mapActions } from 'vuex'
import AllegatiList from '@/components/Mastercom/UIComponents/AllegatiList'

export default {
  mixins: [Common],
  components: {
    Filters,
    AllegatiList,
    ElDialog: Dialog
  },
  data() {
    return {
      loaded: false,
      mostraDialogDettaglio: false,
      contenutoDialog: ''
    }
  },
  computed: {
    displayItems() {
      let result = this.alternanza.map(x => {
        x.hasAllegati = x.allegati.length > 0
        // if (x.hasAllegati) {
        //   let allegati = []
        //   for (let allegato of x.allegati) {
        //     allegato.allegato = allegato.allegato_webapp
        //     allegati.push(allegato)
        //   }
        // }
        return x
      })

      return result
    },
    ...mapGetters({
      alternanza: 'alternanza/alternanza'
    })
  },
  mounted() {
    this.doClearFiltri().then(() => this.doLoadAlternanza())
  },
  methods: {
    _dialogWidth() {
      return window.outerWidth > 1024 ? '30%' : '100%'
    },
    viewDetails(item) {
      this.contenutoDialog = item.dettaglio
      this.mostraDialogDettaglio = true
      this.tipoPrenotazione = item.tipo
      this.prenotazione = item
    },
    doSubscribe(idProgetto) {
      this.setProgettoIscrizione(idProgetto)
        .then(() => this.iscrizioneAlternanza(this.authObject, idProgetto))
        .then(() => this.doLoadAlternanza())
    },
    doUnsubscribe(idProgetto) {
      this.setProgettoCancellazione(idProgetto)
        .then(() => this.cancellazioneAlternanza(this.authObject, idProgetto))
        .then(() => this.doLoadAlternanza())
    },
    doLoadAlternanza(aggiornamenti = false) {
      this.loaded = false
      this.showLoader()

      this.initScuolaStudente(this.authObject)
        .then(() => {
          this.active = this.isSectionEnabled('alternanza')
        })
        .catch(error => {
          this.hideLoader()
          this.doSetError(error)
        })

      this.loadAlternanza(this.authObject)
        .then(enabled => {
          this.loaded = enabled
        })
        .catch(error => {
          this.doSetError(error)
        })
        .finally(() => {
          this.hideLoader()
        })
      if (aggiornamenti == true) {
        this.doLoadAggiornamenti()
      }
    },
    ...mapActions({
      loadAlternanza: 'alternanza/loadAlternanza',
      setProgettoIscrizione: 'alternanza/setProgettoIscrizione',
      setProgettoCancellazione: 'alternanza/setProgettoCancellazione',
      iscrizioneAlternanza: 'alternanza/iscrizioneAlternanza',
      cancellazioneAlternanza: 'alternanza/cancellazioneAlternanza'
    })
  }
}
</script>
<style lang="scss" scoped>
.alternanza-list {
  border-bottom: 1px solid #e0e0e0;
  padding: 5px 0;
}
.dialog-content {
  word-break: keep-all;
  width: 99.9%;
  overflow: auto;
  /deep/ h1 {
    font-size: 1.8em;
    margin-top: 5px;
    margin-bottom: 50px;
    font-weight: bold;
  }
  /deep/ p {
    &:first-of-type {
      display: none;
    }
    line-height: 1.7em;
  }
}
.buttons {
  padding: 5px;
  text-align: center;
  button {
    width: 120px;
    margin: 0px 5px 5px 0px;
    .btn-label {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      position: relative;
      font-size: 85%;
      i {
        position: absolute;
        left: 0px; /* Regola la distanza dal bordo sinistro */
      }
    }
  }
}
.flex-cell {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  align-items: self-start;
  align-content: flex-start;
  .item {
    padding: 5px;
    font-size: 1em;
    box-sizing: border-box;
    &.inner-label {
      width: calc(20% - 5px);
      text-align: center;
    }
    &.inner-content {
      width: calc(80% - 5px);
      text-align: left;
    }
    &.full-content {
      width: calc(100%);
      text-align: left;
    }
  }
}
</style>
