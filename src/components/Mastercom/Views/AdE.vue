<template>
  <div>
    <div v-if="loading">
      <spinner :radius="51" class="download-spinner"></spinner>
      Caricamento dati
    </div>
    <div class="flex-container" v-else>
      <div class="item-full-width align-left" v-if="showOptPagante">
        <p>
          Ai fini della dichiarazione, chiediamo di confermare i propri dati
          scegliendo tra le opzioni disponibili.
        </p>
      </div>
      <div class="item-full-width align-left" v-if="showOptPagante">
        <h4>1. CONFERMA DEI DATI DEL PAGANTE</h4>
      </div>
      <div
        class="item"
        :class="{
          selected: pagante == 'NO' || opt_pagante == 'NO',
          selectable: pagante == '',
          'not-clickable': pagante != '',
          'not-selected': pagante == 'SI' || opt_pagante == 'SI'
        }"
        @click="pagante == '' ? setOptPagante('NO') : false"
        v-if="showOptPagante"
      >
        i dati del sottoscritto
        <strong>NON</strong>
        corrispondono a quelli del soggetto che ha sostenuto le spese
        <div v-if="pagante == 'NO'" class="data-selezione">
          scelta effettuata {{ data_pagante | fulldate }} alle ore
          {{ data_pagante | simpletime }}
        </div>
      </div>
      <div
        class="item"
        @click="pagante == '' ? setOptPagante('SI') : false"
        v-if="showOptPagante"
        :class="{
          selected: pagante == 'SI' || opt_pagante == 'SI',
          selectable: pagante == '',
          'not-clickable': pagante != '',
          'not-selected': pagante == 'NO' || opt_pagante == 'NO'
        }"
      >
        confermo che sono il pagante delle spese scolastiche
        <div v-if="pagante == 'SI'" class="data-selezione">
          scelta effettuata {{ data_pagante | fulldate }} alle ore
          {{ data_pagante | simpletime }}
        </div>
      </div>
      <div class="item-full-width align-left" v-if="showOptOpposizione">
        <h4>2. OPPOSIZIONE ALL'UTILIZZO DEI DATI</h4>
      </div>
      <div
        class="item"
        v-if="showOptOpposizione"
        @click="opposizione == '' ? setOptOpposizione('SI') : false"
        :class="{
          selected: opposizione == 'SI' || opt_opposizione == 'SI',
          selectable: opposizione == '',
          'not-clickable': opposizione != '',
          'not-selected': opposizione == 'NO' || opt_opposizione == 'NO'
        }"
      >
        chiedo che i dati (spese ed eventuali rimborsi) relativi alle spese
        scolastiche
        <strong>NON</strong>
        vengano utilizzati per la dichiarazione dei redditi precompilata
        relativa all’anno d’imposta 2022.
        <div v-if="opposizione == 'SI'" class="data-selezione">
          scelta effettuata {{ data_opposizione | fulldate }} alle ore
          {{ data_opposizione | simpletime }}
        </div>
      </div>
      <div
        class="item"
        v-if="showOptOpposizione"
        @click="opposizione == '' ? setOptOpposizione('NO') : false"
        :class="{
          selected: opposizione == 'NO' || opt_opposizione == 'NO',
          selectable: opposizione == '',
          'not-clickable': opposizione != '',
          'not-selected': opposizione == 'SI' || opt_opposizione == 'SI'
        }"
      >
        sono consapevole che i dati (spese ed eventuali rimborsi) relativi alle
        spese scolastiche verranno utilizzati per la dichiarazione dei redditi
        precompilata relativa all'anno d’imposta 2022.
        <div v-if="opposizione == 'NO'" class="data-selezione">
          scelta effettuata {{ data_opposizione | fulldate }} alle ore
          {{ data_opposizione | simpletime }}
        </div>
      </div>
      <div class="item-full-width" v-if="showBtnPagante">
        <div v-if="spinner">
          <spinner :radius="51" class="download-spinner"></spinner>
        </div>
        <button @click="doSetPagante()" class="btn btn-fill btn-info" v-else>
          <i class="ti-arrow-right"></i>
          Conferma la scelta e invia i dati alla scuola
        </button>
      </div>
      <div class="item-full-width" v-if="showBtnOpposizione">
        <div v-if="spinner">
          <spinner :radius="51" class="download-spinner"></spinner>
        </div>
        <button
          @click="doSetOpposizione()"
          class="btn btn-fill btn-info"
          v-else
        >
          <i class="ti-arrow-right"></i>
          Conferma la scelta e invia i dati alla scuola
        </button>
      </div>
      <div class="item-full-width" v-if="showProceduraCompletata">
        <p>La procedura è conclusa. I dati sono stati inviati correttamente.</p>
      </div>
      <div class="item-full-width" v-if="showInformazioniUfficio">
        <p>
          La procedura è conclusa. Recarsi in ufficio per maggiori informazioni.
        </p>
      </div>
      <div class="item-full-width" v-if="showBtnReset">
        <div v-if="spinnerReset">
          <spinner :radius="51" class="download-spinner"></spinner>
        </div>
        <button @click="doResetData()" class="btn btn-fill btn-warning" v-else>
          <i class="ti-arrow-right"></i>
          Annulla l'inserimento dei dati e ricomincia
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common.js'
import { Spinner } from 'element-ui'
import { mapGetters, mapActions } from 'vuex'
export default {
  name: 'AdE',
  components: {
    Spinner
  },
  mixins: [Common],
  data() {
    return {
      loading: false,
      spinner: false,
      spinnerReset: false
    }
  },
  computed: {
    showOptPagante() {
      return this.spinnerReset == false
    },
    showOptOpposizione() {
      return this.pagante == 'SI' && this.spinnerReset == false
    },
    showBtnPagante() {
      return (
        this.pagante == '' &&
        this.opt_pagante != null &&
        this.spinnerReset == false
      )
    },
    showBtnOpposizione() {
      return (
        this.pagante == 'SI' &&
        this.opt_opposizione != null &&
        this.opposizione == '' &&
        this.spinnerReset == false
      )
    },
    showBtnReset() {
      return (
        this.pagante == 'NO' || (this.pagante == 'SI' && this.opposizione != '')
      )
    },
    showProceduraCompletata() {
      return (
        this.pagante == 'SI' &&
        this.opposizione != '' &&
        this.spinnerReset == false
      )
    },
    showInformazioniUfficio() {
      return this.pagante == 'NO' && this.spinnerReset == false
    },
    ...mapGetters({
      pagante: 'ade/pagante',
      opposizione: 'ade/opposizione',
      opt_pagante: 'ade/opt_pagante',
      opt_opposizione: 'ade/opt_opposizione',
      data_pagante: 'ade/data_pagante',
      data_opposizione: 'ade/data_opposizione'
    })
  },
  methods: {
    doSetOptPagante(val) {
      this.setOptPagante(val)
    },
    doSetOptOpposizione(val) {
      this.setOptOpposizione(val)
    },
    doResetData() {
      this.setOptPagante('')
      this.setOptOpposizione('')
      this.spinnerReset = true
      this.setPagante(this.authObject)
        .then(() => this.setOpposizione(this.authObject))
        .then(() => this.loadStatoPagante(this.authObject))
        .then(() => this.loadStatoOpposizione(this.authObject))
        .then(() => this.reset())
        .finally(() => {
          this.spinnerReset = false
        })
    },
    doLoadStatoPagante() {
      this.loading = true
      this.loadStatoPagante(this.authObject)
        .then(() => {
          if (this.pagante == 'SI') {
            return this.loadStatoOpposizione(this.authObject)
          }
          if (this.pagante == 'NO') {
            return this.pagante
          }
          return false
        })
        .finally(() => (this.loading = false))
    },
    doSetPagante() {
      this.spinner = true
      this.setPagante(this.authObject)
        .then(() => this.loadStatoPagante(this.authObject))
        .finally(() => (this.spinner = false))
    },
    doSetOpposizione() {
      this.spinner = true
      this.setOpposizione(this.authObject)
        .then(() => this.loadStatoOpposizione(this.authObject))
        .finally(() => (this.spinner = false))
    },
    ...mapActions({
      loadStatoPagante: 'ade/loadPagante',
      loadStatoOpposizione: 'ade/loadOpposizione',
      setPagante: 'ade/postPagante',
      setOpposizione: 'ade/postOpposizione',
      setOptPagante: 'ade/setOptPagante',
      setOptOpposizione: 'ade/setOptOpposizione',
      reset: 'ade/reset'
    })
  },
  mounted() {
    this.reset().then(() => this.doLoadStatoPagante())
  }
}
</script>

<style lang="scss" scoped>
/deep/ .download-spinner {
  &.el-spinner {
    .el-spinner-inner {
      .path {
        stroke: rgba(200, 200, 200, 0.8);
      }
    }
  }
}
.flex-container {
  display: flex;
  flex-wrap: wrap;
  gap: 40px;

  .item {
    border-radius: 6px;
    flex: 1 0 41%;
    padding: 20px;
    color: #333;
    border: 6px solid #aaa;
    background: #fff;
    font-size: 1.3em;
    line-height: 1.5em;
    cursor: pointer;
    &.selected {
      border-color: #06c;
    }
    &.selectable {
      &:hover {
        background: linear-gradient(rgba(207, 207, 207, 0.2), #fff);
        color: #333;
        border: 6px solid #06c;
        strong {
          color: #333;
        }
      }
    }
    &.not-clickable {
      cursor: default;
    }
    &.not-selected {
      color: #bbb;
      border-color: #ddd;
      strong {
        color: #bbb;
      }
    }
    .data-selezione {
      align-self: flex-end;
      padding-top: 5px;
      text-transform: uppercase;
      font-size: 0.7em;
      margin-top: 50px;
      border-top: 1px solid #ccc;
    }
  }
  .item-full-width {
    flex: 1 0 100%;
    text-align: center;
    padding: 20px;
    &.align-left {
      text-align: left;
      padding: 0px;
    }
  }
}
</style>
