<template>
  <div v-if="loaded">
    <filters v-if="loaded"></filters>
    <div class="col-xs-12 mastercom">
      <div v-for="documento in documentiDisplay" :key="documento.id_materia">
        <el-card class="box-card card">
          <div slot="header" class="row">
            <div class="full-relative-width">
              <materia
                :titolo="documento.materia"
                :indice-colore="documento.indice_colore"
              ></materia>
            </div>
          </div>
          <div class="card-content">
            <h5 class="col-xs-2">
              <i class="ti-calendar"></i>
              Data
            </h5>
            <h5 class="col-xs-8">
              <i class="ti-clip"></i>
              {{ $t('materiale.title_descrizione') }}
            </h5>
            <h5 class="col-xs-2">
              <i class="ti-user"></i>
              {{ $t('materiale.title_caricato_da') }}
            </h5>
            <fade-transition group tag="div" :duration="250">
              <div
                v-for="item in documento.documenti"
                :key="item.uid"
                class="row document-list"
              >
                <div class="col-xs-2">
                  {{ item.data | fulldate }}
                </div>
                <div class="col-xs-8">
                  <download-item
                    :url="item.url"
                    :descrizione="item.descrizione"
                  ></download-item>
                </div>
                <div class="col-xs-2">
                  {{ item.autore }}
                </div>
              </div>
            </fade-transition>
          </div>
        </el-card>
      </div>
    </div>
  </div>
  <div v-else>Loading</div>
</template>

<script>
import Vue from 'vue'
import { Card } from 'element-ui'
import Common from '@/components/Mastercom/Mixins/Common'
// import AllegatiList from '@/components/Mastercom/UIComponents/AllegatiList.vue'
import { mapGetters, mapActions } from 'vuex'
import DownloadItem from '@/components/Mastercom/UIComponents/DownloadItem'
import Materia from '@/components/Mastercom/UIComponents/Materia.vue'
import Filters from '@/components/Mastercom/UIComponents/Filters.vue'
import { getMateriaDetails } from '@/utils'
Vue.use(Card)
export default {
  name: 'MaterialeDidattico',
  components: {
    DownloadItem,
    Filters,
    Materia
  },
  mixins: [Common],
  data() {
    return {
      loaded: true,
      materiaSection: 0
    }
  },
  computed: {
    documentiDisplay() {
      let result = []
      let documenti = this.documenti
        .map(x => {
          x.id_materia = parseInt(x.id_materia)
          return x
        })
        .sort((a, b) => {
          return a.id_materia < b.id_materia ? -1 : 1
        })
      if (this.filtriMateria.length) {
        documenti = documenti.filter(a => {
          return this.filtriMateria.includes(a.id_materia)
        })
      }

      if (this.filtriData.length) {
        documenti = documenti.filter(a => {
          return (
            this.$dayjs(a.data) >= this.filtriData[0] &&
            this.$dayjs(a.data) <= this.filtriData[1]
          )
        })
      }

      result = this.materieStudenteCorrente
        .map(x => {
          let id_materia = x.id
          let documentiFiltered = documenti.filter(
            d => d.id_materia === id_materia
          )

          if (documentiFiltered.length > 0) {
            return {
              ...getMateriaDetails(x.id),
              documenti: documentiFiltered
            }
          }
        })
        .filter(x => x !== undefined)
      return result
    },
    ...mapGetters({
      documenti: 'materialeDidattico/all'
    })
  },
  methods: {
    doLoadMaterialeDidattico() {
      this.loaded = false
      this.showLoader()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          return this.isSectionEnabled('materiale_didattico')
        })
        .then(enabled => {
          if (enabled === true) {
            this.loadMaterialeDidattico(this.authObject)
          }
          return enabled
        })
        .then(enabled => {
          this.loaded = enabled
        })
        .catch(error => {
          this.doSetError(error)
        })
        .finally(() => {
          this.hideLoader()
        })
    },
    ...mapActions({
      loadMaterialeDidattico: 'materialeDidattico/loadDocumenti'
    })
  },
  mounted() {
    this.doLoadMaterialeDidattico()
  }
}
</script>

<style lang="scss" scoped>
.document-list {
  text-align: left;
  padding-bottom: 10px;
  padding-top: 10px;
  border-bottom: 1px solid #f8f8f8;
  color: #666666;

  &:nth-of-type(even) {
    background-color: #f8f8f8;
  }
}
</style>
