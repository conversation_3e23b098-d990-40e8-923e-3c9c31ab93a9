<template>
  <div class="wrapper mastercom">
    <div class="row">
      <div class="col-xs-12">
        <colloqui-generali-panel v-if="loaded"></colloqui-generali-panel>
        <div v-else>Loading</div>
      </div>
    </div>
  </div>
</template>
<script>
import Common from '@/components/Mastercom/Mixins/Common.js'
import Colloqui from '@/components/Mastercom/Mixins/Colloqui.js'
import ColloquiGeneraliPanel from '@/components/Mastercom/UIComponents/Panels/ColloquiGeneraliPanel.vue'

export default {
  name: 'ColloquiGenerali',
  mixins: [Common, Colloqui],
  components: {
    ColloquiGeneraliPanel
  },
  data() {
    return {
      loaded: false
    }
  },
  methods: {
    doLoadColloqui(aggiornamenti = false) {
      this.showLoader()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          return this.isSectionEnabled('colloqui_generali')
        })
        .then(enabled => {
          if (enabled === true) {
            this.setType('generali')
            return this.loadColloqui(this.authObject, 'generali')
          }
          return enabled
        })
        .then(enabled => {
          if (enabled === true && this.dateColloquiGenerali.length) {
            this.setDataInizio(this.dateColloquiGenerali[0])
          }
          this.loaded = enabled
          if (aggiornamenti === true) {
            this.doLoadAggiornamenti()
          }
        })
        .catch(error => {
          this.doSetError(error)
        })
        .finally(() => {
          this.hideLoader()
        })
    }
  },
  mounted() {
    this.doLoadColloqui(true)
  }
}
</script>
