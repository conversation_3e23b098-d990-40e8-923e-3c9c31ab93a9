<template>
  <div class="wrapper mastercom">
    <div class="row">
      <div class="col-xs-12">
        <colloqui-panel v-if="loaded"></colloqui-panel>
        <div v-else>Loading</div>
      </div>
    </div>
  </div>
</template>
<script>
import Common from '@/components/Mastercom/Mixins/Common.js'
import Colloqui from '@/components/Mastercom/Mixins/Colloqui.js'
import ColloquiPanel from '@/components/Mastercom/UIComponents/Panels/ColloquiPanel.vue'

export default {
  name: 'Colloqui',
  mixins: [Common, Colloqui],
  components: {
    ColloquiPanel
  },
  data() {
    return {
      loaded: false
    }
  },
  methods: {
    doLoadColloquiSemplici(aggiornamenti = false) {
      this.showLoader()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          return this.isSectionEnabled('colloqui_individuali')
        })
        .then(enabled => {
          if (enabled === true) {
            this.setType('individuali')
            return this.loadColloqui(this.authObject)
          }
          return enabled
        })
        .then(enabled => {
          this.loaded = enabled
          if (aggiornamenti === true) {
            this.doLoadAggiornamenti()
          }
        })
        .catch(error => {
          this.doSetError(error)
        })
        .finally(() => {
          this.hideLoader()
        })
    }
  },
  mounted() {
    this.doLoadColloquiSemplici(true)
  }
}
</script>
