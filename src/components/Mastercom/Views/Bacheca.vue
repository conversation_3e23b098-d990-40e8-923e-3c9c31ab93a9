<template>
  <div class="mastercom" id="container-comunicazioni">
    <filters v-if="loaded" :filtra-materie="false"></filters>
    <messenger-main-panel
      @tab-changed="tabChangeManager"
      @message-sent="messageSentHandler"
      :inviati="false"
      :has-ricevuti="false"
      :has-bacheca="true"
      :invia-messaggi="false"
      :loading="tabLoading"
      v-if="loaded && active"
    ></messenger-main-panel>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common'
import Filters from '@/components/Mastercom/UIComponents/Filters.vue'
import MessengerMainPanel from '@/components/Mastercom/UIComponents/Panels/MessengerMainPanel.vue'

import { mapActions, mapGetters, mapState } from 'vuex'

export default {
  name: 'Bacheca',
  mixins: [Common],
  components: {
    Filters,
    MessengerMainPanel
  },
  computed: {
    hasDateFilter() {
      if (this.comunicazioniDateFilter !== null) {
        if (
          this.comunicazioniDateFilter.data_inizio &&
          this.comunicazioniDateFilter.data_fine
        ) {
          return true
        }
      }
      return false
    },
    ...mapState({
      cDataInizio: state => state.comunicazioni.data_inizio,
      cDataFine: state => state.comunicazioni.data_fine
    }),
    ...mapGetters({
      comunicazioniDateFilter: 'comunicazioni/date_params'
    })
  },
  data() {
    return {
      active: false,
      loaded: false,
      tabLoading: false
    }
  },
  methods: {
    messageSentHandler() {
      return
    },
    tabChangeManager(tabIndex, newTab) {
      if (newTab.id === 'bacheca') {
        this.tabLoading = true
        this.loadBacheca(this.authObject)
          .then(() => {
            this.tabLoading = false
          })
          .catch(error => {
            this.setError(error)
          })
      }
    },
    doLoadBacheca(aggiornamenti = false) {
      // if (!this.hasDateFilter) {
      //   this.setDataInizio(
      //     this.$dayjs(this.filter.last90.data_inizio).format('YYYY-MM-DD')
      //   )
      //   this.setDataFine(
      //     this.$dayjs(this.filter.last90.data_fine).format('YYYY-MM-DD')
      //   )
      //   this.setDateFilter([
      //     this.filter.last90.data_inizio,
      //     this.filter.last90.data_fine
      //   ])
      //   return
      // }

      if (this.blockApiLoading) return false
      this.loaded = false
      this.showLoader()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          if (this.isSectionEnabled('comunicazioni')) {
            this.active = true
          }
        })
        .catch(error => {
          this.hideLoader()
          this.setError(error)
        })
      this.loadBacheca(this.authObject)
        .then(() => {
          this.loaded = true
        })
        .catch(error => {
          this.setError(error)
        })
        .finally(() => {
          this.hideLoader()
        })
      if (aggiornamenti === true) {
        this.doLoadAggiornamenti()
      }
    },
    ...mapActions({
      loadBacheca: 'comunicazioni/loadBachecaThread',
      setDateFilter: 'main/dateFilter',
      setDataInizio: 'comunicazioni/setDataInizio',
      setDataFine: 'comunicazioni/setDataFine',
      reset: 'comunicazioni/reset'
    })
  },
  mounted() {
    this.reset()
    this.doLoadBacheca(true)
  }
}
</script>

<style></style>
