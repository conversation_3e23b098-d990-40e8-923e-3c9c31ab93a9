<template>
  <div class="mastercom" id="container-compiti">
    <filters v-if="loaded && active"></filters>
    <div v-if="loaded && active" class="col-xs-12">
      <div class="local-filters row">
        <div class="col-xs-5">
          <el-switch
            v-model="_showSoloNovita"
            :active-text="$t('main.filter_solo_novita')"
            :inactive-text="$t('compiti.filter_tutti')"
            active-color="#ff3300"
            inactive-color="#696969"
          ></el-switch>
        </div>
        <div class="col-xs-5">
          <el-switch
            v-model="_showAll"
            :active-text="$t('main.filter_vedi_tutti')"
            :inactive-text="$t('compiti.filter_prossimi')"
            active-color="#3388ff"
            inactive-color="#13ce66"
          ></el-switch>
        </div>
      </div>
      <time-line v-if="compitiDisplay.length">
        <time-line-item
          v-for="(item, index) in compitiDisplay"
          class="timeline-inverted"
          :key="index"
          :month-header="
            index > 0
              ? displayMonthHeader(item, compitiDisplay[index - 1])
              : $dayjs(item.data).format('MMMM')
          "
          :badge-text="
            index > 0
              ? displayDataItem(item, compitiDisplay[index - 1])
              : $dayjs(item.data).format('ddd DD/MM')
          "
          :data="$dayjs(item.data).format('DDMMYYYY')"
        >
          <div slot="news" v-if="item.novita">
            <div class="btn btn-danger btn-fill btn-xs" slot="novita">
              <span class="ti-shine"></span>
              {{ $t('main.novita') }}
            </div>
          </div>
          <div slot="header" class="full-relative-width">
            <materia
              :titolo="item.materia"
              :indice-colore="item.indice_colore"
            ></materia>
            <insegnante :nome="item.insegnante"></insegnante>
          </div>

          <h5 slot="title" v-if="item.modulo" v-html="item.modulo"></h5>
          <p slot="body" class="text-primary" v-html="item.titolo"></p>

          <h6 slot="footer">
            <i class="ti-time"></i>
            {{ item.data | simpledate }}
          </h6>
        </time-line-item>
      </time-line>
      <div v-else>
        <div class="card">
          <div class="card-content">
            {{ $t('main.no_content') }}
            <span v-if="descFiltroMateria">{{ descFiltroMateria }}</span>
            <span v-if="descFiltroPeriodo">{{ descFiltroPeriodo }}</span>
          </div>
        </div>
      </div>
    </div>
    <div v-else>Loading</div>
  </div>
</template>

<script>
import TimeLine from '@/components/Mastercom/UIComponents/TimeLine.vue'
import TimeLineItem from '@/components/Mastercom/UIComponents/TimeLineItem3.vue'
import Materia from '@/components/Mastercom/UIComponents/Materia.vue'
import Insegnante from '@/components/Mastercom/UIComponents/Insegnante.vue'
import Filters from '@/components/Mastercom/UIComponents/Filters.vue'
import Common from '@/components/Mastercom/Mixins/Common.js'
import { Switch } from 'element-ui'
import { mapState, mapGetters, mapActions } from 'vuex'
// import {mapCacheActions} from 'vuex-cache'

export default {
  name: 'Compiti',
  mixins: [Common],
  components: {
    TimeLine,
    TimeLineItem,
    Materia,
    Insegnante,
    Filters,
    ElSwitch: Switch
  },
  data() {
    return {
      active: false,
      loaded: false,
      oldBadgeText: '',
      today: this.$dayjs()
    }
  },
  computed: {
    compitiDisplay() {
      let data = this.compiti

      if (this.filtriMateria.length) {
        data = data.filter(a => {
          return this.filtriMateria.includes(a.id_materia)
        })
      }

      if (this.filtriData.length) {
        data = data.filter(a => {
          return (
            this.$dayjs(a.data) >= this.filtriData[0] &&
            this.$dayjs(a.data) <= this.filtriData[1]
          )
        })
      }

      if (this.showSoloNovita) {
        data = data.filter(a => {
          return a.novita
        })
      }

      return data
    },
    firstAvailableDate() {
      let items = this.agenda.filter(a => a.data >= this.today.toISOString())
      let date = items.length > 0 ? items[0].date : this.agenda.last().data
      return this.$dayjs(date)
    },
    firstAvailableCompitoItem() {
      return `date_${this.firstAvailableDate.format('YYYY-MM-DD')}`
    },
    _showSoloNovita: {
      get() {
        return this.showSoloNovita
      },
      set(val) {
        this.setSoloNovita(val)
      }
    },
    _showAll: {
      get() {
        return this.showAll
      },
      set(val) {
        this.setShowAll(val)
      }
    },
    ...mapState({
      showSoloNovita: state => state.compiti.solo_novita,
      showAll: state => state.compiti.show_all
    }),
    ...mapGetters({
      compiti: 'compiti/all'
    })
  },
  methods: {
    doLoadCompiti(aggiornamenti = false) {
      this.setCompitiDataInizio(null)
      this.setCompitiDataFine(null)
      this.loaded = false
      this.showLoader()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          this.active = this.isSectionEnabled('compiti')
        })
        .catch(error => {
          this.hideLoader()
          this.doSetError(error)
        })

      this.loadCompiti(this.authObject)
        .then(enabled => {
          this.loaded = enabled
        })
        .catch(error => {
          this.doSetError(error)
        })
        .finally(() => {
          this.hideLoader()
        })
      if (aggiornamenti == true) {
        this.doLoadAggiornamenti()
      }
    },
    // ...mapCacheActions('compiti', ['loadCompiti'])
    ...mapActions({
      loadCompiti: 'compiti/loadCompiti',
      reset: 'compiti/reset',
      setCompitiDataInizio: 'compiti/setDataInizio',
      setCompitiDataFine: 'compiti/setDataFine',
      setSoloNovita: 'compiti/setSoloNovita',
      setShowAll: 'compiti/setShowAll'
    })
  },
  mounted() {
    this.doClearFiltri().then(this.doLoadCompiti(true))
  },
  unmounted() {
    this.reset()
  }
}
</script>

<style scoped lang="scss">
.local-filters {
  padding: 10px;
}
</style>
