<template>
  <div>
    <api-error-handler></api-error-handler>
    <div class="wrapper wrapper-full-page" v-if="pageVisible">
      <div
        class="full-page login-page mastercom-login-page"
        data-color=""
        data-image="static/img/background/background-2.jpg"
      >
        <!--   you can change the color of the filter page using: data-color="blue | azure | green | orange | red | purple" -->
        <div class="content">
          <div class="container">
            <div class="row">
              <div class="col-md-4 col-sm-6 col-md-offset-4 col-sm-offset-3">
                <form method="#" action="#">
                  <div
                    class="card"
                    data-background="color"
                    data-color="blue"
                    v-loading="loading"
                  >
                    <div class="card-header">
                      <img :src="logo" @error="logoAlt" alt="logo mastercom" />
                      <h3 class="card-title"></h3>
                    </div>
                    <div class="card-content" v-if="showLogin === false">
                      <h3 class="card-title text-center">
                        Sit<PERSON> in aggiornamento
                      </h3>
                      <h5 class="card-title text-center">
                        Riprovare l'accesso più tardi
                      </h5>
                    </div>
                    <div class="card-content" v-if="showLogin === true">
                      <h4 v-html="scuola"></h4>
                    </div>
                    <div class="card-content" v-if="showLogin === true">
                      <el-alert
                        v-show="showErrorMsg"
                        type="error"
                        :description="errorMsg"
                        show-icon
                        @close="resetErrorMsg"
                      ></el-alert>
                      <div v-if="loginRedirect === true">
                        <h5>
                          <spinner
                            :radius="51"
                            class="download-spinner"
                          ></spinner>
                          {{ $t('login.ongoing_auth') }}
                        </h5>
                      </div>
                      <div v-else>
                        <div v-if="loginUsernamePassword">
                          <h6 role="heading" aria-level="1">
                            {{ $t('login.title') }}
                          </h6>
                          <div class="form-group">
                            <label for="utente" class="capital">
                              {{ utenteLabel }}
                            </label>
                            <input
                              name="utente"
                              id="utente"
                              v-model="utente"
                              :placeholder="utentePlaceholder"
                              type="text"
                              class="form-control input-no-border"
                            />
                          </div>
                          <div class="form-group password">
                            <label for="password" class="capital">
                              {{ passwordLabel }}
                            </label>
                            <input
                              name="password"
                              :type="passwordFieldType"
                              :placeholder="passwordPlaceholder"
                              class="form-control input-no-border"
                              id="password"
                              v-model="password"
                            />
                            <button
                              aria-label="mostra o nascondi password"
                              type="button"
                              class="btn btn-icon btn-simple btn-lg"
                              @click="switchPasswordFieldType"
                            >
                              <i
                                class="ti-eye"
                                :class="passwordFieldTypeClass"
                              ></i>
                            </button>
                          </div>
                        </div>
                        <div v-else>
                          {{ disclaimerLoginUsernamePassword }}
                        </div>
                      </div>
                    </div>
                    <div class="card-footer text-center">
                      <button
                        type="submit"
                        class="btn btn-fill btn-wd capital btn-login"
                        @click.stop.prevent="login"
                        v-if="
                          showLogin === true &&
                            loginRedirect === false &&
                            loginUsernamePassword === true
                        "
                      >
                        <span class="btn-login-icon"></span>
                        <span>
                          {{ submitLabel }}
                        </span>
                      </button>
                    </div>
                    <div
                      class="card-footer text-center capital"
                      v-if="
                        showLogin === true &&
                          loginRedirect === false &&
                          loginUsernamePassword === true
                      "
                    >
                      <a
                        @click.stop.prevent="checkPasswordRecovery"
                        target="_blank"
                        class="link"
                      >
                        {{ $t('login.label_lost_password') }}
                      </a>
                    </div>
                    <div
                      class="card-footer text-center capital card-spid-login"
                      v-if="
                        loginSPID === true &&
                          loginRedirect === false &&
                          showLogin === true
                      "
                    >
                      <h6 v-if="loginUsernamePassword === true">
                        {{ $t('main.label_or') }}
                      </h6>
                      <a
                        :href="url_finale_spid"
                        class="italia-it-button italia-it-button-size-sm button-spid"
                      >
                        <span class="italia-it-button-icon">
                          <img
                            src="/static/img/spid-sp-access-button-1.6/src/production/img/spid-ico-circle-bb.svg"
                            onerror="this.src='/static/img/spid-sp-access-button-1.6/src/production/img/spid-ico-circle-bb.png'; this.onerror=null;"
                            alt=""
                          />
                        </span>
                        <span class="italia-it-button-text">
                          {{ $t('login.label_SPID') }}
                        </span>
                      </a>
                    </div>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>

        <footer class="footer footer-transparent">
          <div class="container">
            <div class="copyright">
              MasterCom portale famiglie ver {{ appVersion }}
            </div>
          </div>
          <cookie-law></cookie-law>
        </footer>
        <div
          class="full-page-background"
          style="background-image: url(/static/img/background/bg-registro.jpg)"
        ></div>
      </div>
    </div>
    <div v-else>
      <div class="white-bg">
        <spinner :radius="51" class="download-spinner"></spinner>
        <h3 class="text-center">
          {{ $t('login.ongoing_auth') }}
        </h3>
      </div>
    </div>
  </div>
</template>
<script>
import Vue from 'vue'

import { Events } from '@/utils'
import { Alert, Loading, Notification, Spinner } from 'element-ui'

import { mapState, mapActions, mapGetters } from 'vuex'
import ApiErrorHandler from '@/components/Mastercom/UIComponents/ApiErrorHandler.vue'
import CookieLaw from '@/components/Mastercom/UIComponents/CookieLaw.vue'
import Common from '@/components/Mastercom/Mixins/Common.js'

Vue.use(Alert)
Vue.use(Loading)
export default {
  components: {
    ApiErrorHandler,
    CookieLaw,
    Spinner
  },
  mixins: [Common],
  data() {
    return {
      pageVisible: true,
      showLogin: null,
      errorMsg: '',
      utente: '',
      password: '',
      thirdLevel: '',
      loading: false,
      mastercom_id: '',
      passwordFieldType: 'password',
      passwordFieldTypeClass: '',
      version: '',
      logo: '',
      default_logo: '/static/img/mastercom_logo.png',
      notificationTime: 2500,
      notificationPosition: 'bottom-right',
      loginErrorMsg: '',
      token: null,
      loginRedirect: false,
      redirectTo: '/',
      idStudente: null,
      annoScolastico: null
    }
  },
  computed: {
    // isAuthenticated () {
    //   return this.$store.getters['auth/isAuthenticated']
    // },
    showErrorMsg() {
      return this.errorMsg !== ''
    },
    _mastercom_id() {
      return process.env.MASTERCOM_ID
    },
    recoverPasswordUrl() {
      let url = this.urlScuola
        ? this.urlScuola
        : `https://${this.mastercom_id}.registroelettronico.com`

      return `${url}/preiscrizioni/recover_password.html`
    },
    url_finale_spid() {
      let app =
        window.location.host.indexOf('sito-dev.') > 0
          ? 'sito-genitori-dev'
          : 'sito-genitori'

      let state = JSON.stringify({
        app: app,
        auth_url: this.spid.auth_url,
        id: this.spid.mastercom_id
      })

      return `${this.spid.url}?client_id=${
        this.spid.client_id
      }&redirect_uri=${encodeURI(this.spid.redirect_uri)}&state=${encodeURI(
        state
      )}&aggregate_ref_type=${
        this.spid.aggregate_ref_type
      }&aggregate_ref_value=${this.spid.aggregate_ref_value}`
    },
    ...mapGetters({
      appVersion: 'app/appVersion'
    }),
    ...mapState({
      scuola: state => state.main.nomeScuola,
      recuperaPasswordEnabled: state => state.main.recuperaPasswordEnabled,
      user: state => state.auth.user,
      urlScuola: state => state.main.urlScuola,
      loginSPID: state => state.main.loginSPID,
      loginUsernamePassword: state => state.main.loginUsernamePassword,
      disclaimerLoginUsernamePassword: state =>
        state.main.disclaimerLoginUsernamePassword,
      spid: state => state.main.spid
    })
  },
  props: {
    titolo: {
      type: String,
      default: 'Sito Genitori'
    },
    // utente: {
    //   type: String,
    //   default: ''
    // },
    // password: {
    //   type: String,
    //   default: ''
    // },
    utenteLabel: {
      type: String,
      default: function() {
        return this.$i18n.t('login.label_username')
      }
    },
    utentePlaceholder: {
      type: String,
      default: function() {
        return this.$i18n.t('login.placeholder_username')
      }
    },
    passwordLabel: {
      type: String,
      default: function() {
        return this.$i18n.t('login.label_password')
      }
    },
    passwordPlaceholder: {
      type: String,
      default: function() {
        return this.$i18n.t('login.placeholder_password')
      }
    },
    submitLabel: {
      type: String,
      default: function() {
        return this.$i18n.t('login.label_button_submit')
      }
    },
    idSCUOLA: {
      type: String,
      default: 'ID SCUOLA'
    }
  },
  created() {
    this.mastercom_id = process.env.VUE_APP_MASTERCOM_ID || window.MASTERCOM_ID

    this.logo = this.default_logo
  },
  mounted() {
    this.version = process.env.VUE_APP_VERSION
    if (window.location.hostname.split('.').length > 1) {
      this.thirdLevel = window.location.hostname.split('.')[0]
    } else {
      this.thirdLevel = 'demo4'
    }
    if ('token' in this.$route.query) {
      this.token = this.$route.query.token
      window.history.replaceState({}, document.title, this.$route.path)
    }

    if ('redirect_to' in this.$route.query) {
      this.redirectTo = `/${this.$route.query.redirect_to}`
      this.pageVisible = false
    }

    if ('id_studente' in this.$route.query) {
      this.idStudente = this.$route.query.id_studente
    }
    if ('spid_login_error' in this.$route.query) {
      if (this.$route.query.spid_login_error == 'utente_non_trovato') {
        this.errorMsg = this.$i18n.t('login.warning.spid_login_not_found')
      }
      if (this.$route.query.spid_login_error == 'utente_multiplo') {
        this.errorMsg = this.$i18n.t('login.warning.spid_login_duplicate')
      }
      window.history.replaceState({}, document.title, this.$route.path)
    }

    this.doLoginInit()

    // Events.$on('failedAuth', msg => {
    //   this.loading = false
    //   this.errorMsg = i18n.t(msg.response.data.detail)
    // })
  },
  beforeDestroy() {
    this.closeMenu()
    Events.$off('failedAuth')
  },
  methods: {
    resetErrorMsg() {
      this.errorMsg = ''
    },
    logoAlt(event) {
      event.target.src = this.default_logo
    },
    switchPasswordFieldType() {
      if (this.passwordFieldType === 'password') {
        this.passwordFieldType = 'text'
        this.passwordFieldTypeClass = 'opaque'
      } else {
        this.passwordFieldType = 'password'
        this.passwordFieldTypeClass = ''
      }
    },
    toggleNavbar() {
      document.body.classList.toggle('nav-open')
    },
    closeMenu() {
      document.body.classList.remove('nav-open')
      document.body.classList.remove('off-canvas-sidebar')
    },
    doLoginInit() {
      this.showLogin = null
      this.loginInit({ mastercom: this.mastercom_id })
        .then(() => {
          this.showLogin = true
        })
        .catch(() => {
          this.showLogin = false
        })

      if (this.token) {
        this.loginRedirect = true
        this.login()
      }
    },
    checkPasswordRecovery() {
      this.loginInit({ mastercom: this.mastercom_id }).then(() => {
        if (this.recuperaPasswordEnabled === true) {
          window.location = this.recoverPasswordUrl
        } else {
          Notification.warning({
            title: 'Recupera Password',
            message:
              'la scuola ha temporaneamente disabilitato questa funzione',
            duration: this.notificationTime,
            position: this.notificationPosition
          })
        }
      })
    },
    login() {
      this.errorMsg = ''
      if ((!this.utente || !this.password) && !this.token) {
        this.errorMsg = this.$i18n.t('login.warning.required_fields')
        return
      }
      this.loading = true
      this.loginErrorMsg = this.$i18n.t('login.warning.login_failed')
      let loginPayload = {
        mastercom: this.mastercom_id
      }
      if (this.token) {
        loginPayload.token = this.token
      } else {
        loginPayload.utente = this.utente
        loginPayload.password = this.password
      }
      this.authLogin(loginPayload)
        .then(() => {
          this.loginErrorMsg = this.$i18n.t('login.warning.school_config_error')
          // Notification.success({
          //   title: 'Credenziali',
          //   message: 'Le credenziali di accesso sono valide',
          //   duration: this.notificationTime,
          //   position: this.notificationPosition
          // })
          if (this.idStudente) {
            const studente = this.user.studenti.find(
              s => String(s.id) === String(this.idStudente)
            )
            if (!studente) {
              throw this.$i18n.t('login.warning.student_not_found')
            }
            this.initStudente(studente)
          } else {
            return this.init(this.user)
          }
        })
        .then(() => {
          this.loginErrorMsg = this.$i18n.t(
            'login.warning.student_config_error'
          )
          // Notification.success({
          //   title: 'App setup',
          //   message: 'ok',
          //   duration: this.notificationTime,
          //   position: this.notificationPosition
          // })
          return this.initScuolaStudente(this.authObject)
        })
        .then(() => {
          this.loginErrorMsg = this.$i18n.t(
            'login.warning.school_updates_error'
          )
          // Notification.success({
          //   title: 'Caricamento dati studente',
          //   message: 'ok',
          //   duration: this.notificationTime,
          //   position: this.notificationPosition
          // })
          return this.loadMaterie(this.authObject)
        })
        .then(() => {
          // Notification.success({
          //   title: 'Caricamento aggiornamenti',
          //   message: 'ok',
          //   duration: this.notificationTime,
          //   position: this.notificationPosition
          // })
          this.loading = false
          this.$router.push(this.redirectTo)
        })
        .catch(error => {
          this.loading = false
          this.loginRedirect = false
          this.errorMsg = error
        })
    },
    ...mapActions({
      authLogin: 'auth/login',
      loginInit: 'main/loginInit',
      init: 'main/init',
      initStudente: 'main/initStudente',
      loadAggiornamenti: 'aggiornamenti/loadAggiornamenti'
      // init: 'main/init',
      // authenticate: 'auth/authenticate'
    })
  }
}
</script>
<style scoped lang="scss">
@import url('/static/css/italia.css');
.login-page {
  .content {
    padding-top: 10vh;
    .password {
      position: relative;
      button {
        position: absolute;
        right: 0;
        top: 20px;
        i {
          opacity: 0.6;
          &.opaque {
            opacity: 1;
          }
        }
      }
    }
  }
  .link {
    cursor: pointer;
  }
  .capital {
    text-transform: lowercase;
    &:first-letter {
      text-transform: uppercase;
    }
  }
  .btn-login {
    border-radius: 6px;
    width: 170px;
  }
  .card-spid-login {
    border-top: 1px dashed #ccc;
    padding-top: 20px;
    background-color: #f8f8f8;
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
  }
  .white-bg {
    background-color: #fff;
    height: 100vh;
    width: 100vw;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 9999;
  }
}
</style>
