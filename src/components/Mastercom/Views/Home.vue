<template>
  <div class="wrapper mastercom">
    <div class="row" v-show="loaded">
      <div class="col-xs-12" v-if="showAEStatus">
        <ade-panel></ade-panel>
      </div>
      <empty-panel v-if="nessunContenuto" class="col-xs-12">
        <template v-slot:header>
          <h3>{{ studenteCorrente.nome }}</h3>
        </template>
        <template v-slot:content>
          <div class="row">
            <div class="col-xs-12">
              A.S. {{ studenteCorrente.anno_corrente_display }}
            </div>
            <div class="col-xs-12">CLASSE {{ studenteCorrente.classe }}</div>
          </div>
        </template>
      </empty-panel>
      <div
        class="col-xs-12 col-sm-6 col-lg-3"
        v-if="studenteCorrente.servizi.assenze"
      >
        <assenze-panel></assenze-panel>
      </div>
      <div
        class="col-xs-12 col-sm-6 col-lg-3"
        v-if="studenteCorrente.servizi.comunicazioni"
      >
        <comunicazioni-non-lette-panel></comunicazioni-non-lette-panel>
      </div>
      <div
        class="col-xs-12 col-sm-6 col-lg-3"
        v-if="studenteCorrente.servizi.compiti"
      >
        <compiti-panel></compiti-panel>
      </div>
      <div
        class="col-xs-12 col-sm-6 col-lg-3"
        v-if="studenteCorrente.servizi.note"
      >
        <note-disciplinari-panel></note-disciplinari-panel>
      </div>
    </div>
    <div class="row" v-show="loaded">
      <div class="col-xs-12 col-lg-12">
        <orario-panel v-if="mostraOrario"></orario-panel>
      </div>
      <div class="col-xs-12 col-lg-12">
        <andamento-panel></andamento-panel>
      </div>
    </div>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common.js'

import OrarioPanel from '@/components/Mastercom/UIComponents/Panels/OrarioPanel.vue'
// import MensePanel from '@/components/Mastercom/UIComponents/Panels/MensePanel.vue'
import CompitiPanel from '@/components/Mastercom/UIComponents/Panels/CompitiPanel.vue'
import NoteDisciplinariPanel from '@/components/Mastercom/UIComponents/Panels/NoteDisciplinariPanel.vue'
import ComunicazioniNonLettePanel from '@/components/Mastercom/UIComponents/Panels/ComunicazioniNonLettePanel.vue'
import AssenzePanel from '@/components/Mastercom/UIComponents/Panels/AssenzePanel.vue'
import AndamentoPanel from '@/components/Mastercom/UIComponents/Panels/AndamentoPanel.vue'
import EmptyPanel from '@/components/Mastercom/UIComponents/Panels/EmptyPanel.vue'
import AdePanel from '@/components/Mastercom/UIComponents/Panels/AdePanel.vue'

import { mapState, mapGetters, mapActions } from 'vuex'

export default {
  name: 'Home',
  mixins: [Common],
  components: {
    OrarioPanel,
    AssenzePanel,
    AndamentoPanel,
    CompitiPanel,
    NoteDisciplinariPanel,
    ComunicazioniNonLettePanel,
    EmptyPanel,
    AdePanel
  },
  data() {
    return {
      loaded: true,
      showAdeButton: false
    }
  },
  computed: {
    nessunContenuto() {
      return (
        this.studenteCorrente.servizi.assenze === false &&
        this.studenteCorrente.servizi.comunicazioni === false &&
        this.studenteCorrente.servizi.note === false &&
        this.studenteCorrente.servizi.compiti === false &&
        this.mostraOrario === false
      )
    },
    ...mapState({
      voti: state => state.voti.all,
      argomenti: state => state.argomenti.all
    }),
    ...mapGetters({
      mostraOrario: 'main/mostraOrario',
      showAEStatus: 'ade/AEStatus'
    })
  },
  methods: {
    goAde() {
      this.$router.push('/agenzia-entrate')
    },
    doLoadHomeContent() {
      this.loadAggiornamenti(this.authObject)
      this.loadVoti(this.authObject).catch(error => {
        this.setError(error)
      })

      this.loadArgomenti(this.authObject).catch(error => {
        this.setError(error)
      })
      this.loadAde(this.authObject)
    },
    ...mapActions({
      loadArgomenti: 'argomenti/loadArgomenti',
      loadVoti: 'voti/loadVoti',
      loadAde: 'ade/loadStatus'
    })
  },
  mounted() {
    this.doLoadHomeContent()
    // this.validateAuth()
  }
}
</script>

<style></style>
