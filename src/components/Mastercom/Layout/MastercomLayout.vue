<template>
  <div class="wrapper">
    <side-bar
      type="sidebar"
      :sidebar-links="mastercomLinks"
      active-color="warning"
      :title="nomeScuola"
    >
      <user-menu></user-menu>
      <student-menu></student-menu>
      <div v-show="false">
        <form
          class="navbar-form navbar-left navbar-search-form navbar-search-form-mobile"
          role="search"
        >
          <div class="input-group">
            <span class="input-group-addon"><i class="fa fa-search"></i></span>
            <input
              type="text"
              value=""
              class="form-control"
              placeholder="Search..."
            />
          </div>
        </form>
      </div>
      <mobile-menu v-if="false"></mobile-menu>
    </side-bar>

    <div class="main-panel" id="stage">
      <api-error-handler></api-error-handler>
      <top-navbar></top-navbar>
      <dashboard-content @click.native="toggleSidebar"></dashboard-content>
      <content-footer></content-footer>
    </div>
    <change-password></change-password>
  </div>
</template>
<script>
import DashboardLayout from '@/components/Dashboard/Layout/DashboardLayout.vue'
import ApiErrorHandler from '@/components/Mastercom/UIComponents/ApiErrorHandler.vue'
import Common from '@/components/Mastercom/Mixins/Common.js'
import TopNavbar from './TopNavbar.vue'
import ChangePassword from '@/components/Mastercom/UIComponents/ChangePassword.vue'
import ContentFooter from '@/components/Mastercom/UIComponents/ContentFooter.vue'

// const intervalDuration = 5 * 60 * 1000
// const intervalDuration = 1 * 60 * 1000

export default {
  name: 'MastercomLayout',
  mixins: [DashboardLayout, Common],
  data() {
    return {
      loginValidator: null
    }
  },
  components: {
    TopNavbar,
    ApiErrorHandler,
    ChangePassword,
    ContentFooter
  },
  created() {
    /*
    this.loginValidator = setInterval(
      this.validateAuth.then(() => {
        this.init(this.user)
      }),
      intervalDuration
    )
    */
    // this.loginValidator = setInterval(() => {
    //   this.refreshAuth().then(response => {
    //     return this.refreshStudente(response)
    //   })
    // }, intervalDuration)
  },
  beforeDestroy() {
    clearInterval(this.loginValidator)
  }
}
</script>

<style></style>
