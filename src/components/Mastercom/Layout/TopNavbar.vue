<template>
  <nav class="navbar navbar-default">
    <div class="container-fluid">
      <div class="navbar-minimize">
        <button class="btn btn-fill btn-icon" @click="minimizeSidebar">
          <i :class="$sidebar.isMinimized ? 'ti-menu-alt' : 'ti-more-alt'"></i>
        </button>
      </div>
      <div class="navbar-header">
        <button
          type="button"
          class="navbar-toggle"
          :class="{ toggled: $sidebar.showSidebar }"
          @click="toggleSidebar"
        >
          <span class="sr-only">Toggle navigation</span>
          <span class="icon-bar bar1"></span>
          <span class="icon-bar bar2"></span>
          <span class="icon-bar bar3"></span>
        </button>
        <a class="navbar-brand">{{ this.$route.name }}</a>
      </div>
      <div class="collapse navbar-collapse">
        <ul class="nav navbar-nav navbar-right">
          <li class="open">
            <router-link
              to="/admin/stats"
              class="dropdown-toggle btn-magnify"
              data-toggle="dropdown"
              v-if="false"
            >
              <i class="ti-panel"></i>
              <p>Stats</p>
            </router-link>
          </li>
          <drop-down tag="li" title="5" icon="ti-bell" v-if="false">
            <li><a href="#">Notification 1</a></li>
            <li><a href="#">Notification 2</a></li>
            <li><a href="#">Notification 3</a></li>
            <li><a href="#">Notification 4</a></li>
            <li><a href="#">Another notification</a></li>
          </drop-down>
          <drop-down
            tag="li"
            :title="userName"
            icon="ti-crown"
            :rotate="false"
            v-if="false"
          >
            <li v-if="changePasswordEnabled">
              <a href="#" @click.stop="doShowChangePasswordWindow()">
                Modifica password
              </a>
            </li>
            <li><a href="#" @click.stop="doLogout">Logout</a></li>
          </drop-down>
        </ul>
      </div>
    </div>
  </nav>
</template>

<script>
import TopNavbar from '@/components/Dashboard/Layout/TopNavbar.vue'
import Common from '@/components/Mastercom/Mixins/Common'

import { mapState, mapGetters } from 'vuex'

export default {
  name: 'TopNavbar',
  mixins: [TopNavbar, Common],
  computed: {
    ...mapState({
      nome_utente: state => state.auth.user.nome,
      cognome_utente: state => state.auth.user.cognome
    }),
    ...mapGetters({
      userName: 'auth/userName'
    })
  },
  data() {
    return {
      changePasswordEnabled: true
    }
  }
  // methods: {
  //   doLogout () {
  //     this.logout()
  //       .then((response) => {
  //         this.$router.push('/login')
  //       })
  //   },
  //   ...mapActions({
  //     logout: 'auth/logout'
  //   })
  // }
}
</script>

<style lang="scss" scoped>
/*
  .navbar {
    .navbar-collapse {
      &.collapse {
        @media (max-width: 992px) {
          display: block !important;

        }
      }
    /deep/ .dropdown {
      @media (max-width: 992px) {
        max-width: 60px;
      .dropdown-toggle {

        p {
          display: none;
        }
      }
      }
    }

    }

  }
  */
</style>
