import { Loading, MessageBox } from 'element-ui'
import { mapState, mapGetters, mapActions } from 'vuex'

export default {
  name: 'Common',
  render: false,
  computed: {
    urlBase() {
      return `/v3/scuole/${this.studenteCorrente.id_scuola}/studenti/${
        this.studenteCorrente.id
      }/${this.studenteCorrente.anno_corrente}`
    },
    descFiltroMateria() {
      let result = null
      if (this.filtriMateria.length) {
        result = []
        for (let m of this.filtriMateria) {
          result.push(`${this.getNomeMateria(m)}`)
        }
        result = ` per ${result.join(', ')}`
      }
      return result
    },
    descFiltroPeriodo() {
      let result = null
      if (this.filtriData.length) {
        let periodo = this.periodiScolastici.filter(p => {
          let a = this.$dayjs(p.data_inizio).format('YYYY-MM-DD')
          let b = this.$dayjs(p.data_fine).format('YYYY-MM-DD')
          return a === this.filtriData[0] && b === this.filtriData[1]
        })
        if (periodo.length) {
          result = [...periodo].pop().nome
        } else {
          result = `periodo da ${this.$dayjs(this.filtriData[0]).format(
            'DD-MM-YYYY'
          )} a ${this.$dayjs(this.filtriData[1]).format('DD-MM-YYYY')}`
        }
      }
      return result
    },
    filtriData() {
      return this._filtriData.map(x => {
        x = this.$dayjs(x)
        return x
      })
    },
    ...mapState({
      studenteCorrente: state => state.main.studenteCorrente,
      annoCorrente: state => state.main.annoCorrente,
      periodoCorrente: state => state.main.periodoCorrente,
      nomeScuola: state => state.main.nomeScuola,
      jwtToken: state => state.auth.jwt,
      urlScuola: state => state.auth.urlScuola,
      filtriMateria: state => state.materie.materieFilter,
      _filtriData: state => state.main.dateFilter,
      _periodiScolastici: state => state.main._periodiScolastici,
      dataInizioAnno: state => state.main.dataInizioAnno,
      dataFineAnno: state => state.main.dataFineAnno,
      user: state => state.auth.user,
      showChangePassword: state => state.auth.showChangePassword
    }),
    ...mapGetters({
      materieStudenteCorrente: 'materie/materieStudenteCorrente',
      mastercomLinks: 'main/sidebarLinks',
      mastercomAuthObject: 'main/authObject',
      periodiScolastici: 'main/periodiScolastici',
      dateFestivita: 'main/dateFestivita',
      dateAttivita: 'main/dateAttivita'
    }),
    authObject() {
      return {
        studente: this.studenteCorrente,
        anno: this.annoCorrente,
        token: this.jwtToken
      }
    }
  },
  data() {
    return {
      showDialogCC: false,
      sectionDisabledMessage:
        'Questa sezione è stata temporanemante disabilitata da parte della scuola',
      msgBoxProperties: {
        type: 'warning',
        customClass: 'mastercom-msg-box'
      },
      CommonLabelMesi: [
        'Set',
        'Ott',
        'Nov',
        'Dic',
        'Gen',
        'Feb',
        'Mar',
        'Apr',
        'Mag',
        'Giu'
      ],
      today: this.$dayjs(),
      fsLoader: null,
      aggiornamentiIsLoading: false,
      filter: {
        today: {
          data_inizio: this.$dayjs().format('YYYY-MM-DD'),
          data_fine: this.$dayjs().format('YYYY-MM-DD')
        },
        tomorrow: {
          data_inizio: this.$dayjs()
            .add(1, 'days')
            .format('YYYY-MM-DD'),
          data_fine: this.$dayjs()
            .add(1, 'days')
            .format('YYYY-MM-DD')
        },
        last30: {
          data_inizio: this.$dayjs()
            .subtract(30, 'days')
            .format('YYYY-MM-DD'),
          data_fine: this.$dayjs().format('YYYY-MM-DD')
        },
        last90: {
          data_inizio: this.$dayjs()
            .subtract(90, 'days')
            .format('YYYY-MM-DD'),
          data_fine: this.$dayjs().format('YYYY-MM-DD')
        }
      },
      blockApiLoading: false
    }
  },
  methods: {
    doShowChangePasswordWindow() {
      this.showChangePasswordWindow(true)
    },
    doHideChangePasswordWindow() {
      this.showChangePasswordWindow(false)
    },
    isObjectEmpty(obj) {
      for (var key in obj) {
        if (obj.hasOwnProperty(key)) return false
      }
      return true
    },
    dataAnnoScolastico(d) {
      let data = this.$dayjs(d)
      return (
        data.isAfter(this.$dayjs(this.dataInizioAnno).subtract(1, 'day')) &&
        data.isBefore(this.$dayjs(this.dataFineAnno).add(1, 'day'))
      )
    },
    hex2rgba(hex, opacity = 1) {
      let r = parseInt(hex.substring(1, 3), 16)
      let g = parseInt(hex.substring(3, 5), 16)
      let b = parseInt(hex.substring(5, 7), 16)
      return `rgba(${r}, ${g}, ${b}, ${opacity})`
    },
    showLoader() {
      if (this.fsLoader === null) {
        this.fsLoader = Loading.service({
          fullscreen: false,
          target: document.getElementById('stage'),
          // background: 'rgba(60, 60, 60, 0.4)'
          background: '#f4f3ef'
        })
      }
    },
    hideLoader() {
      if (this.fsLoader !== null) {
        this.fsLoader.close()
        this.fsLoader = null
      }
    },
    doValidateUser() {},
    doReset() {
      this.doHideChangePasswordWindow()
      this.resetVoti()
      this.resetArgomenti()
      this.resetCompiti()
      this.resetNoteDisciplinari()
      this.resetAnnotazioni()
      this.resetPagelle()
      this.resetComunicazioni()
      this.resetAssenze()
      this.resetAgenda()
      this.resetMain()
      this.resetMaterie()
    },
    doLogout() {
      this.blockApiLoading = true
      this.logout()
        .then(() => {
          return this.doReset()
        })
        .then(() => {
          return this.clearError()
        })
        .then(() => {
          this.$router.push('/login')
        })
        .finally(() => {
          this.blockApiLoading = false
        })
    },
    doSetError(error) {
      this.doReset()
      this.setError(error)
    },
    // doLoadMaterie() {
    //   this.loadMaterie(this.authObject)
    // },
    async doClearFiltri() {
      await this.clearFiltroMaterie()
      await this.clearFiltroDate()
    },
    displayMonthHeader(a, b) {
      let month1 = this.$dayjs(a.data).format('MMMM')
      if (typeof b.data === 'undefined') {
        return month1
      }
      let month2 = this.$dayjs(b.data).format('MMMM')
      if (month1 !== month2) {
        return month1
      }
      return ''
    },
    displayDataItem(a, b) {
      let data1 = this.$dayjs(a.data).format('ddd DD/MM')
      if (typeof b.data === 'undefined') {
        return data1
      }
      let data2 = this.$dayjs(b.data).format('ddd DD/MM')
      if (data1 !== data2) {
        return data1
      }
      return ''
    },
    doLoadAggiornamenti() {
      if (!this.aggiornamentiIsLoading) {
        this.aggiornamentiIsLoading = true
        this.loadAggiornamenti(this.authObject).then(() => {
          this.aggiornamentiIsLoading = false
        })
      }
    },
    getNomeMateria(id) {
      let result = this.materieStudenteCorrente.filter(m => {
        return m.id === id
      })
      return [...result].pop()['label']
    },
    isSectionEnabled(section) {
      if (this.studenteCorrente.servizi[section] === false) {
        MessageBox.alert(this.sectionDisabledMessage, {
          ...this.msgBoxProperties
        }).then(() => {
          this.$router.push('Home')
        })
        return false
      }
      return true
    },
    ...mapActions({
      logout: 'auth/logout',
      showChangePasswordWindow: 'auth/showChangePasswordWindow',
      validateAuth: 'auth/validateAuth',
      refreshAuth: 'auth/refreshAuth',
      loadMaterie: 'materie/load',
      init: 'main/init',
      initScuolaStudente: 'main/initScuolaStudente',
      setError: 'apiErrorHandler/setError',
      clearError: 'apiErrorHandler/clearError',
      clearFiltroMaterie: 'materie/clearFilter',
      clearFiltroDate: 'main/clearDateFilter',
      loadAggiornamenti: 'aggiornamenti/loadAggiornamenti',
      resetMaterie: 'materie/reset',
      resetVoti: 'voti/reset',
      resetArgomenti: 'argomenti/reset',
      resetAgenda: 'agenda/reset',
      resetPagelle: 'pagelle/reset',
      resetAssenze: 'assenze/reset',
      resetNoteDisciplinari: 'noteDisciplinari/reset',
      resetAnnotazioni: 'annotazioni/reset',
      resetComunicazioni: 'comunicazioni/reset',
      resetCompiti: 'compiti/reset',
      resetStudente: 'studente/reset',
      resetAuth: 'auth/reset',
      resetMain: 'main/reset',
      refreshStudente: 'main/refreshStudente',
      loadCalendario: 'main/loadCalendario'
    })
  },
  beforeMount() {
    // this.doLoadAggiornamenti()
  }
}
