export default {
  data() {
    return {
      horizontalAlign: 'center',
      verticalAlign: 'top'
    }
  },
  methods: {
    showNotification(params = {}) {
      this.$notify({
        // component: {
        //   template: `${params.content}`
        // },
        message: params.content,
        timeout: 0,
        horizontalAlign:
          'horizontalAlign' in params
            ? params.horizontalAlign
            : this.horizontalAlign,
        verticalAlign:
          'verticalAlign' in params ? params.verticalAlign : this.verticalAlign,
        type: params.type
      })
    }
  }
}
