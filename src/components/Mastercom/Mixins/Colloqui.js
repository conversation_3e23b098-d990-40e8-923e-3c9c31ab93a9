import { mapGetters, mapActions } from 'vuex'

export default {
  name: '<PERSON><PERSON><PERSON>',
  render: false,
  data() {
    return {
      prenotazioniPanelVisible: false,
      idProfessoreSelected: 0
    }
  },
  computed: {
    urlColloquiPrenotati() {
      return `https://mp.registroelettronico.com${
        this.urlBase
      }/stampa_colloqui_prenotati/`
    },
    colloquiInsegnante() {
      return this.colloqui
        .filter(c => c.id_professore === this.idProfessore)
        .filter(c => c.generale !== true)
        .filter(c => this.$dayjs(c.data) >= this.$dayjs())
        .sort((a, b) => {
          return this.$dayjs(a.data) < this.$dayjs(b.data) ? -1 : 1
        })
        .map(c => this.dettaglioColloquio(c))
    },
    colloquiGenerali() {
      return this.colloqui
        .filter(c => c.id_professore === this.idProfessore)
        .filter(c => c.generale === true)
        .filter(c => this.$dayjs(c.data) >= this.$dayjs())
        .sort((a, b) => {
          return this.$dayjs(a.data) < this.$dayjs(b.data) ? -1 : 1
        })
        .map(c => this.dettaglioColloquio(c))
    },
    dateColloquiGenerali() {
      let date = [
        ...new Set(
          this.colloqui
            .filter(c => c.generale === true)
            .filter(c =>
              this.$dayjs(c.data).isSameOrAfter(this.$dayjs(), 'day')
            )
            .map(c => {
              c.giorno = this.$dayjs(c.data)
                .second(0)
                .minute(0)
                .hour(0)
                .format('YYYY-MM-DD')
              return c
            })
            .sort((a, b) => {
              return this.$dayjs(a.data).isSameOrAfter(this.$dayjs(b.data))
                ? 1
                : -1
            })
            .map(c => c.giorno)
        )
      ].map(c => this.$dayjs(c))
      return date
    },
    elencoInsegnanti() {
      let result = []
      if (this.colloqui.length === 0) {
        return result
      }
      for (let item of this.materieStudenteCorrente) {
        for (let professore of item.professori) {
          if ('visualizza_colloqui' in professore) {
            if (professore.visualizza_colloqui != true) {
              continue
            }
          }
          let professorePresente = result.filter(r => {
            return r.id === professore.id
          })
          if (professorePresente.length) {
            continue
          }
          this.id = professore.id
          let colloquiProfessore = this.colloqui.filter(c => {
            return c.id_professore === professore.id
          })

          let colloquiIndividuali = colloquiProfessore
            .filter(c => c.generale !== true)
            .filter(c =>
              this.$dayjs(c.data).isSameOrAfter(this.$dayjs(), 'day')
            )

          let prenotatoIndividuale = colloquiIndividuali
            .filter(c => {
              return c.stato === 'prenotato'
            })
            .sort((a, b) => {
              return this.$dayjs(a.data) < this.$dayjs(b.data) ? -1 : 1
            })

          let colloquiGenerali = colloquiProfessore
            .filter(c => c.generale === true)
            .filter(c =>
              // this.$dayjs(c.data).isSameOrAfter(this.$dayjs(), 'day')
              this.$dayjs(c.data).isSame(
                this.$dayjs(this.dateParams.data_inizio),
                'day'
              )
            )

          let prenotatoGenerali = colloquiGenerali
            .filter(c => {
              return c.stato === 'prenotato'
            })
            .sort((a, b) => {
              return this.$dayjs(a.data) < this.$dayjs(b.data) ? -1 : 1
            })

          professore.colloqui = colloquiIndividuali
          professore.prenotato = prenotatoIndividuale.length ? true : false
          professore.prossimoColloquio = prenotatoIndividuale.length
            ? this.dettaglioColloquio(prenotatoIndividuale[0])
            : null
          professore.materia = colloquiProfessore.length
            ? colloquiProfessore[0].titolo
            : item.descrizione

          professore.colloqui_generali = colloquiGenerali
          professore.prenotato_generali = prenotatoGenerali.length
            ? true
            : false
          professore.prossimoColloquioGenerali = prenotatoGenerali.length
            ? this.dettaglioColloquio(prenotatoGenerali[0])
            : null

          result.push(professore)
        }
      }
      result = result.sort((a, b) => {
        if (a.colloqui.length > 0 && b.colloqui.length == 0) {
          return -1
        }

        if (a.colloqui.length == 0 && b.colloqui.length > 0) {
          return 1
        }
      })
      return result
    },
    ...mapGetters({
      colloqui: 'colloqui/all',
      dateParams: 'colloqui/date_params',
      currentColloquio: 'colloqui/current',
      updatingColloquio: 'colloqui/updating',
      richiestaColloquiIndividuali: 'main/richiestaColloquiIndividuali'
    })
  },
  methods: {
    dettaglioColloquio(c) {
      c.slot_prenotato = ''
      c.slot_prenotabili = {}
      if ('slot_disponibili' in c === false) {
        return null
      }
      if (Array.isArray(c.slot_disponibili)) {
        c.slot_prenotabili = c.slot_disponibili.filter(
          s => s.stato.toLowerCase() === 'libero'
        )
        c.slot_disponibili.map(sd => {
          sd.background_color = '#00CC00'
          if (sd.stato.toLowerCase() == 'occupato') {
            sd.background_color = '#FF0000'
          }
          if (sd.stato.toLowerCase() == 'prenotato') {
            c.slot_prenotato = sd
          }
          return sd
        })
      }
      c.background_color = '#00CC00'
      if (c.stato === 'concluso') {
        c.background_color = '#aaaaaa'
        c.slot_prenotabili = {}
      }
      if (c.stato === 'occupato') {
        c.background_color = '#ff0000'
      }
      return c
    },
    openPrenotazioniPanel() {
      this.idProfessoreSelected = this.idProfessore
      this.prenotazioniPanelVisible = true
    },
    refreshCurrent() {
      if (typeof this.currentColloquio.id === 'number') {
        let newColloqui = this.colloqui.filter(
          c => c.id === this.currentColloquio.id
        )
        if (newColloqui.length) {
          this.setCurrent(newColloqui[0])
        }
      }
      return
    },
    backgroundColor(stato) {
      let colore = '#00CC00'
      if (stato === 'occupato') {
        colore = '#FF0000'
      }
      if (stato === 'concluso') {
        colore = '#AAAAAA'
      }
      return colore
    },
    ...mapActions({
      loadColloqui: 'colloqui/load',
      loadColloquiFiltrati: 'colloqui/loadFiltered',
      setDataInizio: 'colloqui/setDataInizio',
      setDataFine: 'colloqui/setDataFine',
      prenota: 'colloqui/prenota',
      cancella: 'colloqui/cancella',
      setCurrent: 'colloqui/setCurrent',
      setUpdating: 'colloqui/setUpdating',
      setTargetSlot: 'colloqui/setTargetSlot',
      setType: 'colloqui/setType',
      setIdProfessoreRichiesta: 'colloqui/setIdProfessoreRichiesta',
      richiestaColloquio: 'colloqui/richiestaColloquio'
    })
  }
}
