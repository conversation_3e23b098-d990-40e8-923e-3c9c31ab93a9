import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'Mense',
  render: false,
  computed: {
    ...mapGetters({
      pastiConsumati: 'mense/pastiConsumati',
      creditoResiduo: 'mense/creditoResiduo',
      pastoCorrente: 'mense/pastoCorrente',
      idPastoCorrente: 'mense/idPastoCorrente',
      isPastoPrenotabile: 'mense/isPastoPrenotabile',
      isPrenotazionePossibile: 'mense/isPrenotazionePossibile',
      pagamenti: 'mense/pagamenti',
      elencoPastiFruiti: 'mense/elencoPastiFruiti',
      allarmeCreditoResiduo: 'mense/allarmeCreditoResiduo'
    })
  },
  methods: {
    ...mapActions({
      loadMense: 'mense/loadMense',
      loadMensePagamenti: 'mense/loadMensePagamenti',
      loadPastiFruiti: 'mense/loadPastiFruiti',
      loadPastoCorrente: 'mense/loadPastoCorrente',
      setIdPastoCorrente: 'mense/setIdPastoCorrente',
      setServizioPrenotato: 'mense/setServizioPrenotato',
      prenotaPasto: 'mense/prenotaPasto'
    })
  }
}
