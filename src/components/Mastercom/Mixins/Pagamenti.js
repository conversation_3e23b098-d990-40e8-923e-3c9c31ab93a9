import { mapGetters, mapActions } from 'vuex'

export default {
  name: '<PERSON><PERSON><PERSON>',
  computed: {
    estrattoContoCreditiDisplay() {
      return this.estrattoContoCrediti
    },
    storicoDisplay() {
      return this.storico
    },
    fattureDisplay() {
      return this.fatture
    },
    articoliDisplay() {
      return this.articoli
    },
    marketplaceDisplay() {
      return this.marketplace
    },
    estrattoContoDisplay() {
      return this.estrattoConto
    },
    noteCreditoDisplay() {
      return this.noteCredito
    },
    reportDisplay() {
      return this.report
    },
    filtroAnni() {
      let anni = [...new Set(this.estrattoConto.map(x => x.anno))]
      return anni.sort((a, b) => b - a)
    },
    stripeTestMode() {
      return this.stripePK.startsWith('pk_test')
    },
    ...mapGetters({
      storico: 'pagamenti/storico',
      fatture: 'pagamenti/fatture',
      articoli: 'pagamenti/articoli',
      marketplace: 'pagamenti/marketplace',
      estrattoConto: 'pagamenti/estrattoConto',
      estrattoContoCrediti: 'pagamenti/estrattoContoCrediti',
      report: 'pagamenti/report',
      carrello: 'pagamenti/carrello',
      noteCredito: 'pagamenti/notecredito',
      articoliCarrello: 'pagamenti/articoliCarrello',
      totaleCarrello: 'pagamenti/totaleCarrello',
      satispayEnv: 'pagamenti/satispayEnv',
      satispayScriptUrl: 'pagamenti/satispayScriptUrl',
      satispayKeyId: 'pagamenti/satispayKeyId',
      isSatispayEnabled: 'pagamenti/satispayEnabled',
      satispayPaymentId: 'pagamenti/satispayPaymentId',
      satispayResult: 'pagamenti/satispayResult',
      isStripeEnabled: 'pagamenti/stripeEnabled',
      stripePK: 'pagamenti/stripePK',
      stripePaymentIntent: 'pagamenti/stripePaymentIntent',
      reset: 'pagamenti/reset'
    })
  },
  watch: {
    isSatispayEnabled: function(val) {
      this.carrelloDisplay = val
    },
    isStripeEnabled: function(val) {
      this.carrelloDisplay = val
    }
  },
  data() {
    return {
      showPanels: false,
      loaded: false,
      panelLoading: false,
      carrelloDisplay: null
    }
  },
  methods: {
    itemInCart(id) {
      return this.carrello.includes(id)
    },
    doCheckAreaActive() {
      this.initScuolaStudente(this.authObject).then(() => {
        this.showPanels = this.isSectionEnabled('pagamenti')
      })
    },
    doLoadArticoli() {
      this.panelLoading = true
      this.loadArticoli(this.authObject)
        .then(enabled => {
          this.loaded = enabled
          if (this.aggiornamenti) {
            this.doLoadAggiornamenti()
            this.aggiornamenti = false
          }
        })
        .catch(error => {
          this.setError(error)
        })
        .finally(() => {
          this.panelLoading = false
        })
    },
    doLoadMarketplace() {
      this.panelLoading = true
      this.loadMarketplace(this.authObject)
        .then(enabled => {
          this.loaded = enabled
          if (this.aggiornamenti) {
            this.doLoadAggiornamenti()
            this.aggiornamenti = false
          }
        })
        .catch(error => {
          this.setError(error)
        })
        .finally(() => {
          this.panelLoading = false
        })
    },
    doLoadEstrattoConto() {
      this.panelLoading = true
      this.loadEstrattoConto(this.authObject)
        .then(enabled => {
          this.loaded = enabled
          if (this.aggiornamenti) {
            this.doLoadAggiornamenti()
            this.aggiornamenti = false
          }
        })
        .catch(error => {
          this.setError(error)
        })
        .finally(() => {
          this.panelLoading = false
        })
    },
    doLoadEstrattoContoCrediti() {
      this.panelLoading = true
      this.loadCrediti(this.authObject)
        .then(enabled => {
          this.loaded = enabled
          if (this.aggiornamenti) {
            this.doLoadAggiornamenti()
            this.aggiornamenti = false
          }
        })
        .catch(error => {
          this.setError(error)
        })
        .finally(() => {
          this.panelLoading = false
        })
    },
    doLoadStorico() {
      this.panelLoading = true
      this.initScuolaStudente(this.authObject)
        .then(() => {
          return this.isSectionEnabled('pagamenti')
        })
        .then(enabled => {
          if (enabled === true) {
            this.loadStorico(this.authObject)
          }
          return enabled
        })
        .then(enabled => {
          this.loaded = enabled
          if (this.aggiornamenti) {
            this.doLoadAggiornamenti()
            this.aggiornamenti = false
          }
        })
        .catch(error => {
          this.setError(error)
        })
        .finally(() => {
          this.panelLoading = false
        })
    },
    doLoadFatture() {
      this.panelLoading = true
      this.initScuolaStudente(this.authObject)
        .then(() => {
          return this.isSectionEnabled('pagamenti')
        })
        .then(enabled => {
          if (enabled === true) {
            this.loadFatture(this.authObject)
          }
          return enabled
        })
        .then(enabled => {
          this.loaded = enabled
          if (this.aggiornamenti) {
            this.doLoadAggiornamenti()
            this.aggiornamenti = false
          }
        })
        .catch(error => {
          this.setError(error)
        })
        .finally(() => {
          this.panelLoading = false
        })
    },
    doLoadReport() {
      this.panelLoading = true
      this.loadReport(this.authObject)
        .then(() => {
          this.loaded = true
          if (this.aggiornamenti) {
            this.doLoadAggiornamenti()
            this.aggiornamenti = false
          }
        })
        .catch(error => {
          this.setError(error)
        })
        .finally(() => {
          this.panelLoading = false
        })
    },
    ...mapActions({
      loadCrediti: 'pagamenti/loadCrediti',
      loadStorico: 'pagamenti/loadStorico',
      loadFatture: 'pagamenti/loadFatture',
      loadArticoli: 'pagamenti/loadArticoli',
      loadMarketplace: 'pagamenti/loadMarketplace',
      loadEstrattoConto: 'pagamenti/loadEstrattoConto',
      loadSatispayConfig: 'pagamenti/loadSatispayConfig',
      loadStripeConfig: 'pagamenti/loadStripeConfig',
      loadReport: 'pagamenti/loadReport',
      getSatispayPaymentId: 'pagamenti/getSatispayPaymentId',
      getSatispayPaymentDetail: 'pagamenti/getSatispayPaymentDetail',
      getStripePaymentIntent: 'pagamenti/getStripePaymentIntent',
      addToCart: 'pagamenti/addToCart',
      removeFromCart: 'pagamenti/removeFromCart',
      emptyCart: 'pagamenti/emptyCart'
    })
  },
  mounted() {
    if (this.isSatispayEnabled !== null || this.isStripeEnabled !== null) {
      this.carrelloDisplay = this.isSatispayEnabled || this.isStripeEnabled
    }
  }
}
