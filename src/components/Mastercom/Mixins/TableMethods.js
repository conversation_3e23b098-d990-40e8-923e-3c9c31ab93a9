import { columnSorter } from '@/utils/ui-table'

export default {
  name: 'TableMethods',
  data() {
    return {
      subjectQuery: '',
      tableData: []
    }
  },
  methods: {
    filter<PERSON>andler(value, row, column) {
      const property = column['property']
      return row[property] === value
    },
    subjectFilterHandler(values) {
      if (values.length > 0) {
        console.log('set', values)
        this.subjectQuery = values
      } else {
        this.subjectQuery = ''
      }
    },
    sortData(column) {
      this.tableData = columnSorter(this.tableData, column.prop, column.order)
    }
  }
}
