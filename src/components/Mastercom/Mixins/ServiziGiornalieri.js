import { mapGetters, mapActions } from 'vuex'
export default {
  name: 'ServiziGiornalieri',
  render: false,
  data() {
    return {}
  },
  computed: {
    ...mapGetters({
      prenotazioniGiornaliere: 'serviziGiornalieri/prenotazioniGiornaliere',
      marketplace: 'serviziGiornalieri/marketplace',
      errorMessage: 'serviziGiornalieri/errorMessage',
      credito: 'serviziGiornalieri/credito'
    })
  },
  methods: {
    _dialogWidth() {
      return window.outerWidth > 1024 ? '50%' : '98%'
    },
    hasPrenotazioni(data) {
      let prenotazioni = this.prenotazioniGiornaliere.filter(
        a => a.data === data
      )
      return prenotazioni.length > 0
    },
    adesioniGiornaliere(data) {
      let adesioni = this.prenotazioniGiornaliere.filter(a => a.data === data)
      return adesioni[0].adesioni
    },
    backgroundColor(adesione) {
      let colore = '#AAAAAA'
      if (adesione === 'SI') {
        colore = '#FFFFFF'
      }
      return colore
    },
    ...mapActions({
      loadPrenotazioniGiornaliere:
        'serviziGiornalieri/loadPrenotazioniGiornaliere',
      loadMarketplace: 'serviziGiornalieri/loadMarketplace',
      loadCredito: 'serviziGiornalieri/loadCredito',
      clearMarketplace: 'serviziGiornalieri/clearMarketplace',
      adesioneServizio: 'serviziGiornalieri/adesione',
      cancellazioneServizio: 'serviziGiornalieri/cancellazione',
      setDataAdesione: 'serviziGiornalieri/setDataAdesione',
      setMarketplaceAdesione: 'serviziGiornalieri/setMarketplaceAdesione'
    })
  }
}
