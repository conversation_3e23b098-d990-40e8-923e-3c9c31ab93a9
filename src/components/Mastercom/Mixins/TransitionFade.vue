<script>
// import Vue from 'vue'

// Vue.component('fade-transition', {
//   functional: true,
//   render: function(createElement, context) {
//     var data = {
//       props: {
//         name: 'fade-transition',
//         mode: 'out-in'
//       },
//       on: {
//         beforeEnter: function(el) {
//           el.style.opacity = 0
//         },
//         leave: function(el) {
//           el.style.opacity = 1
//         },
//         enter: function(el) {
//           Velocity
//         }
//       }
//     }
//     return createElement('transition', data, context.children)
//   }
// })
</script>
