<template>
  <section>
    <div class="col-xs-12" v-if="hideData === false">
      <h3 :class="dateAlert">
        <i class="ti-calendar"></i>
        {{ item.data | extdate }}
      </h3>
    </div>
    <div class="col-xs-12">
      <i class="ti-timer"></i>
      {{ item.ora_inizio }} - {{ item.ora_fine }}
    </div>
    <div class="col-xs-12 slot-prenotato" v-if="item.slot_prenotato">
      <badge
        background-color="#fff"
        :mini="true"
        border-width="0"
        :outline="backgroundColor(item.stato)"
      >
        <i class="ti-check"></i>
      </badge>
      {{ item.slot_prenotato.descrizione }}
    </div>
    <div class="col-xs-12" v-if="item.titolo_colloqui">
      <i class="ti-pin"></i>
      <span v-html="item.titolo_colloqui"></span>
    </div>
    <div class="col-xs-12" v-if="item.note">
      <i class="ti-notepad"></i>
      <span v-html="item.note"></span>
    </div>
    <div class="col-xs-12" v-if="item.link_videomeeting">
      <a
        class="btn btn-fill btn-info"
        :href="item.link_videomeeting"
        target="_blank"
      >
        <i class="ti-video-camera"></i>
        {{ item.testo_videomeeting }}
      </a>
    </div>
  </section>
</template>

<script>
import Badge from '@/components/Mastercom/UIComponents/Badge'
import Colloqui from '@/components/Mastercom/Mixins/Colloqui.js'

export default {
  name: 'ColloquiDetail',
  mixins: [Colloqui],
  components: {
    Badge
  },
  computed: {
    dateAlert() {
      return this.item.has_alert ? 'hot' : 'cold'
    }
  },
  props: {
    hideData: {
      type: Boolean,
      default: false
    },
    item: {
      type: Object,
      default: () => {}
    }
  }
}
</script>

<style lang="scss" scoped>
div {
  i {
    display: inline-block;
    margin-right: 5px;
  }
  span {
    word-break: break-word;
  }
  padding: 0 0 8px 0;
}
.slot-prenotato {
  color: #00cc00;
  font-weight: 700;
}
.hot {
  font-weight: 700;
  color: #00cc00;
}
.cold {
  font-weight: 300;
  color: #777;
}
</style>
