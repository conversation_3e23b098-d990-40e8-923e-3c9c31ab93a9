<template>
  <div v-if="data !== ''">
    <h6>
      <span class="ti-time"></span>
      {{ display(data, formatoData) }}
    </h6>
  </div>
</template>

<script>
import Vue from 'vue'
export default {
  name: 'DateDisplay',
  props: {
    formatoData: {
      type: String,
      default: ''
    },
    data: {
      type: String,
      default: ''
    },
    novita: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    display(val, filter) {
      return Vue.filter(filter)(val)
    }
  }
}
</script>

<style lang="scss" scoped></style>
