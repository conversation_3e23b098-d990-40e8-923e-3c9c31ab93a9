<template>
  <div class="clearfix">
    <h6 class="insegnante">{{ displayTeacher }}</h6>
    <span v-if="titolo !== ''">
      {{ titolo }}
    </span>
  </div>
</template>

<script>
import Materia from './Materia.vue'
export default {
  extends: Materia,
  props: {
    nome: {
      type: String,
      default: ''
    },
    titolo: {
      type: String,
      default: ''
    }
  },
  computed: {
    // displayTeacher() {
    //   return this.nome !== ''
    //     ? this.nome
    //     : this.materia.professori.reduce((acc, cv) => {
    //         return acc !== '' ? `${acc}, ${cv.nome}` : cv.nome
    //       }, '')
    // }
    displayTeacher() {
      return this.nome
    }
  }
}
</script>

<style lang="scss" scoped>
h6 {
  margin-bottom: 0;
}
</style>
