<template>
  <div class="top-alert updates-alert" v-if="showUpdates">
    <div class="top-alert__message updates-message">
      <span class="updates-message--label">
        Aggiornamento disponibile
        <span v-if="requiresLogout">(richiede logout):</span>
        <span v-else>:</span>
      </span>
      <strong>v{{ versionCurrent }}</strong>
      <small>-></small>
      <strong>v{{ versionCheck }}</strong>
    </div>
    <button
      class="btn btn-fill btn-wd btn-warning"
      v-if="!updating && !requiresLogout"
      @click="onClickUpdate(versionCheck)"
    >
      Aggiorna
    </button>
    <button
      class="btn btn-fill btn-wd btn-warning"
      v-if="!updating && requiresLogout"
      @click="onClickUpdate(versionCheck)"
    >
      Aggiorna ed esci
    </button>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import axios from 'axios'
import store from '@/store'
export default {
  name: 'AutoUpdateAlert',
  data() {
    return {
      updateData: '',
      showUpdates: false,
      versionCurrent: '0',
      versionCheck: '0',
      versionOptions: [],
      updateTimeout: 0,
      updating: false
    }
  },
  computed: {
    requiresLogout: function() {
      return this.versionOptions.indexOf('logout') >= 0
    },
    ...mapGetters({
      swUpdateAvailable: 'app/isSWUpdateAvailable',
      appHasFocus: 'app/isAppFocused',
      appVersion: 'app/appVersion'
    })
  },
  methods: {
    compareVersionStrings(version1, version2) {
      let regex = /\b\d+\.\d+\.\d+\b/g
      if (!version1.match(regex) || !version2.match(regex)) {
        return 0
      }
      const parts1 = version1.split('.').map(Number)
      const parts2 = version2.split('.').map(Number)

      for (let i = 0; i < Math.max(parts1.length, parts2.length); i++) {
        const part1 = parts1[i] || 0
        const part2 = parts2[i] || 0

        if (part1 > part2) {
          return 1
        }
      }
      return 0
    },
    checkForUpdates: function() {
      if (this.updating) {
        return
      }
      console.log('APP VERSION', this.appVersion)
      axios
        .get(`/version.txt?${new Date().getTime()}`)
        .then(result => {
          if (result && 'data' in result) {
            this.versionCurrent = this.appVersion
            this.versionCheck = result.data.replace('VERSION=', '')
            if (
              this.compareVersionStrings(
                this.versionCheck,
                this.versionCurrent
              ) > 0 &&
              navigator &&
              'serviceWorker' in navigator
            ) {
              console.log('UPDATING 1')
              this.startForcedUpdateTimeout()
              return navigator.serviceWorker.getRegistration()
            }
          }
        })
        .then(registration => {
          if (registration) {
            registration.addEventListener('updatefound', () =>
              this.onSWUpdateFound(registration)
            )
            registration.update()
          }
        })
      // .catch(error => {
      //   console.log('[ERR] checkForUpdates', error)
      // })
    },
    extractUpdateInfo: function(data) {
      this.updateData = String(data)
      var matchVersion = this.updateData.match(/^[0-9.]+/),
        matchOptions = this.updateData.match(/\soptions=(\w+)/)
      if (matchVersion && matchVersion.length) {
        this.versionNewer = matchVersion[0]
      }
      if (matchOptions && matchOptions.length > 1) {
        this.versionOptions = matchOptions[1].split('|')
      }
    },
    lockUI: function() {
      let main = document.getElementsByTagName('main')
      if (main) {
        main[0].classList.add('lock-ui')
      }
    },
    startForcedUpdateTimeout: function() {
      // If the new service worker fails to install and activate after 10 seconds, we force the displaying of the update message
      if (!this.updateTimeout) {
        this.updateTimeout = setTimeout(() => {
          this.showUpdates = true
        }, 10000)
      }
    },
    onSWUpdateFound: function(swReg) {
      var newSW = swReg.installing
      swReg.installing.addEventListener('statechange', () =>
        this.onSWStateChange(newSW)
      )
    },
    onSWStateChange: function(newSW) {
      if (newSW.state == 'activated' && navigator.serviceWorker.controller) {
        this.showUpdates = true
      }
    },
    onClickUpdate: function(ver) {
      /*
      if (!this.updating) {
        console.log('UPDATING 2')
        this.lockUI()
        console.log('UPDATING 3')
        this.updating = true
        if (this.requiresLogout) {
          console.log('UPDATING 4 ')
          this.logout()
        } else {
          console.log('UPDATING 5')
          return;
          window.location.reload()
        }
      } */
      this.setAppVersion(ver)
      let dataToKeep = [
        'app',
        'auth',
        'materie',
        'main',
        'aggiornamenti',
        'apiErrorHandler'
      ]
      let refreshData = {}
      for (let key of Object.keys(store.state)) {
        if (dataToKeep.includes(key)) {
          refreshData[key] = store.state[key]
        } else {
          refreshData[key] = store.dispatch[`${key}/refreshReset`]
        }
      }
      refreshData = store.state
      sessionStorage.setItem('mp-webapp-data', JSON.stringify(refreshData))
      window.location.reload()
    },
    ...mapActions({
      logout: 'user/logout',
      setAppVersion: 'app/setAppVersion'
    })
  },
  mounted() {
    this.checkForUpdates()
  },
  watch: {
    appHasFocus: function() {
      if (this.appHasFocus) {
        this.checkForUpdates()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.top-alert {
  z-index: 10;
  position: fixed;
  width: 100%;
  padding: 1em;
}
.updates-alert {
  background: #aaaaaa;
  text-align: right;
  .updates-message {
    color: #ffffff;
    margin: 0.5em 1em;
    float: left;
    strong {
      margin: 0px 5px;
      font-size: 0.9em;
      color: #ffffff;
    }
  }
}

@media (max-width: 640px) {
  .updates-message--label {
    display: none;
  }
}
</style>
