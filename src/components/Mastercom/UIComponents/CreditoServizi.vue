<template>
  <div>
    <el-card shadow="never" class="top-card">
      <button class="btn btn-default" @click.stop.prevent="mostraCredito()">
        {{ $t('servizi.crediti.label_button') }}
      </button>
    </el-card>
    <el-dialog
      :append-to-body="true"
      :visible.sync="creditoVisible"
      top="5vh"
      width="80%"
    >
      <div v-loading="dataLoading">
        <div v-for="(item, index) in credito" :key="`credito_${index}`">
          <h4>{{ item.descrizione }}</h4>
          <div class="row table head">
            <div class="col-xs-5">
              {{ $t('servizi.crediti.title_servizi_desc') }}
            </div>
            <div class="col-xs-2 right">
              {{ $t('servizi.crediti.title_servizi_cup') }}
            </div>
            <div class="col-xs-1 center">
              {{ $t('servizi.crediti.title_servizi_presenze') }}
            </div>
            <div class="col-xs-1 center">
              {{ $t('servizi.crediti.title_servizi_assenze') }}
            </div>
            <div class="col-xs-1 center">
              {{ $t('servizi.crediti.title_servizi_non_definite') }}
            </div>
            <div class="col-xs-2 right">
              {{ $t('servizi.crediti.title_servizi_da_pagare') }}
            </div>
          </div>
          <div
            v-for="(servizio, s_index) in item.servizi"
            :key="`servizio_${index}_${s_index}`"
            class="row table item"
          >
            <div class="col-xs-5">
              {{ servizio.nome_sitoapp }}
            </div>
            <div class="col-xs-2 right">
              {{ servizio.costo_unitario_prenotazione | currency }}
            </div>
            <div class="col-xs-1 center">
              {{ servizio.presenze }}
            </div>
            <div class="col-xs-1 center">
              {{ servizio.assenze }}
            </div>
            <div class="col-xs-1 center">
              {{ servizio.non_definite }}
            </div>
            <div class="col-xs-2 right">
              {{ servizio.importo_da_pagare | currency }}
            </div>
          </div>
          <div class="totali row table item">
            <div class="col-xs-4 col-xs-offset-4 right">
              <h6>
                {{ $t('servizi.crediti.title_credito_total') }}
                {{ item.credito_totale | currency }}
              </h6>
            </div>
            <div class="col-xs-4 right">
              <h6>
                {{ $t('servizi.crediti.title_credito_rimanente') }}
                {{ item.credito_rimanente | currency }}
              </h6>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common.js'
import ServiziGiornalieri from '@/components/Mastercom/Mixins/ServiziGiornalieri.js'
import { Dialog, Loading } from 'element-ui'

Vue.use(Dialog)
Vue.use(Loading)
export default {
  name: 'CreditoServizi',
  mixins: [Common, ServiziGiornalieri],
  data() {
    return {
      creditoVisible: false,
      dataLoading: false
    }
  },
  methods: {
    mostraCredito() {
      this.creditoVisible = true
      this.dataLoading = true
      this.loadCredito(this.authObject).then(() => {
        this.dataLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.top-card {
  margin-bottom: 10px;
}
.head {
  font-weight: 700;
}
.table {
  border-bottom: 1px solid #eee;
  padding-bottom: 5px;
  margin-top: 5px;
}
.center {
  text-align: center;
}
.right {
  text-align: right;
}
</style>
