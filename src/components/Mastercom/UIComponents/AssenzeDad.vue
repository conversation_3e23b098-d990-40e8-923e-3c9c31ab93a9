<template>
  <div>
    <div v-if="elencoOre" class="elenco-ore">
      <div class="row header">
        <div class="col-xs-4 text-center">
          <i class="ti-time"></i>
          orario
        </div>
      </div>
      <div class="row header">
        <div class="col-xs-2 text-center">inizio</div>
        <div class="col-xs-2 text-center">fine</div>
        <div class="col-xs-5">Materia</div>
        <div class="col-xs-3 text-center">Stato</div>
      </div>
      <div v-for="(item, index) in elencoOre" :key="index" class="row ore">
        <div class="col-xs-2 ora">
          {{ item.ora_inizio }}
        </div>
        <div class="col-xs-2 ora">
          {{ item.ora_fine }}
        </div>
        <div class="col-xs-5">
          <h6>{{ item.materia }}</h6>
        </div>
        <div class="col-xs-3 stato-assenza">
          {{ item.stato }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AssenzeDad',
  props: {
    detail: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    elencoOre() {
      let result = []
      for (let item of this.detail) {
        result.push({
          ora_inizio: item.ora_inizio,
          ora_fine: item.ora_fine,
          materia: item.materia,
          stato: item.stato,
          motivazione: item.motivazione
        })
      }
      return result
    }
  }
}
</script>

<style lang="scss" scoped>
.elenco-ore {
  margin: 2em 0;
  .header {
    font-size: 0.7em;
    text-transform: uppercase;
    font-weight: 700;
    background: #f8f8f8;
  }
  .ore {
    border-bottom: 1px solid #cccccc;
    font-size: 0.8em;
    &:hover {
      background-color: #edfcb6;
    }
    .ora {
      margin: 5px 0;
      text-align: center;
    }
    .stato-assenza {
      margin: 5px 0;
      color: #ff0000;
      text-align: center;
    }
  }
}
</style>
