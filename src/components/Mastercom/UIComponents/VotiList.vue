<template>
  <div class="card">
    <div class="card-header">
      <h4 class="card-title">{{ titolo }} {{ elencoCompetenze }}</h4>
      <p class="category">{{ sottotitolo }}</p>
      <div class="filters" v-if="hasFilters">
        <div class="col-xs-4">
          <el-switch
            v-model="showVotoScritto"
            @change="changeScritto"
          ></el-switch>
          {{ $t('voti.scritto') }}
        </div>
        <div class="col-xs-4">
          <el-switch v-model="showVotoOrale" @change="changeOrale"></el-switch>
          {{ $t('voti.orale') }}
        </div>
        <div class="col-xs-4">
          <el-switch
            v-model="showVotoPratico"
            @change="changePratico"
          ></el-switch>
          {{ $t('voti.pratico') }}
        </div>
      </div>
    </div>
    <fade-transition tag="div" class="card-content">
      <fade-transition group tag="ul" class="item-list" :duration="150">
        <voto
          v-for="(item, index) in voti"
          :key="index"
          :voto="item"
          :on-confirm-action="onConfirmAction"
          :confirm-action-text="confirmActionText"
        >
          <badge slot="voto" :background-color="item.colore_simbolo">
            {{ item.simbolo }}
          </badge>
          <desc-peso-voto slot="desc-voto" v-if="item.descrizione_peso">
            {{ $t('voti.descrizione_peso', [item.descrizione_peso]) }}
          </desc-peso-voto>
          <desc-peso-voto slot="desc-voto" v-if="item.descrizione_competenza">
            {{ item.descrizione_competenza }}
          </desc-peso-voto>
          <div slot="header" class="full-relative-width">
            <materia
              :titolo="item.materia"
              :indice-colore="item.indice_colore"
            ></materia>
            <insegnante :nome="item.insegnante"></insegnante>
          </div>
          <h6 slot="footer">
            <i class="ti-time"></i>
            {{ item.data | simpledate }}
          </h6>
        </voto>
      </fade-transition>
    </fade-transition>
    <div class="card-footer"></div>
  </div>
</template>
<script>
import Materia from '@/components/Mastercom/UIComponents/Materia.vue'
import Insegnante from '@/components/Mastercom/UIComponents/Insegnante.vue'
import Voto from '@/components/Mastercom/UIComponents/Voto.vue'
import Badge from '@/components/Mastercom/UIComponents/Badge.vue'
import DescPesoVoto from '@/components/Mastercom/UIComponents/DescPesoVoto.vue'
import { Switch } from 'element-ui'

import { mapState, mapActions } from 'vuex'

export default {
  components: {
    Voto,
    Materia,
    Badge,
    DescPesoVoto,
    Insegnante,
    ElSwitch: Switch
  },
  computed: {
    ...mapState({
      showVotoScritto: state => state.voti.showVotoScritto,
      showVotoOrale: state => state.voti.showVotoOrale,
      showVotoPratico: state => state.voti.showVotoPratico
    })
  },
  props: {
    titolo: {
      type: String,
      default: ''
    },
    sottotitolo: {
      type: String,
      default: ''
    },
    voti: {
      type: Array,
      default: () => []
    },
    hasFilters: {
      type: Boolean,
      default: true
    },
    confirmActionText: {
      type: String,
      default: ''
    },
    onConfirmAction: {
      type: Function,
      default: null
    }
  },
  methods: {
    hasHeader() {
      return !!this.$slots.header
    },
    hasFooter() {
      return !!this.$slots.footer
    },
    changeScritto(event) {
      if (!event) {
        this.hideScritto()
      } else {
        this.showScritto()
      }
    },
    changeOrale(event) {
      if (!event) {
        this.hideOrale()
      } else {
        this.showOrale()
      }
    },
    changePratico(event) {
      if (!event) {
        this.hidePratico()
      } else {
        this.showPratico()
      }
    },
    ...mapActions({
      showPratico: 'voti/showPratico',
      hidePratico: 'voti/hidePratico',
      showScritto: 'voti/showScritto',
      hideScritto: 'voti/hideScritto',
      showOrale: 'voti/showOrale',
      hideOrale: 'voti/hideOrale'
    })
  }
}
</script>
<style lang="scss" scoped>
.filters {
  margin: 5px 0;
  padding: 5px 0;
  overflow: hidden;
  text-transform: uppercase;
  font-size: 0.8em;
  font-weight: 700;
}

/deep/ .item-list {
  border-bottom: 0;
}
// .list-enter-active,
// .list-leave-active {
//     transition: opacity .2s;
// }

// .list-enter, .list-leave-to {
//     opacity: 0;
// }
</style>
