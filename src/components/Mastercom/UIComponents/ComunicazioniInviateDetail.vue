<template>
  <div class="comunicazioni-detail">
    <h4>{{ item.titolo }}</h4>
    <div class="mittente">
      <h6>
        <span class="ti-user"></span>
        <span>
          {{ item.sottotitolo }}
        </span>
      </h6>
    </div>
    <div class="body">
      <div v-html="item.dettaglio"></div>
    </div>
    <div>
      <allegati-list
        :allegati="item.allegati"
        :item-id="item.id"
      ></allegati-list>
    </div>
  </div>
</template>

<script>
import AllegatiList from '@/components/Mastercom/UIComponents/AllegatiList.vue'
export default {
  name: 'ComunicazioniInviateDetail',
  components: {
    AllegatiList
  },
  props: {
    item: {
      type: Object,
      default: null
    }
  }
}
</script>

<style scoped lang="scss"></style>
