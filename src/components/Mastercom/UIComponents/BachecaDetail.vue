<template>
  <div class="comunicazioni-detail">
    <h4>{{ item.titolo }}</h4>
    <div class="mittente">
      <h6>
        <span class="ti-user"></span>
        <span v-if="item.mittente !== null">
          {{ item.mittente.nome }} {{ item.mittente.cognome }}
        </span>
        <span v-else>b {{ item.sottotitolo }}</span>
      </h6>
    </div>
    <div class="body">
      <div v-html="item.dettaglio"></div>
    </div>
    <div>
      <allegati-list
        :allegati="item.allegati"
        :item-id="item.id"
      ></allegati-list>
    </div>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common'
import Notifications from '@/components/Mastercom/Mixins/Notifications.js'
import AllegatiList from '@/components/Mastercom/UIComponents/AllegatiList.vue'

import { mapActions } from 'vuex'
export default {
  name: 'BachecaDetail',
  mixins: [Common, Notifications],
  components: {
    AllegatiList
  },
  props: {
    item: {
      type: Object,
      default: null
    }
  },
  methods: {
    ...mapActions({
      loadAllegato: 'comunicazioni/loadAllegato',
      setUrlAllegato: 'comunicazioni/setUrlAllegato'
    })
  }
}
</script>

<style scoped lang="scss">
h4 {
  margin-top: 0;
  margin-bottom: 5vh;
}
.mittente {
  margin: 3vh 0;
}

.el-dialog__body {
  margin-top: 0;
  padding-top: 0;
}
</style>
