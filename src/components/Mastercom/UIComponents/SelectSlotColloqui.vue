<template>
  <div class="block">
    <slot name="title"></slot>
    <div class="selettore">
      <el-select
        v-model="selectedValue"
        value-key="id"
        placeholder="Seleziona orario"
        @change="prenotaColloquio()"
      >
        <el-option
          v-for="item in colloquio.slot_disponibili"
          clearable
          :key="item.id"
          :label="item.descrizione"
          :value="item"
          :disabled="item.stato !== 'libero'"
        >
          <badge
            :mini="true"
            :background-color="backgroundColor(item.stato)"
            v-if="item.stato !== 'prenotato'"
          ></badge>
          <badge
            :mini="true"
            background-color="#fff"
            :outline="backgroundColor(item.stato)"
            v-else
          >
            <i class="ti-check selected"></i>
          </badge>
          {{ item.descrizione }}
        </el-option>
      </el-select>
    </div>
    <div v-if="colloquio.stato === 'prenotato'" class="selettore">
      <button
        class="btn btn-fill btn-icon btn-danger"
        @click.stop.prevent="doCancellaPrenotazione()"
      >
        <i class="ti-close"></i>
      </button>
    </div>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common.js'
import Colloqui from '@/components/Mastercom/Mixins/Colloqui.js'
import Badge from '@/components/Mastercom/UIComponents/Badge'
import { Select, Option } from 'element-ui'
export default {
  name: 'SelectSlotColloqui',
  mixins: [Common, Colloqui],
  components: {
    ElSelect: Select,
    ElOption: Option,
    Badge
  },
  data() {
    return {
      updating: false,
      selectedValue: null
    }
  },
  props: {
    colloquio: {
      type: Object,
      default: () => {}
    },
    selected: {
      type: Number,
      default: 0
    }
  },
  methods: {
    canCancellaPrenotazione() {
      return (
        this.colloquio.stato === 'prenotato' &&
        this.$dayjs(this.colloquio.data) > this.$dayjs()
      )
    },
    doCancellaPrenotazione() {
      this.setUpdating(this.colloquio.id)
      this.setTargetSlot(this.colloquio.slot_prenotato.id)
      // this.showLoader()
      this.updating = true
      this.initScuolaStudente(this.authObject)
        .then(() => {
          return this.isSectionEnabled('colloqui')
        })
        .then(enabled => {
          if (enabled === true) {
            return this.cancella(this.authObject)
          }
          return enabled
        })
        .then(enabled => {
          if (enabled !== false) {
            return this.loadColloqui(this.authObject)
          }
        })
        .then(() => {
          return this.setTargetSlot(null)
          // this.hideLoader()
        })
        .finally(() => {
          this.updating = false
          this.setUpdating(0)
        })
    },
    prenotaColloquio() {
      this.setUpdating(this.colloquio.id)
      this.setTargetSlot(this.selectedValue.id)
      this.updating = true
      this.initScuolaStudente(this.authObject)
        .then(() => {
          return this.isSectionEnabled('colloqui')
        })
        .then(enabled => {
          if (enabled === true) {
            return this.prenota(this.authObject)
          }
          return enabled
        })
        .then(enabled => {
          if (enabled !== false) {
            return this.loadColloqui(this.authObject)
          }
        })
        .catch(error => {
          this.setError(error.response)
          this.loadColloqui(this.authObject)
        })
        .finally(() => {
          this.updating = false
          this.setUpdating(0)
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.block {
  padding: 10px;
  display: flex;
}
.selettore {
  display: inline-block;
  margin-right: 50px;
  align-content: stretch;
  align-items: flex-start;
}
.selettore:last-of-type {
  margin-right: 0px;
}
.selected {
  color: #00cc00;
  font-weight: 700;
}
</style>
