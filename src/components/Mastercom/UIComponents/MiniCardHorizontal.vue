<template>
  <div class="card" :data-background-color="cardColor">
    <div class="card-content">
      <div class="row">
        <div class="col-xs-12">
          <slot name="header"></slot>
        </div>
        <slot name="content"></slot>
      </div>
    </div>
    <div class="card-footer">
      <hr />
      <slot name="footer"></slot>
    </div>
  </div>
</template>
<script>
export default {
  name: 'MiniCardHorizontal',
  props: {
    cardColor: {
      type: String,
      default: ''
    }
  }
}
</script>
<style scoped>
.card-content {
  min-height: 85px;
}
</style>
