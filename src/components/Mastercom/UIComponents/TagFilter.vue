<template>
  <div>
    <el-select v-model="selectedValues" clearable placeholder="tag">
      <el-option
        v-for="item in tags"
        :key="item.id"
        class="select-primary"
        :value="item.codice"
        :label="item.descrizione"
      ></el-option>
    </el-select>
  </div>
</template>

<script>
import Tags from '@/components/Mastercom/Mixins/Tags.js'
import { Select, Option } from 'element-ui'
import 'element-theme-chalk/lib/input.css'
import 'element-theme-chalk/lib/select.css'
export default {
  name: 'TagFilter',
  mixins: [Tags],
  components: {
    'el-select': Select,
    'el-option': Option
  },
  watch: {
    selectedValues: function(newVal) {
      if (typeof newVal !== 'number') {
        this.setTagFilter(newVal)
      } else {
        this.setTagFilter([newVal])
      }
    }
  },
  data() {
    return {
      selectedValues: []
    }
  },
  props: {
    tags: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    setTagFilter(t) {
      this.setTag(t)
    }
  }
}
</script>

<style lang="css" scoped>
.el-select .el-input:hover .el-input__icon {
  color: black;
}
</style>
