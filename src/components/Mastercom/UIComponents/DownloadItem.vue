<template>
  <a class="downloadItem" @click="downloadItem">
    <spinner v-if="spinner" :radius="51" class="download-spinner"></spinner>

    <div
      class="btn btn-icon btn-fill btn-sm btn-primary btn-download"
      :class="downloadClass"
      v-else
    >
      <span :class="downloadIcon"></span>
    </div>
    <span v-if="mostradescrizione">{{ descrizione }}</span>
  </a>
</template>

<script>
import { Api } from '@/data/api'
import Common from '@/components/Mastercom/Mixins/Common'
import { Spinner } from 'element-ui'
export default {
  mixins: [Common],
  components: {
    Spinner
  },
  computed: {
    downloadClass() {
      return this.baseDownloadClass[this.downloadState]
    },
    downloadIcon() {
      if (this.icons.hasOwnProperty(this.downloadState) === true) {
        return this.icons[this.downloadState]
      } else {
        return this.baseDownloadIcon[this.downloadState]
      }
    },
    spinner() {
      return this.downloadState === 'active'
      // return true
    }
  },
  data() {
    return {
      downloadState: 'idle',
      baseDownloadClass: {
        idle: 'btn-primary',
        active: 'btn-warning',
        downloaded: 'btn-success'
      },
      baseDownloadIcon: {
        idle: 'ti-download',
        active: 'ti-arrow-right',
        downloaded: 'ti-check'
      }
    }
  },
  props: {
    url: {
      type: String,
      default: null
    },
    descrizione: {
      type: String,
      default: null
    },
    icons: {
      type: Object,
      default: () => ({
        idle: 'ti-download',
        active: 'ti-arrow-right',
        downloaded: 'ti-check'
      })
    },
    mostradescrizione: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    downloadItem() {
      if (!this.url) {
        return null
      }
      this.downloadState = 'active'
      this.fetchAttachment()
        .then(response => this.putFile(response))
        .catch(error => {
          this.downloadState = 'idle'
          this.setError(error.response)
        })
      return false
    },
    adjustUrl(url) {
      const regex = /https?:\/\/[^/\s]+/g
      let result = url
        .replace(regex, 'api')
        .replace('/v2/', '/v3/')
        .replace('api/api', 'api')
      return result
    },

    async fetchAttachment() {
      let api = new Api(this.authObject)
      try {
        api.setRawUrl(this.adjustUrl(this.url))
        // api.setRawUrl(this.url)
        let result = await api.fetchRaw('blob')
        this.downloadState = 'downloaded'
        return result
      } catch (error) {
        throw error
      }
    },
    putFile(response) {
      const url = window.URL.createObjectURL(
        new Blob([response.data], {
          type: response.headers['content-type']
        })
      )
      const link = document.createElement('a')
      link.href = url
      // link.target = '_blank'
      // link.rel = 'noopener noreferrer'
      // Estrai il filename dal content-disposition
      const contentDisposition = response.headers['content-disposition']
      let filename = this.descrizione // fallback

      if (contentDisposition && contentDisposition.includes('filename=')) {
        const match = contentDisposition.match(/filename="?([^"]+)"?/)
        if (match && match[1]) {
          filename = match[1]
        }
      }
      link.setAttribute('download', filename)
      document.body.appendChild(link)
      link.click()
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .download-spinner {
  &.el-spinner {
    .el-spinner-inner {
      .path {
        stroke: rgba(200, 200, 200, 0.8);
      }
    }
  }
}

.downloadItem {
  display: flex;
  align-items: center;
  cursor: pointer;
  .download-spinner {
    margin-right: 10px;
  }

  .btn-download {
    margin-right: 10px;
  }

  .description {
    margin-top: 5px;
  }
}
</style>
