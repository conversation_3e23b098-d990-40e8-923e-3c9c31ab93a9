<template>
  <div>
    <api-error-handler></api-error-handler>
    <el-dialog
      :visible="showChangePassword"
      :append-to-body="true"
      :before-close="resetChangePassword"
      :destroy-on-close="true"
    >
      <div slot="title">
        <h6>Modifica password</h6>
      </div>
      <div>
        <el-alert
          v-show="showErrorMsg"
          type="error"
          :description="errorMsg"
          show-icon
          @close="resetErrorMsg"
        ></el-alert>
        <el-alert
          v-show="showSuccessMsg"
          type="success"
          :description="successMsg"
          show-icon
          @close="resetSuccessMsg"
        ></el-alert>
      </div>
      <form @submit.prevent>
        <div v-loading="loading">
          <div class="row">
            <div class="col-xs-12">
              <div class="form-group">
                <label for="oldPassword">{{ oldPasswordLabel }}</label>
                <input
                  name="oldPassword"
                  id="oldPassword"
                  v-model="oldPassword"
                  :type="passwordFieldType"
                  class="form-control input-no-border"
                  autocomplete="current-password"
                />
              </div>
            </div>
          </div>
          <div class="row">
            <div class="col-xs-12">
              <div class="form-group password">
                <label for="newPassword">{{ newPasswordLabel }}</label>
                <input
                  name="newPassword"
                  :type="passwordFieldType"
                  class="form-control input-no-border"
                  id="newPassword"
                  v-model="newPassword"
                  autocomplete="new-password"
                />
              </div>
            </div>
          </div>
          <div class="row align-items-bottom">
            <div class="col-xs-12">
              <div class="form-group password">
                <label for="newPasswordCheck">
                  {{ newPasswordCheckLabel }}
                </label>
                <input
                  name="newPasswordCheck"
                  :type="passwordFieldType"
                  class="form-control input-no-border"
                  id="newPasswordCheck"
                  v-model="newPasswordCheck"
                />
              </div>
            </div>
          </div>
          <div class="form-group text-right">
            <i class="ti-eye" :class="passwordFieldTypeClass"></i>
            {{ showPasswordLabel }}
            <el-switch
              v-model="passwordFieldType"
              inactive-value="password"
              active-value="text"
            ></el-switch>
          </div>
        </div>
        <div class="card-footer text-center">
          <button
            type="submit"
            class="btn btn-fill btn-wd"
            @click.stop.prevent="doSubmitChangePassword"
            :disabled="disabled"
          >
            {{ submitLabel }}
          </button>
        </div>
      </form>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common'
import ApiErrorHandler from '@/components/Mastercom/UIComponents/ApiErrorHandler.vue'
import { Dialog, Loading, Card, Alert, Switch } from 'element-ui'

import { mapActions } from 'vuex'
Vue.use(Card)
Vue.use(Alert)
Vue.use(Loading)
Vue.use(Dialog)
Vue.use(Switch)
export default {
  name: 'ChangePassword',
  components: {
    ApiErrorHandler
  },
  mixins: [Common],
  computed: {
    disabled() {
      return (
        this.oldPassword.length == 0 ||
        this.newPassword.length == 0 ||
        this.newPasswordCheck.length == 0 ||
        this.oldPassword == this.newPassword
      )
    },
    showErrorMsg() {
      return this.errorMsg !== ''
    },
    showSuccessMsg() {
      return this.successMsg !== ''
    }
  },
  props: {
    oldPasswordLabel: {
      type: String,
      default: 'Password attuale:'
    },
    newPasswordLabel: {
      type: String,
      default: 'Nuova password:'
    },
    newPasswordCheckLabel: {
      type: String,
      default: 'Conferma nuova password:'
    },
    oldPasswordPlaceholder: {
      type: String,
      default: 'Inserisci la password attuale'
    },
    newPasswordPlaceholder: {
      type: String,
      default: 'Inserisci la nuova password'
    },
    showPasswordLabel: {
      type: String,
      default: 'mostra/nascondi password'
    },
    submitLabel: {
      type: String,
      default: 'INVIA'
    }
  },
  data() {
    return {
      showDialog: false,
      passwordFieldType: 'password',
      oldPassword: '',
      newPassword: '',
      newPasswordCheck: '',
      errorMsg: '',
      successMsg: '',
      passwordFieldTypeClass: '',
      loading: false
    }
  },
  methods: {
    resetErrorMsg() {
      this.errorMsg = ''
    },
    resetSuccessMsg() {
      this.successMsg = ''
    },
    resetChangePassword() {
      this.oldPassword = ''
      this.newPassword = ''
      this.newPasswordCheck = ''
      this.resetErrorMsg()
      this.resetSuccessMsg()
      this.doHideChangePasswordWindow()
    },
    doSubmitChangePassword() {
      if (this.newPassword !== this.newPasswordCheck) {
        this.errorMsg =
          'Attenzione, la nuova password non coincide con quella di conferma'
        return
      }
      let data = {
        token: this.authObject.token,
        id_scuola: this.authObject.studente.id_scuola,
        old_password: this.oldPassword,
        new_password: this.newPassword
      }
      this.resetErrorMsg()
      this.resetSuccessMsg()
      this.loading = true
      this.submitChangePassword(data)
        .then(() => {
          this.successMsg = 'Password modificata con successo'
        })
        .catch(() => {
          this.errorMsg =
            'Attenzione, controlla che la password corrente sia corretta'
        })
        .finally(() => {
          this.loading = false
        })
    },
    switchPasswordFieldType() {
      if (this.passwordFieldType === 'password') {
        this.passwordFieldType = 'text'
        this.passwordFieldTypeClass = 'opaque'
      } else {
        this.passwordFieldType = 'password'
        this.passwordFieldTypeClass = ''
      }
    },
    ...mapActions({
      submitChangePassword: 'auth/changePassword'
    })
  }
}
</script>

<style lang="scss" scoped></style>
