<template>
  <el-popconfirm
    :confirm-button-text="$t('colloqui.btn_conferma_richiesta').toUpperCase()"
    :cancel-button-text="$t('colloqui.btn_annulla_richiesta').toUpperCase()"
    placement="left-start"
    icon="el-icon-info"
    icon-color="grey"
    popper-class="confirm-richiesta"
    :title="$t('colloqui.text_conferma_richiesta_colloquio').toUpperCase()"
    @confirm="doRichiestaColloquio"
  >
    <div slot="reference">
      <spinner :radius="51" class="download-spinner" v-if="spinner"></spinner>
      <button class="btn btn-fill btn-sm" :class="btnClass" v-else>
        <span :class="btnIcon">
          {{ $t('colloqui.btn_richiesta_colloquio_individuale') }}
        </span>
      </button>
    </div>
  </el-popconfirm>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common'
import Colloqui from '@/components/Mastercom/Mixins/Colloqui'
import { Spinner, Popconfirm, MessageBox } from 'element-ui'
Vue.use(Popconfirm)
export default {
  name: 'RichiestaColloquioBtn',
  mixins: [Common, Colloqui],
  components: {
    Spinner
  },
  computed: {
    btnClass() {
      return this.baseBtnClass[this.richiediColloquioState]
    },
    btnIcon() {
      if (this.icons.hasOwnProperty(this.richiediColloquioState) === true) {
        return this.icons[this.richiediColloquioState]
      } else {
        return this.baseBtnIcon[this.richiediColloquioState]
      }
    },
    spinner() {
      return this.richiediColloquioState === 'active'
      // return true
    }
  },
  data() {
    return {
      richiediColloquioState: 'idle',
      baseBtnClass: {
        idle: 'btn-primary',
        active: 'btn-warning',
        set: 'btn-success'
      },
      baseBtnIcon: {
        idle: 'ti-bell',
        active: 'ti-arrow-right',
        set: 'ti-check'
      }
    }
  },
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    icons: {
      type: Object,
      default: () => ({
        idle: 'ti-bell',
        active: 'ti-arrow-right',
        set: 'ti-check'
      })
    },
    idProfessore: {
      type: Number,
      default: 0
    }
  },
  methods: {
    doRichiestaColloquio() {
      this.richiediColloquioState = 'active'
      this.setIdProfessoreRichiesta(this.idProfessore)
      this.richiestaColloquio(this.authObject)
        .then(result => {
          this.setIdProfessoreRichiesta(null)
          this.richiediColloquioState = 'set'
          MessageBox.alert(
            this.$i18n
              .t('colloqui.text_conferma_richiesta_inviata')
              .toUpperCase()
          )
          return result
        })
        .catch(error => {
          this.downloadState = 'idle'
          this.setError(error.response)
        })
    }
  }
}
</script>

<style scoped lang="scss">
/deep/ .download-spinner {
  &.el-spinner {
    .el-spinner-inner {
      .path {
        stroke: rgba(200, 200, 200, 0.8);
      }
    }
  }
}

.downloadItem {
  cursor: pointer;
}
</style>
