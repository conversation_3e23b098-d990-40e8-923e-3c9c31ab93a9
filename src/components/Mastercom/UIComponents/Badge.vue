<template>
  <div
    class="badge"
    :class="{ mini: isMini, small: isSmall }"
    :style="{
      'background-color': _backgroundColor,
      'border-color': _borderColor,
      'border-width': _borderSize,
      'border-style': _borderStyle
    }"
  >
    <span :style="{ color: _textColor }">
      <slot></slot>
    </span>
  </div>
</template>

<script>
export default {
  props: {
    backgroundColor: {
      type: String,
      default: ''
    },
    mini: {
      type: Boolean,
      default: false
    },
    small: {
      type: Boolean,
      default: false
    },
    outline: {
      type: String,
      default: ''
    },
    borderWidth: {
      type: String,
      default: '2'
    }
  },
  computed: {
    _backgroundColor() {
      return this.backgroundColor
    },
    _borderColor() {
      return this.outline !== '' ? this.outline : 'none'
    },
    _borderSize() {
      return this.outline !== '' ? `${this.borderWidth}px` : 'none'
    },
    _borderStyle() {
      return this.outline !== '' ? 'solid' : 'none'
    },
    _textColor() {
      return this.outline !== '' ? `${this.outline}` : 'inherit'
    },
    isMini() {
      return this.mini
    },
    isSmall() {
      return this.small
    }
  }
}
</script>

<style lang="scss" scoped>
.badge {
  border-radius: 50%;
  width: 50px;
  height: 50px;
  span {
    color: #fff;
    font-size: 1.2em;
    font-weight: 700;
    line-height: 3em;
  }
  &.small {
    width: 35px;
    height: 35px;
    span {
      line-height: 2.4em;
      font-size: 1em;
    }
  }
  &.mini {
    width: 20px;
    height: 20px;
    padding: 0;
    span {
      line-height: 2em;
      font-size: 0.8em;
      text-align: center;
      width: 100%;
      display: inline-block;
    }
  }
}
</style>
