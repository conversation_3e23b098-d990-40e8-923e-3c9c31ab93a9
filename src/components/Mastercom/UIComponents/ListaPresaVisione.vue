<template>
  <div class="comunicazioni-detail">
    <div class="content" v-if="loading">in caricamento</div>
    <div class="body">
      <div class="container-fluid" v-if="destinatari">
        <div class="row header">
          <div class="col-xs-10">Nome</div>
          <div class="col-xs-2 text-center">Confermato</div>
        </div>
        <div class="row" v-for="(item, index) in confermati" :key="index">
          <div class="col-xs-10">{{ item }}</div>
          <div class="col-xs-2 text-center"><i class="ti-check"></i></div>
        </div>
        <div class="row" v-for="(item, index) in daConfermare" :key="index">
          <div class="col-xs-10">{{ item }}</div>
          <div class="col-xs-2 text-center"><i class="ti-alert"></i></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Card, Table, TableColumn } from 'element-ui'
Vue.use(Card)
Vue.use(Table)
Vue.use(TableColumn)
export default {
  name: 'ListaPresaVisione',
  data() {
    return {}
  },
  computed: {
    confermati() {
      return this.destinatari.confermati
    },
    daConfermare() {
      return this.destinatari.da_confermare
    }
  },
  props: {
    destinatari: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style lang="scss" scoped>
.el-dialog__body {
  margin-top: 0;
  padding-top: 0;
}
.row {
  border-bottom: 1px solid #e0e0e0;
  padding: 10px 0;
  &.header {
    font-weight: 700;
    border-bottom: 0;
  }
}
</style>
