<template>
  <div class="row">
    <div class="col-xs-12">
      <div class="card">
        <div class="card-header">
          <h4 class="card-title">{{ title }}</h4>
        </div>
        <div class="card-content">
          <el-collapse>
            <el-collapse-item
              v-for="item in items"
              :key="item.id"
              :title="item.titolo"
            >
              <div v-html="item.dettaglio"></div>
            </el-collapse-item>
          </el-collapse>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import { Collapse, CollapseItem } from 'element-ui'
Vue.use(Collapse)
Vue.use(CollapseItem)
export default {
  props: {
    title: {
      type: String,
      default: ''
    },
    items: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      myHtml: '<h1>Titolo</h1><ul><li>aaaa</li><li>bbbb</li></ul>'
    }
  }
}
</script>

<style></style>
