<template>
  <div class="mastercom" id="documenti-main-panel">
    <div class="col-xs-12">
      <el-card class="box-card card">
        <div slot="header" class="row">
          <h6 class="col-xs-9">
            {{ $t('documenti.title') }}
          </h6>
        </div>
        <div class="card-content">
          <vue-tabs class="row">
            <v-tab
              :title="$t('documenti.didattici.title')"
              id="documenti_didattici"
              icon="ti-package"
            >
              <documenti-didattici-panel></documenti-didattici-panel>
            </v-tab>
            <v-tab
              :title="$t('documenti.amministrativi.title')"
              id="documenti_amministrativi"
              icon="ti-files"
            >
              <pagamenti-report-panel
                v-loading="panelLoading"
              ></pagamenti-report-panel>
            </v-tab>
          </vue-tabs>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common'

import PagamentiReportPanel from '@/components/Mastercom/UIComponents/Panels/PagamentiReportPanel'
import DocumentiDidatticiPanel from '@/components/Mastercom/UIComponents/Panels/DocumentiDidatticiPanel'

import VueTabs from 'vue-nav-tabs'
Vue.use(VueTabs)
export default {
  name: 'PagamentiPanel',
  components: {
    DocumentiDidatticiPanel,
    PagamentiReportPanel
  },
  mixins: [Common],
  data() {
    return {
      loading: false
    }
  }
}
</script>

<style lang="scss" scoped></style>
