<template>
  <div
    @click.stop="gotoAssenze()"
    class="clickable"
    v-if="studenteCorrente.servizi.assenze"
  >
    <mini-card card-color="orange">
      <h6 slot="header">Assenze da giustificare</h6>
      <div class="numbers" slot="content">
        {{ assenzeDisplay }}
      </div>
      <div slot="footer">
        <p class="category" v-if="loading">
          <spinner :radius="30"></spinner>
          Loading
        </p>
        <p class="category" v-else>
          <span class="ti-time"></span>
          {{ lastTimeLoaded | simpletime }}
        </p>
      </div>
    </mini-card>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common.js'
import MiniCard from '@/components/Mastercom/UIComponents/MiniCard'

import { mapGetters, mapActions } from 'vuex'
import { Spinner } from 'element-ui'

export default {
  name: 'AssenzePanel',
  mixins: [Common],
  components: {
    MiniCard,
    Spinner
  },
  data() {
    return {
      loading: false,
      lastTimeLoaded: null
    }
  },
  computed: {
    assenzeDisplay() {
      return this.assenze > 0 ? this.assenze : 0
    },
    ...mapGetters({
      assenze: 'assenze/daGiustificare',
      authObject: 'main/authObject'
    })
  },
  methods: {
    doLoadAssenze() {
      this.loading = true
      this.setProfileUpdate(false)
      this.loadAssenze(this.authObject)
        .then(() => {
          this.loading = false
          this.lastTimeLoaded = this.$dayjs()
        })
        .finally(this.setProfileUpdate(true))
    },
    gotoAssenze() {
      this.setOnlyNonGiustificate(true)
      this.$router.push('Assenze')
    },
    ...mapActions({
      setOnlyNonGiustificate: 'assenze/setOnlyNonGiustificate',
      setProfileUpdate: 'assenze/setProfileUpdate',
      loadAssenze: 'assenze/loadAssenze'
    })
  },
  mounted() {
    this.doLoadAssenze()
  }
}
</script>

<style></style>
