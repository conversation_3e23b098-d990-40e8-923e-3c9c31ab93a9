<template>
  <div class="mastercom" id="comunicazioni-main-panel">
    <div class="col-xs-12">
      <el-card class="box-card card">
        <div slot="header" class="row">
          <h6 class="col-xs-9">Pagamenti e Fatture</h6>
          <carrello class="col-xs-3" v-if="carrelloDisplay"></carrello>
        </div>
        <div class="card-content">
          <vue-tabs class="row">
            <v-tab title="Articoli" id="articoli" icon="ti-package">
              <articoli-panel></articoli-panel>
            </v-tab>
            <v-tab title="Pagamenti" id="pagamenti" icon="ti-wallet">
              <storico-panel v-loading="panelLoading"></storico-panel>
            </v-tab>
            <v-tab title="Fatture emesse" id="fatture" icon="ti-files">
              <fatture-panel v-loading="panelLoading"></fatture-panel>
            </v-tab>
            <v-tab
              title="Note di credito"
              id="note_credito"
              icon="ti-share-alt"
            >
              <fatture-panel
                v-loading="panelLoading"
                :ndc="true"
              ></fatture-panel>
            </v-tab>
            <v-tab title="Documenti" id="documenti" icon="ti-files">
              <pagamenti-report-panel
                v-loading="panelLoading"
              ></pagamenti-report-panel>
            </v-tab>
          </vue-tabs>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common'
import Pagamenti from '@/components/Mastercom/Mixins/Pagamenti'
import StoricoPanel from '@/components/Mastercom/UIComponents/Panels/StoricoPanel'
import FatturePanel from '@/components/Mastercom/UIComponents/Panels/FatturePanel'
import ArticoliPanel from '@/components/Mastercom/UIComponents/Panels/ArticoliPanel'
import PagamentiReportPanel from '@/components/Mastercom/UIComponents/Panels/PagamentiReportPanel'
import Carrello from '@/components/Mastercom/UIComponents/Carrello'
import VueTabs from 'vue-nav-tabs'
Vue.use(VueTabs)
export default {
  name: 'PagamentiPanel',
  components: {
    StoricoPanel,
    FatturePanel,
    ArticoliPanel,
    PagamentiReportPanel,
    Carrello
  },
  mixins: [Common, Pagamenti],
  data() {
    return {
      loading: false
    }
  }
}
</script>

<style lang="scss" scoped></style>
