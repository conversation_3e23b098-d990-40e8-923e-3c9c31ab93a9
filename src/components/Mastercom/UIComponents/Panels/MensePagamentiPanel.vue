<template>
  <div class="wrapper mastercom">
    <el-card class="box-card card" v-if="loaded">
      <div slot="header" class="row">
        <h6 class="col-xs-12">{{ $t('mense.crediti_panel_titolo') }}</h6>
        <div class="content" v-if="pagamenti.length">
          <div
            v-for="(item, index) in pagamenti"
            :key="index"
            class="col-xs-12 elemento"
          >
            <div class="col-xs-4 importo">
              {{ item.importo }}
            </div>
            <div class="col-xs-8">
              <h5>{{ item.descrizione }}</h5>
              <div class="descrizione" v-if="item.debito">
                <span v-if="item.pagato">
                  {{
                    $t('mense.pagamento_effettuato', [
                      item.pagante,
                      item.metodo
                    ])
                  }}
                </span>
                <span v-else-if="item.pagato_parzialmente">
                  {{
                    $t('mense.pagamento_parziale', [
                      item.pagante,
                      item.metodo,
                      item.da_pagare
                    ])
                  }}
                </span>
                <span v-else>
                  {{ $t('mense.pagamento_da_eseguire') }}
                </span>
              </div>
              <div class="descrizione" v-if="item.credito">
                <i class="ti-back-left"></i>
                {{ $t('mense.rimborso', [item.pagante, item.metodo]) }}
              </div>
              <h6 v-if="item.data">
                <i class="ti-time"></i>
                {{ item.data | simpledate }}
              </h6>
            </div>
          </div>
        </div>
        <div class="content col-xs-12" v-else>Nessun dato presente</div>
      </div>
    </el-card>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common.js'
import Mense from '@/components/Mastercom/Mixins/Mense.js'

import { Card } from 'element-ui'

Vue.use(Card)

export default {
  name: 'MensePagamentiPanel',
  mixins: [Common, Mense],
  data() {
    return {
      loaded: false
    }
  },
  mounted() {
    this.doLoadMensePagamenti()
  },
  methods: {
    doLoadMensePagamenti() {
      this.loadMensePagamenti(this.authObject).then(() => (this.loaded = true))
    }
  }
}
</script>

<style lang="scss" scoped>
.elemento {
  border-bottom: 1px solid #f8f8f8;
  padding-bottom: 10px;
  margin-top: 10px;
  .importo {
    font-weight: 700;
    font-size: 1.4em;
  }
}
</style>
