<template>
  <div class="table-list col-xs-12" v-loading="panelLoading">
    <div class="row storico-list table-header">
      <div class="col-xs-7">{{ $t('pagamenti.label_descrizione') }}</div>
      <div class="col-xs-2">{{ $t('pagamenti.label_importo') }}</div>
      <div class="col-xs-3"></div>
    </div>
    <fade-transition group tag="div" class="" :duration="250">
      <div
        class="row storico-list"
        v-for="(item, index) in marketplaceDisplay"
        :key="`marketplace_${index}`"
      >
        <div class="col-xs-7">
          <span v-if="item.tipo == 'movimento'">{{ item.sottotitolo }}</span>
          <span v-else>{{ item.titolo }}</span>
        </div>
        <div class="col-xs-2 currency">
          {{ item.prezzo | currency }}
          <span v-if="item.da_pagare" class="sub-line">
            ({{ $t('pagamenti.prefix_residuo') }} {{ item.prezzo }})
          </span>
        </div>
        <div class="col-xs-3 text-center" v-if="carrelloDisplay">
          <button
            class="btn btn-sm btn-success"
            @click="removeFromCart(item.cart_id)"
            v-if="itemInCart(item.cart_id)"
          >
            <i class="ti-close"></i>
            {{ $t('pagamenti.carrello.label_rimuovi') }}
          </button>
          <button
            class="btn btn-sm btn-primary"
            @click="addToCart(item.cart_id)"
            v-else
          >
            <i class="ti-shopping-cart"></i>
            {{ $t('pagamenti.carrello.label_aggiungi') }}
          </button>
        </div>
        <div class="col-xs-3 no-payment" v-else>
          {{ $t('pagamenti.carrello.no_payment') }}
        </div>
      </div>
    </fade-transition>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common'
import Pagamenti from '@/components/Mastercom/Mixins/Pagamenti'
import { Loading } from 'element-ui'

Vue.use(Loading)
export default {
  name: 'MarketplacePanel',
  mixins: [Common, Pagamenti],
  data() {
    return {}
  },
  mounted() {
    this.doLoadMarketplace()
  }
}
</script>

<style lang="scss" scoped>
.no-payment {
  text-align: center;
}
.storico-list {
  padding: 10px 0;
}
</style>
