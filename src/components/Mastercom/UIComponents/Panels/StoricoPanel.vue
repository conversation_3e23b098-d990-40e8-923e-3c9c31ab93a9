<template>
  <div class="table-list col-xs-12" v-loading="panelLoading">
    <div class="row storico-list table-header">
      <div class="col-xs-2">Data</div>
      <div class="col-xs-2">N. Fatt.</div>
      <div class="col-xs-3">Descrizione</div>
      <div class="col-xs-2">Importo</div>
      <div class="col-xs-3"></div>
    </div>
    <fade-transition group tag="div" class="" :duration="250">
      <div
        class="row storico-list"
        v-for="(item, index) in storicoDisplay"
        :key="`storico_${index}`"
      >
        <div class="col-xs-2">{{ item.data | simpledate }}</div>
        <div class="col-xs-2">{{ item.fattura }}</div>
        <div class="col-xs-3">
          {{ item.descrizione }}
        </div>
        <div class="col-xs-2 currency">
          {{ item.importo }}
          <span v-if="item.da_pagare" class="sub-line">
            (da pagare {{ item.da_pagare }})
          </span>
        </div>
        <div class="col-xs-3" v-if="item.pagamenti.length">
          <div
            class="row inner-detail"
            v-for="(pitem, px) in item.pagamenti"
            :key="`pagamenti_${px}`"
          >
            <div class="col-xs-6 col-lg-4">
              {{ pitem.operation_date | simpledate }}
            </div>
            <div class="col-xs-6 col-lg-4 currency">{{ pitem.amount }}</div>
            <div class="col-xs-12 col-lg-4">
              {{ pitem.payer_surname }}
              {{ pitem.payer_name }}
            </div>
          </div>
        </div>
        <div class="col-xs-3" v-else>
          <span v-if="item.debito">Eseguito da</span>
          <span v-if="item.credito">
            <i class="ti-back-left"></i>
            A favore di
          </span>
          {{ item.pagante }}
        </div>
      </div>
    </fade-transition>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common'
import Pagamenti from '@/components/Mastercom/Mixins/Pagamenti'
import { Loading } from 'element-ui'
Vue.use(Loading)
export default {
  name: 'StoricoPanel',
  mixins: [Common, Pagamenti],
  data() {
    return {}
  },
  mounted() {
    this.doLoadStorico()
  }
}
</script>

<style lang="scss" scoped></style>
