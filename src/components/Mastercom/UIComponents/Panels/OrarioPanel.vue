<template>
  <div>
    <el-card class="box-card card">
      <div slot="header" class="row">
        <h6 class="col-xs-10">ORARIO</h6>
        <div class="col-xs-2 text-right">
          <slot name="header-actions"></slot>
        </div>
        <div class="col-sm-4 col-lg-4">
          <date-picker
            v-model="currentDay"
            type="date"
            placeholder="Seleziona un giorno"
            :picker-options="PickerOptions"
            format="ddd, dd MMMM yyyy"
            @change="doLoadCurrentDay()"
            :clearable="false"
            :editable="false"
            size="mini"
          ></date-picker>
        </div>
        <div class="col-xs-4 col-sm-4 col-md-3 col-lg-2 pull-right text-right">
          <button
            @click.stop="doLoadPreviousDay"
            type="button"
            class="btn btn-default btn-simple btn-icon btn-xs"
          >
            <span class="ti-angle-left"></span>
          </button>
          <button
            @click.stop="doLoadNextDay"
            type="button"
            class="btn btn-default btn-simple btn-icon btn-xs"
          >
            <span class="ti-angle-right"></span>
          </button>
        </div>
        <div class="clearfix"></div>
      </div>

      <div class="content my-20 bb-3" v-if="loaded && orario.length">
        <div class="row my-1">
          <div class="col-xs-5 col-sm-2 col-lg-2 text-center">
            <span class="ti-time"></span>
            Orario
          </div>
          <div class="col-xs-7 col-sm-5 col-lg-5">
            <span class="ti-book"></span>
            Materia
          </div>
          <div
            class="col-xs-7 col-sm-2 col-lg-2 text-center"
            v-if="studenteCorrente.servizi.compiti"
          >
            <span class="ti-pencil"></span>
            Compiti
          </div>
          <div
            class="col-xs-7 col-sm-3 col-lg-3 text-center"
            v-if="studenteCorrente.servizi.argomenti"
          >
            <span class="ti-light-bulb"></span>
            Argomenti
          </div>
        </div>

        <div
          class="row my-2 item-row"
          v-for="(item, index) in displayOrario"
          :key="index"
        >
          <div class="col-xs-5 col-sm-2 col-lg-2">
            <p class="category text-center">
              {{ item.data_ora_inizio | simpletime }}
              <br />
              -
              <br />
              {{ item.data_ora_fine | simpletime }}
            </p>
          </div>
          <div class="col-xs-7 col-sm-5 col-lg-5">
            <section v-if="item.titolo !== 'MATERIA NON DEFINITA'">
              <materia
                :titolo="item.materia"
                :indice-colore="item.indice_colore"
              ></materia>
              <insegnante :nome="item.insegnante"></insegnante>
            </section>
            <section v-else>
              <materia
                :titolo="item.titolo"
                :indice-colore="item.indice_colore"
              ></materia>
            </section>
          </div>
          <div class="col-sm-2 col-lg-2">
            <transition
              name="fade-zoom-in"
              v-if="studenteCorrente.servizi.compiti"
            >
              <el-popover
                v-if="'compiti' in item && item.compiti.length"
                trigger="click"
                width="400"
                placement="left"
                title="Compiti"
                popper-class="mastercom-popover popover-compiti"
              >
                <div class="row">
                  <div
                    class="col-xs-12 popover-content"
                    v-for="(compito, compitiIndex) in item.compiti"
                    :key="`compiti_${index}_${compitiIndex}`"
                    v-html="compito.titolo"
                  ></div>
                </div>

                <button
                  class="btn btn-info btn-fill btn-icon btn-sm center-block"
                  slot="reference"
                >
                  <span class="ti-pencil"></span>
                </button>
              </el-popover>
            </transition>
          </div>
          <div class="col-sm-3 col-lg-3">
            <transition
              name="fade-zoom-in"
              v-if="studenteCorrente.servizi.argomenti"
            >
              <el-popover
                v-if="'argomenti' in item && item.argomenti.length"
                trigger="click"
                width="400"
                placement="left"
                title="Argomenti"
                popper-class="mastercom-popover popover-argomenti"
              >
                <div class="row">
                  <div
                    class="col-xs-12 popover-content"
                    v-for="(argomento, argomentiIndex) in item.argomenti"
                    :key="`argomenti_${index}_${argomentiIndex}`"
                    v-html="`${argomento.modulo}<br>${argomento.dettaglio}`"
                  ></div>
                </div>
                <button
                  class="btn btn-warning btn-fill btn-icon btn-sm center-block"
                  slot="reference"
                >
                  <span class="ti-light-bulb"></span>
                </button>
              </el-popover>
            </transition>
          </div>
        </div>
      </div>
      <div class="content" v-else-if="loaded">
        <h1 class="text-muted">
          <span class="ti-home"></span>
          Giorno festivo
        </h1>
      </div>
      <div class="content" v-else>
        <h1 class="text-muted">
          <spinner></spinner>
          Loading
        </h1>
      </div>
    </el-card>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common.js'

import Materia from '@/components/Mastercom/UIComponents/Materia.vue'
import Insegnante from '@/components/Mastercom/UIComponents/Insegnante.vue'

import { mapGetters, mapActions } from 'vuex'
import { Card, Popover, DatePicker, Spinner } from 'element-ui'

Vue.use(Card)
Vue.use(Popover)

export default {
  name: 'OrarioPanel',
  mixins: [Common],
  components: {
    Materia,
    Insegnante,
    DatePicker,
    Spinner
  },
  data() {
    return {
      loaded: false,
      currentDay: null
    }
  },
  computed: {
    PickerOptions() {
      let me = this
      return {
        firstDayOfWeek: 1,
        disabledDate: function(d) {
          return !me.dataAnnoScolastico(d)
        }
      }
    },
    displayOrario() {
      let result = []
      for (let [index, item] of this.orario.entries()) {
        let compiti = this.compiti.filter(c => {
          return c.id_materia === item.id_materia
        })

        item['compiti'] = compiti

        let argomenti = this.argomenti.filter(a => {
          return a.id_materia === item.id_materia
        })

        item['argomenti'] = argomenti
        if (index === 0) {
          result.push(item)
        } else {
          if (item.id_materia === [...result].pop().id_materia) {
            result[result.length - 1].data_ora_fine = item.data_ora_fine
          } else {
            result.push(item)
          }
        }
      }
      return result
    },
    ...mapGetters({
      orario: 'orario/all',
      compiti: 'compiti/all',
      argomenti: 'argomenti/all'
    })
  },
  methods: {
    doLoadCurrentDay() {
      this.currentDay = this.$dayjs(this.currentDay)
      this.doLoadOrario({
        data_inizio: this.currentDay.format('YYYY-MM-DD'),
        data_fine: this.currentDay.format('YYYY-MM-DD')
      })
    },
    doLoadPreviousDay() {
      this.currentDay = this.currentDay.subtract(1, 'days')
      this.doLoadOrario({
        data_inizio: this.currentDay.format('YYYY-MM-DD'),
        data_fine: this.currentDay.format('YYYY-MM-DD')
      })
    },
    doLoadNextDay() {
      this.currentDay = this.currentDay.add(1, 'days')
      this.doLoadOrario({
        data_inizio: this.currentDay.format('YYYY-MM-DD'),
        data_fine: this.currentDay.format('YYYY-MM-DD')
      })
    },
    doLoadOrario(data = null) {
      this.loaded = false
      if ('data_inizio' in data) {
        this.setOrarioDataInizio(data.data_inizio)
        this.setCompitiDataInizio(data.data_inizio)
        this.setArgomentiDataInizio(data.data_inizio)
      }
      if ('data_fine' in data) {
        this.setOrarioDataFine(data.data_fine)
        this.setCompitiDataFine(data.data_fine)
        this.setArgomentiDataFine(data.data_fine)
      }
      this.loadOrario(this.authObject)
        .then(() => {
          this.clearCompiti()
          this.loadCompiti(this.authObject)
        })
        .then(() => {
          this.clearArgomenti()
          this.loadArgomenti(this.authObject)
        })
        .then(() => {
          this.loaded = true
        })
        .catch(error => {
          this.setError(error)
        })
    },
    ...mapActions({
      loadOrario: 'orario/loadOrario',
      loadCompiti: 'compiti/loadCompiti',
      clearCompiti: 'compiti/clearCompiti',
      loadArgomenti: 'argomenti/loadArgomenti',
      clearArgomenti: 'argomenti/clearArgomenti',
      setArgomentiDataInizio: 'argomenti/setDataInizio',
      setArgomentiDataFine: 'argomenti/setDataFine',
      setOrarioDataInizio: 'orario/setDataInizio',
      setOrarioDataFine: 'orario/setDataFine',
      setCompitiDataInizio: 'compiti/setDataInizio',
      setCompitiDataFine: 'compiti/setDataFine'
    })
  },
  mounted() {
    this.currentDay = this.today
    this.doLoadOrario(this.filter.today)
  }
}
</script>

<style scoped lang="scss">
.btn-w110 {
  width: 110px;
}

.fade-zoom-in-enter-active {
  transition: all 0.2s ease;
}
.fade-zoom-in-leave-active {
  transition: all 0.5s ease;
}
.fade-zoom-in-enter, .fade-zoom-in-leave-to
/* .fade-zoom-in-leave-active below version 2.1.8 */ {
  opacity: 0;
}
</style>
