<template>
  <mini-card card-color="brown">
    <h6 slot="header">{{ $t('mense.total_lunches') }}</h6>
    <div class="numbers" slot="content">
      {{ pastiConsumati }}
    </div>
  </mini-card>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common.js'
import Mense from '@/components/Mastercom/Mixins/Mense.js'
import MiniCard from '@/components/Mastercom/UIComponents/MiniCard'

export default {
  name: 'PastiConsumatiPanel',
  mixins: [Common, Mense],
  components: {
    MiniCard
  }
}
</script>

<style lang="scss" scoped>
.card {
  .numbers {
    font-size: 1.4em;
  }
}
</style>
