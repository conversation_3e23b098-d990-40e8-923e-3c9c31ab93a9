<template>
  <div class="table-list col-xs-12" v-loading="panelLoading">
    <div class="row storico-list table-header">
      <div class="col-xs-2">
        {{ $t('pagamenti.prefix_scadenza') }}
      </div>
      <div class="col-xs-5">{{ $t('pagamenti.label_descrizione') }}</div>
      <div class="col-xs-2">{{ $t('pagamenti.label_importo') }}</div>
      <div class="col-xs-2"></div>
      <div class="col-xs-1"></div>
    </div>
    <fade-transition group tag="div" class="" :duration="250">
      <div
        class="row storico-list"
        :class="item.style"
        v-for="(item, index) in estrattoContoDisplay"
        :key="`estratto_conto_${index}`"
      >
        <div class="col-xs-2">
          <i class="ti-alert" v-if="item.scaduto"></i>
          {{ item.data_scadenza | simpledate }}
        </div>
        <div class="col-xs-5">
          <span v-if="item.tipo == 'movimento'">{{ item.sottotitolo }}</span>
          <span v-else>{{ item.titolo }}</span>
          <div class="col-xs-12" v-if="item.descrizione_sconto">
            {{ item.descrizione_sconto }}
          </div>
        </div>
        <div class="col-xs-2 currency">
          <span v-if="item.descrizione_importo">
            {{ item.descrizione_importo }}
          </span>
          <span v-else>
            <span v-if="item.totale">
              {{ item.totale | currency }}
            </span>
            <span
              v-if="item.prezzo && item.totale > item.prezzo"
              class="sub-line residuo"
            >
              ({{ $t('pagamenti.prefix_residuo') }}
              {{ item.prezzo | currency }})
            </span>
          </span>
        </div>
        <div class="col-xs-2 text-center">
          <div v-if="item.acquistabile">
            <button
              class="btn btn-sm btn-success"
              @click="removeFromCart(item.cart_id)"
              v-if="itemInCart(item.cart_id)"
            >
              <i class="ti-close"></i>
              {{ $t('pagamenti.carrello.label_rimuovi') }}
            </button>
            <button
              class="btn btn-sm btn-primary"
              @click="addToCart(item.cart_id)"
              v-else
            >
              <i class="ti-shopping-cart"></i>
              {{ $t('pagamenti.carrello.label_paga') }}
            </button>
          </div>
          <div class="no-payment" v-else-if="item.pagato">
            {{ $t('pagamenti.carrello.paid') }}
          </div>
          <div class="no-payment" v-else>
            {{ $t('pagamenti.carrello.no_payment') }}
          </div>
        </div>
        <div class="col-xs-1 text-center">
          <download-item
            :url="item.allegati[0].allegato"
            :descrizione="item.allegati[0].descrizione"
            :mostradescrizione="false"
            v-if="item.allegati.length"
          ></download-item>
        </div>
      </div>
    </fade-transition>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common'
import Pagamenti from '@/components/Mastercom/Mixins/Pagamenti'
import DownloadItem from '@/components/Mastercom/UIComponents/DownloadItem'
import { Loading } from 'element-ui'

Vue.use(Loading)
export default {
  name: 'EstrattoContoPanel',
  mixins: [Common, Pagamenti],
  components: {
    DownloadItem
  },
  data() {
    return {}
  },
  mounted() {
    this.doLoadEstrattoConto()
  }
}
</script>

<style lang="scss" scoped>
.no-payment {
  text-align: center;
}
.scaduto {
  color: #aa0000;
}
.credito {
  color: #0066cc;
}
.pagato {
  color: #00aa00;
}
.residuo {
  font-weight: bold;
}

.storico-list {
  padding: 10px 0;
}
</style>
