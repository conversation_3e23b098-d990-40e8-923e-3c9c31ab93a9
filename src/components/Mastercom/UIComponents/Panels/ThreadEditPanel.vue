<template>
  <div
    class="container-fluid"
    id="message-edit-container"
    ref="messageEditContainer"
  >
    <div class="row">
      <div class="col-xs-12" id="form-container">
        <form action="" class="form-horizontal">
          <div
            class="form-group"
            ref="destinatariForm"
            v-if="showDestinatari()"
          >
            <label for="destinatariMessaggio" class="col-sm-2 control-label">
              A:
            </label>
            <div class="col-sm-10">
              <el-select
                v-model="destinatari"
                placeholder="a: "
                size="large"
                multiple
                filterable
                value-key="value"
                :collapse-tags="destinatariSelectCollapse"
              >
                <ul class="destinatariFilterPanel">
                  <li v-for="(gruppo, index) in gruppi" :key="index">
                    <button
                      class="btn btn-simple btn-icon"
                      :class="{ 'is-active': false }"
                      @click.stop.prevent="destinatariLocalFilter(gruppo)"
                    >
                      <account-group
                        v-if="
                          gruppo.toLowerCase() === 'gruppo' ||
                            gruppo.toLowerCase() === 'gruppi'
                        "
                      ></account-group>
                      <account
                        v-if="gruppo.toLowerCase() === 'amministratori'"
                      ></account>
                      <school
                        v-if="gruppo.toLowerCase() === 'professori'"
                      ></school>
                      <account-arrow-right
                        v-if="gruppo.toLowerCase() === 'destinatari'"
                      ></account-arrow-right>
                    </button>
                  </li>
                </ul>
                <section
                  v-for="gruppo in destinatariFiltrati"
                  :key="gruppo.label"
                >
                  <el-option-group
                    v-show="showGruppo(gruppo.label)"
                    :label="gruppo.label"
                  >
                    <section v-show="showGruppo(gruppo.label)">
                      <el-option
                        v-for="item in gruppo.options"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                        @queryChange="handleDestinatariQueryChange"
                      ></el-option>
                    </section>
                  </el-option-group>
                </section>
              </el-select>
              <small id="emailHelp" class="form-text text-muted">
                Seleziona uno o più destinatari
              </small>
            </div>
          </div>
          <div class="form-group" ref="oggetto">
            <label for="oggettoMessaggio" class="col-sm-2 control-label">
              Oggetto:
            </label>
            <div class="col-sm-10">
              <input
                v-model="oggettoMessaggio"
                type="email"
                class="form-control"
                id="oggettoMessaggio"
                aria-describedby="subjectHelp"
                placeholder="oggetto: "
                :disabled="objectDisabled()"
              />
              <small id="subjectHelp" class="form-text text-muted">
                Inserisci l'oggetto
              </small>
            </div>
          </div>
          <div class="form-group" id="form-group-presa-visione" ref="azioni">
            <label for="oggettoMessaggio" class="col-sm-2 control-label">
              Conferma lettura:
            </label>
            <div class="col-sm-10">
              <el-switch v-model="confermaLettura"></el-switch>
              <small id="subjectHelp" class="form-text text-muted">
                Richiedi conferma lettura
              </small>
            </div>
          </div>
          <div class="form-group" id="message-body" ref="messageBody">
            <label for="messageBody" class="col-sm-2 control-label">
              Messaggio:
            </label>
            <div class="col-sm-10" id="editor-container">
              <div class="editor-box">
                <editor-menu-bar
                  :editor="editor"
                  v-slot="{ commands, isActive, getMarkAttrs }"
                >
                  <div class="menubar" ref="editorMenuBar">
                    <div class="menu-group">
                      <button
                        class="btn btn-simple btn-icon"
                        :class="{ 'is-active': isActive.bold() }"
                        @click.stop.prevent="commands.bold"
                      >
                        <format-bold></format-bold>
                      </button>
                      <button
                        class="btn btn-simple btn-icon"
                        :class="{ 'is-active': isActive.italic() }"
                        @click.stop.prevent="commands.italic"
                      >
                        <format-italic></format-italic>
                      </button>
                      <button
                        class="btn btn-simple btn-icon"
                        :class="{ 'is-active': isActive.underline() }"
                        @click.stop.prevent="commands.underline"
                      >
                        <format-underline></format-underline>
                      </button>
                    </div>
                    <div class="menu-group">
                      <button
                        class="btn btn-simple btn-icon"
                        :class="{ 'is-active': isActive.link() }"
                        @click.stop.prevent="showLinkMenu(getMarkAttrs('link'))"
                      >
                        <format-link></format-link>
                        <div v-if="linkMenuIsActive">
                          <form
                            class="menububble__form"
                            @submit.prevent="setLinkUrl(commands.link, linkUrl)"
                          >
                            <input
                              class="menububble__input"
                              type="text"
                              v-model="linkUrl"
                              placeholder="https://"
                              ref="linkInput"
                              @keydown.esc="hideLinkMenu"
                            />
                            <button
                              class="menububble__button"
                              @click="setLinkUrl(commands.link, null)"
                              type="button"
                            >
                              <icon name="remove" />
                            </button>
                          </form>
                        </div>
                      </button>
                    </div>
                    <div class="menu-group">
                      <button
                        class="btn btn-simple btn-icon"
                        :class="{ 'is-active': isActive.bullet_list() }"
                        @click.stop.prevent="commands.bullet_list"
                      >
                        <format-list-bulleted></format-list-bulleted>
                      </button>
                      <button
                        class="btn btn-simple btn-icon"
                        :class="{ 'is-active': isActive.ordered_list() }"
                        @click.stop.prevent="commands.ordered_list"
                      >
                        <format-list-numbered></format-list-numbered>
                      </button>
                    </div>
                  </div>
                </editor-menu-bar>
                <editor-content
                  :editor="editor"
                  class="editor-textarea"
                  ref="editorTextarea"
                />
              </div>
            </div>
          </div>
          <div class="form-group" id="attachments" ref="attachments">
            <div class="col-sm-10 col-sm-offset-2">
              <add-attachment-thread
                @upload-start="handleAttachmentUploadStart"
                @upload-end="handleAttachmentUploadEnd"
                @upload-progress="handleAttachmentUploadProgress"
                @upload-removed="handleAttachmentUploadEnd"
              ></add-attachment-thread>
            </div>
          </div>
          <div class="form-group button-line" ref="pulsanti">
            <div class="col-sm-offset-10 col-sm-2 text-center">
              <button
                type="submit"
                class="btn btn-default"
                @click.stop.prevent="localSendThread"
              >
                <span :class="btnIcon"></span>
                <spinner v-if="spinner" :radius="25"></spinner>
                {{ btnText }}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common.js'
import AddAttachmentThread from '@/components/Mastercom/UIComponents/AddAttachmentThread'
import { Switch, Select, Option, OptionGroup, Spinner } from 'element-ui'
import { Editor, EditorContent, EditorMenuBar } from 'tiptap'
import { mapState, mapGetters, mapActions } from 'vuex'

import {
  Blockquote,
  Bold,
  Italic,
  Underline,
  BulletList,
  OrderedList,
  ListItem,
  Link
} from 'tiptap-extensions'

// import { getMarkAttrs } from 'tiptap-extensions'
import FormatBold from 'vue-material-design-icons/FormatBold'
import FormatItalic from 'vue-material-design-icons/FormatItalic'
import FormatUnderline from 'vue-material-design-icons/FormatUnderline'
import FormatListBulleted from 'vue-material-design-icons/FormatListBulleted'
import FormatListNumbered from 'vue-material-design-icons/FormatListNumbered'
import FormatLink from 'vue-material-design-icons/Link'

import AccountGroup from 'vue-material-design-icons/AccountGroup'
import School from 'vue-material-design-icons/School'
import Account from 'vue-material-design-icons/Account'
import AccountArrowRight from 'vue-material-design-icons/AccountArrowRight'

export default {
  name: 'ThreadEditPanel',
  mixins: [Common],
  components: {
    AddAttachmentThread,
    Spinner,
    EditorMenuBar,
    EditorContent,
    ElSwitch: Switch,
    ElSelect: Select,
    ElOption: Option,
    ElOptionGroup: OptionGroup,
    FormatBold,
    FormatItalic,
    FormatUnderline,
    FormatListBulleted,
    FormatListNumbered,
    FormatLink,
    AccountGroup,
    School,
    Account,
    AccountArrowRight
  },
  data() {
    return {
      spinner: false,
      btnIcon: '',
      btnText: 'Invia',
      btnTextSent: 'Messaggio inviato',
      destinatariSelectCollapse: false,
      destinatariFiltering: false,
      confermaLettura: false,
      oggettoMessaggio: '',
      linkUrl: '',
      linkMenuIsActive: false,
      window: {},
      destinatari: [],
      loadDestinatari: false,
      fileList: [],
      btnDisabled: false,
      editor: new Editor({
        content: '',
        extensions: [
          new Bold(),
          new Italic(),
          new Underline(),
          new BulletList(),
          new OrderedList(),
          new ListItem(),
          new Link(),
          new Blockquote()
        ]
      })
    }
  },
  computed: {
    destinatariDisplay() {
      let data = this.destinatariFiltrati
      let result = data.filter(item => {
        if (
          this.gruppoSelezionato === '' ||
          item.label === this.gruppoSelezionato
        ) {
          return true
        }
        return false
      })
      return result
    },
    gruppoSelezionato() {
      if (this._gruppoSelezionato !== '') {
        return this._gruppoSelezionato
      }
      return this.gruppi[0]
    },
    ...mapGetters({
      editorContent: 'messageEditor/content',
      destinatariFiltrati: 'comunicazioni/destinatariFiltrati',
      attachmentUploading: 'comunicazioni/attachment_uploading',
      thread: 'comunicazioni/dettaglio_thread',
      gruppi: 'comunicazioni/gruppi'
    }),
    ...mapState({
      _gruppoSelezionato: state => state.comunicazioni.gruppo_selezionato,
      risposta: state => state.comunicazioni.risposta
    })
  },
  beforeDestroy() {
    this.editor.destroy()
    this.resetMessageAttachments()
  },
  mounted() {
    this.getMessageBodyHeight()
    if ('id_destinatario' in this.risposta) {
      this.destinatari.push(this.risposta.id_destinatario)
    }
    if ('oggetto' in this.risposta) {
      this.oggettoMessaggio = this.risposta.oggetto
    }

    this.editor.setContent('')
    // if ('messaggio' in this.risposta) {
    //   this.editor.setContent(this.risposta.messaggio)
    // }
  },
  created() {
    window.addEventListener('resize', this.handleResize)
    // this.handleResize()
  },
  destroyed() {
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    doHandleFileChange(file, fileList) {
      this.fileList = fileList
      this.setFileList(fileList)
      this.$emit('file-change', fileList)
    },
    showDestinatari() {
      return Object.keys(this.risposta).length === 0
    },
    objectDisabled() {
      return Object.keys(this.risposta).length > 0
    },
    handleDestinatariQueryChange(data) {
      if (data.length) {
        this.destinatariFiltering = true
      } else {
        this.destinatariFiltering = false
      }
    },
    handleAttachmentUploadStart() {
      this.setAttachmentUploading(true)
    },
    handleAttachmentUploadEnd() {
      this.setAttachmentUploading(false)
      this.getMessageBodyHeight()
    },
    handleAttachmentUploadProgress() {
      this.getMessageBodyHeight()
    },
    localSendThread() {
      this.spinner = true
      this.btnIcon = ''
      if (this.thread) {
        if (this.destinatari.length > 0) {
          this.setDestinatariMessaggio(this.destinatari)
        }
        if (this.oggettoMessaggio !== '') {
          this.setOggetto(this.oggettoMessaggio)
        }
        this.setConfermaLettura(this.confermaLettura)
        this.setTesto(this.editor.getHTML())
        this.buildThreadPayload()
        this.sendThreadJson(this.authObject).then(() => {
          this.spinner = false
          this.btnIcon = 'ti-check'
          this.btnDisabled = true
          setTimeout(() => {
            this.$emit('message-sent')
          }, 800)
        })
      } else {
        if (this.destinatari.length > 0) {
          this.setDestinatariMessaggio(this.destinatari)
          this.setOggetto(this.oggettoMessaggio)
        }
        this.setConfermaLettura(this.confermaLettura)
        this.setTesto(this.editor.getHTML())
        this.buildThreadPayload()
        this.sendThreadJson(this.authObject).then(() => {
          this.spinner = false
          this.btnIcon = 'ti-check'
          this.btnDisabled = true
          setTimeout(() => {
            this.$emit('message-sent')
          }, 800)
        })
      }
    },
    filterDestinatari(query) {
      if (query !== '') {
        this.loadDestinatari = true
        setTimeout(() => {
          this.loadDestinatari = false
        }, 200)
      } else {
        this.options = []
      }
    },
    destinatariLocalFilter(q) {
      this.gruppoSelezionato === q
        ? this.setGruppoSelezionato('')
        : this.setGruppoSelezionato(q)
    },
    getMessageBodyHeight() {
      let destinatariHeight = 0
      try {
        destinatariHeight = this.$refs.destinatariForm.offsetHeight
      } catch {
        destinatariHeight = 0
      }
      try {
        let dialogHeight = this.$refs.messageEditContainer.offsetHeight
        let azioniHeight = this.$refs.azioni.offsetHeight
        let oggettoHeight = this.$refs.oggetto.offsetHeight
        let pulsantiHeight = this.$refs.pulsanti.offsetHeight
        let attachmentsHeight = this.$refs.attachments.offsetHeight
        let marginHeight = 15 * 5
        let messageBodyHeight =
          dialogHeight -
          (azioniHeight +
            oggettoHeight +
            destinatariHeight +
            pulsantiHeight +
            attachmentsHeight +
            marginHeight)

        this.$refs.messageBody.style.height = `${messageBodyHeight}px`
        let menuBarHeight = this.$refs.editorMenuBar.offsetHeight
        let editorTextAreaHeight = messageBodyHeight - menuBarHeight - 2
        this.$refs.editorTextarea.$el.style.height = `${editorTextAreaHeight}px`

        let select_inner = window.document.getElementsByClassName(
          'el-select__input'
        )[0]
        select_inner.style.maxWidth = '100% !important'
      } catch (e) {
        console.log('component resize error', e)
        return
      }
    },
    handleResize() {
      this.window.width = window.innerWidth
      this.window.height = window.innerHeight
      this.getMessageBodyHeight()
    },
    showGruppo(gruppo) {
      if (this.destinatariFiltering === true) {
        return true
      }
      return this.gruppoSelezionato === '' || gruppo === this.gruppoSelezionato
    },
    showLinkMenu(attrs) {
      this.linkUrl = attrs.href
      this.linkMenuIsActive = true
      this.$nextTick(() => {
        this.$refs.linkInput.focus()
      })
    },
    hideLinkMenu() {
      this.linkUrl = null
      this.linkMenuIsActive = false
    },
    setLinkUrl(command, url) {
      command({ href: url })
      this.hideLinkMenu()
      this.editor.focus()
    },
    ...mapActions({
      resetMessageAttachments: 'comunicazioni/resetMessageAttachments',
      setGruppoSelezionato: 'comunicazioni/setGruppoSelezionato',
      setDestinatariMessaggio: 'comunicazioni/setDestinatariMessaggio',
      setOggetto: 'comunicazioni/setOggetto',
      setTesto: 'comunicazioni/setTesto',
      setConfermaLettura: 'comunicazioni/setConfermaLettura',
      setAttachmentUploading: 'comunicazioni/setAttachmentUploading',
      sendThreadJson: 'comunicazioni/sendThreadJson',
      setFileList: 'comunicazioni/setFileList',
      buildThreadPayload: 'comunicazioni/buildThreadPayload'
    })
  }
}
</script>

<style lang="scss" scoped>
#message-edit-container {
  height: 100%;
  .row {
    height: 100%;
  }
}
#attachments {
  max-height: 120px;
}
#form-container {
  height: 100%;
  .form-horizontal {
    height: 100% !important;
  }
}

#editor-container {
  height: 100%;
}

.editor-box {
  border: 1px solid #e8e7e3;
  border-radius: 4px;
  color: #66615b;
  height: 100%;
}

.menubar {
  background-color: #f3f2ee;
}
.menu-group {
  display: inline-block;
  padding-right: 10px;
  margin-left: 10px;
  border-right: 1px solid #e8e7e3;
  background-color: #f3f2ee;
}

.editor-textarea {
  padding: 10px;

  overflow-y: auto;
  background: #fff;
  /deep/ .ProseMirror {
    height: 100%;
  }
}

.button-line {
  bottom: 0;
}
.el-select--large {
  width: 100%;
  max-width: 100% !important;
  background-color: #f3f2ee;

  border: 1px solid #e8e7e3;
  border-radius: 4px;
  max-height: 60px;
  overflow: hidden;
  /*
  &:active {
    background-color: #fff;
  }
  /deep/ .el-select {
    &:focus {
      background-color: #fff;
    }
  }
  */
  /deep/ .el-select__tags {
    max-width: 100% !important;
    max-height: 60px;
    overflow-y: auto;
  }
  &:focus-within {
    /deep/ .el-input__inner {
      background-color: #fff;
    }
  }
  /deep/ .el-input__inner {
    background-color: #f3f2ee;
    border: 0 !important;
  }

  /deep/ .el-tag--info {
    color: #fff;
  }
  /deep/ .el-input__suffix {
    display: none;
  }

  /deep/ .el-select__input {
    max-width: 100% !important;
  }

  /deep/ .el-select-dropdown__list {
    padding-top: 0;
  }
}

.destinatariFilterPanel {
  background-color: #f3f2ee;
  padding-left: 20px;
  li {
    display: inline;
  }
}
#form-group-presa-visione {
  .control-label {
    padding-top: 0 !important;
  }
}
</style>
