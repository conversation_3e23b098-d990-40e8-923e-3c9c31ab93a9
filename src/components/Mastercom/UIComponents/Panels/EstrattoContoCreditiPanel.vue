<template>
  <div>
    <el-collapse v-model="creditoAttivo" @change="handleChange">
      <el-collapse-item
        v-for="(item, index) in estrattoContoCreditiDisplay"
        :key="`estratto_conto_crediti_${index}`"
        :name="`${index}`"
      >
        <template slot="title">
          <div class="flex-row">
            <div class="left-item">{{ item.nome }}</div>
            <div class="right-item">
              <h6>{{ $t('pagamenti.estratto_conto_crediti.saldo') }}</h6>
              {{ item.saldo_formatted }}
            </div>
          </div>
        </template>
        <div class="table-list">
          <div class="flex-row row ec_titles">
            <div class="item item-data title">{{ $t('main.date') }}</div>
            <div class="item item-tipo title">
              {{ $t('pagamenti.estratto_conto_crediti.tipo') }}
            </div>
            <div class="item item-desc title">{{ $t('main.description') }}</div>
            <div class="item item-importo title">
              {{ $t('pagamenti.label_importo') }}
            </div>
          </div>
          <div
            class="flex-row row"
            :class="`${ec_item.css_riga}`"
            v-for="(ec_item, ec_index) in item.estratto_conto"
            :key="`ec_${index}_${ec_index}`"
          >
            <div class="item item-data">{{ ec_item.data | simpledate }}</div>
            <div class="item item-tipo">
              {{
                $t(
                  `pagamenti.estratto_conto_crediti.tipo_${ec_item.tipo_esteso}`
                )
              }}
            </div>
            <div class="item item-desc">
              {{ ec_item.descrizione }}
            </div>
            <div class="item item-importo">{{ ec_item.importo }}</div>
          </div>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common'
import Pagamenti from '@/components/Mastercom/Mixins/Pagamenti'
import { Loading, Collapse, CollapseItem } from 'element-ui'

Vue.use(Loading)
Vue.use(Collapse)
Vue.use(CollapseItem)
export default {
  name: 'EstrattoContoCreditiPanel',
  mixins: [Common, Pagamenti],
  data() {
    return {
      creditoAttivo: 1
    }
  },
  methods: {
    handleChange() {
      return
    }
  }
}
</script>

<style lang="scss" scoped>
.flex-row {
  display: flex;
  flex-direction: row;
  width: 100%;
  align-items: center;
  justify-content: center;
  &:nth-child(even) {
    background-color: #fff;
  }
  &.avere {
    .item {
      color: red;
    }
  }

  &.dare {
    .item {
      color: #282828;
    }
  }
  .left-item {
    width: 49%;
    color: #666;
  }
  .right-item {
    width: 49%;
    color: #666;
    h6 {
      margin-bottom: 0px;
    }
  }
  .label {
    font-size: 0.7em;
    width: 100%;
  }
  .ec_titles {
    padding-top: 25px;
    font-size: 1.1em;
  }
  .item {
    text-align: center;
    font-size: 1em;
    padding: 0.5vh 1vh;
    justify-content: center;
    &.title {
      font-weight: 700;
      text-transform: capitalize;
    }
    &.item-data {
      width: 15%;
    }
    &.item-tipo {
      width: 15%;
      text-align: left;
    }
    &.item-desc {
      width: 55%;
      text-align: left;
    }
    &.item-importo {
      width: 15%;
    }
  }
}
</style>
