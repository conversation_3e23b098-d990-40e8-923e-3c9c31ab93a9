<template>
  <div class="table-list col-xs-12" v-loading="panelLoading">
    <div class="row fatture-list table-header">
      <div class="col-xs-2">{{ $t('main.date') }}</div>
      <div class="col-xs-7">{{ $t('main.description') }}</div>
      <div class="col-xs-1">{{ $t('main.type') }}</div>
      <div class="col-xs-2">{{ $t('main.attachment') }}</div>
    </div>
    <fade-transition
      group
      tag="div"
      class=""
      :duration="250"
      v-if="docDisplay.length"
    >
      <div
        class="row fatture-list"
        v-for="(item, index) in docDisplay"
        :key="`report_${index}`"
      >
        <div class="col-xs-2 text-center">{{ item.data_ora | simpledate }}</div>
        <div class="col-xs-7">{{ item.descrizione }}</div>
        <div class="col-xs-1 text-center">{{ item.tipo }}</div>
        <div class="col-xs-2 text-center">
          <download-item
            :url="item.allegati[0].allegato"
            :descrizione="item.allegati[0].descrizione"
            :mostradescrizione="false"
          ></download-item>
        </div>
      </div>
    </fade-transition>
    <div v-else>
      <div class="row">
        <div class="col-xs-12">
          {{ $t('main.no_content') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common'
import Pagamenti from '@/components/Mastercom/Mixins/Pagamenti'
import DownloadItem from '@/components/Mastercom/UIComponents/DownloadItem'
import { Loading } from 'element-ui'
Vue.use(Loading)
export default {
  name: 'PagamentiReportPanel',
  mixins: [Common, Pagamenti],
  computed: {
    docDisplay() {
      return this.reportDisplay
    }
  },
  components: {
    DownloadItem
  },
  data() {
    return {}
  },
  mounted() {
    this.doLoadReport()
  }
}
</script>
