<template>
  <div class="wrapper mastercom">
    <el-card class="box-card card">
      <div slot="header" class="row">
        <h6 class="col-xs-12">{{ $t('mense.consumed_lunches') }}</h6>
        <button
          class="btn btn-icon btn-simple"
          style="position: absolute; right: 10px; top: 10px"
          @click.stop="pastiFruitiVisible = !pastiFruitiVisible"
        >
          <i class="ti-eye" v-if="false"></i>
        </button>
      </div>
      <div class="content" v-if="loading">{{ $t('main.loading') }}</div>
      <div class="content" v-else>
        <el-table
          :data="elencoPastiFruiti"
          height="382"
          style="width: 100%"
          v-if="pastiFruitiVisible"
        >
          <el-table-column
            prop="data"
            min-width="30"
            :label="$t('main.date')"
          ></el-table-column>
          <el-table-column
            prop="titolo"
            min-width="70"
            :label="$t('main.detail')"
          ></el-table-column>
        </el-table>
      </div>
    </el-card>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common.js'
import Mense from '@/components/Mastercom/Mixins/Mense.js'

import { Card, Table, TableColumn } from 'element-ui'
Vue.use(Card)
Vue.use(Table)
Vue.use(TableColumn)
export default {
  name: 'ElencoPastiFruiti',
  mixins: [Common, Mense],
  data() {
    return {
      loading: false,
      pastiFruitiVisible: true
    }
  },
  mounted() {
    this.doLoadPastiFruiti()
  },
  methods: {
    doLoadPastiFruiti() {
      this.loading = true
      this.loadPastiFruiti(this.authObject).then(() => (this.loading = false))
    }
  }
}
</script>

<style lang="scss" scoped></style>
