<template>
  <div class="table-list col-xs-12" v-loading="panelLoading">
    <div class="row storico-list table-header">
      <div class="col-xs-2">Da pagare entro</div>
      <div class="col-xs-5">Descrizione</div>
      <div class="col-xs-2">Importo</div>
      <div class="col-xs-3"></div>
    </div>
    <fade-transition group tag="div" class="" :duration="250">
      <div
        class="row storico-list"
        v-for="(item, index) in articoliDisplay"
        :key="`articoli_${index}`"
      >
        <div class="col-xs-2">{{ item.data_scadenza | simpledate }}</div>
        <div class="col-xs-5">
          <span v-if="item.tipo == 'movimento'">{{ item.sottotitolo }}</span>
          <span v-else>{{ item.titolo }}</span>
        </div>
        <div class="col-xs-2 currency">
          {{ item.prezzo | currency }}
          <span v-if="item.da_pagare" class="sub-line">
            (da pagare {{ item.da_pagare }})
          </span>
        </div>
        <div class="col-xs-3" v-if="carrelloDisplay">
          <button
            class="btn btn-sm btn-success"
            @click="removeFromCart(item.id)"
            v-if="itemInCart(item.id)"
          >
            <i class="ti-close"></i>
            Rimuovi dal carrello
          </button>
          <button
            class="btn btn-sm btn-primary"
            @click="addToCart(item.id)"
            v-else
          >
            <i class="ti-shopping-cart"></i>
            Aggiungi al carrello
          </button>
        </div>
        <div class="col-xs-3 no-payment" v-else>
          utilizzare il metodo di pagamento concordato con la scuola
        </div>
      </div>
    </fade-transition>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common'
import Pagamenti from '@/components/Mastercom/Mixins/Pagamenti'
import { Loading } from 'element-ui'

Vue.use(Loading)
export default {
  name: 'ArticoliPanel',
  mixins: [Common, Pagamenti],
  data() {
    return {}
  },
  mounted() {
    this.doLoadArticoli()
  }
}
</script>

<style lang="scss" scoped>
.no-payment {
  text-align: center;
}
</style>
