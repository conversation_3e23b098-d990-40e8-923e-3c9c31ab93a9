<template>
  <mini-card card-color="orange">
    <div class="col-xs-12" slot="header">
      <h6 class="col-xs-6">{{ $t('mense.total_lunches') }}</h6>
      <h6 class="col-xs-6">{{ $t('mense.residual_credit') }}</h6>
    </div>
    <div class="col-xs-12" slot="content">
      <div class="numbers col-xs-6">
        {{ pastiConsumati }}
      </div>
      <div class="numbers col-xs-6">
        {{ creditoResiduo }}
      </div>
    </div>
    <p slot="footer" class="footer">
      {{ $t('mense.panel_footer') }}
    </p>
  </mini-card>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common.js'
import Mense from '@/components/Mastercom/Mixins/Mense.js'
import MiniCardHorizontal from '@/components/Mastercom/UIComponents/MiniCardHorizontal'

export default {
  name: 'CreditoResiduoPanel',
  mixins: [Common, Mense],
  components: {
    MiniCard: MiniCardHorizontal
  }
}
</script>

<style lang="scss" scoped>
.card {
  h6 {
    text-align: center;
  }
  .numbers {
    font-size: 1.4em;
    font-weight: bold;
    text-align: center;
  }
  .footer {
    text-align: center;
    margin-bottom: 0;
    font-size: 0.8em;
  }
}
</style>
