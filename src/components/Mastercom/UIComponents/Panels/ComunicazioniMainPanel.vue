<template>
  <div class="mastercom" id="comunicazioni-main-panel">
    <div class="col-xs-12">
      <el-card class="box-card card">
        <div slot="header" class="row">
          <h6 class="col-xs-9">
            {{ $t('comunicazioni.title') }} {{ cAnnoScolasticoComunicazioni }}
          </h6>
          <div class="col-xs-3 text-right">
            <button
              class="btn btn-fill"
              v-if="inviaMessaggi"
              @click="showEditPanel = true"
            >
              <span class="btn-label">
                <i class="ti-pencil"></i>
                {{ $t('comunicazioni.new_message') }}
              </span>
            </button>
          </div>
        </div>
        <div class="card-content">
          <vue-tabs
            v-model="tabName"
            class="row"
            @tab-change="tabChangeManager"
          >
            <v-tab title="In arrivo" id="ricevuti" icon="ti-import">
              <div v-loading="loading">
                <div
                  class="row comunicazioni-list table-header"
                  v-if="comunicazioniDisplay"
                >
                  <div class="col-xs-2 head-label">
                    <el-popover
                      trigger="hover"
                      v-model="filtroMittentiVisible"
                      placement="top-start"
                      width="250"
                    >
                      <el-select
                        v-model="filtroMittenti"
                        filterable
                        @change="hideFiltroMittenti()"
                        placeholder="cerca o seleziona un mittente"
                        size="mini"
                        class="fullwidth"
                      >
                        <el-option
                          v-for="item in mittenti"
                          :key="item"
                          :value="item"
                          :label="item"
                        ></el-option>
                      </el-select>
                      <button
                        class="btn btn-simple pull-left"
                        :class="getActiveFilterClass('filtroMittenti')"
                        @click="showFiltroMittenti()"
                        slot="reference"
                      >
                        <i class="ti-search"></i>
                        Mittente
                      </button>
                    </el-popover>
                    <button
                      v-if="filtroMittenti"
                      class="btn btn-simple btn-icon pull-right btn-info"
                      :class="getActiveFilterClass('filtroMittenti')"
                      @click="filtroMittenti = null"
                    >
                      <i class="ti-close"></i>
                    </button>
                  </div>
                  <div class="col-xs-1 text-center btn-filter">
                    <el-popover
                      placement="top-start"
                      trigger="hover"
                      v-model="filtroPresaVisioneVisible"
                    >
                      <el-radio-group size="mini" v-model="filtroPresaVisione">
                        <el-radio-button label="all">Tutti</el-radio-button>
                        <el-radio-button label="not_read">
                          Da Leggere
                        </el-radio-button>
                        <el-radio-button label="read">
                          Già letti
                        </el-radio-button>
                      </el-radio-group>
                      <button
                        class="btn btn-simple pull-left"
                        :class="getActiveFilterClass('filtroPresaVisione')"
                        @click="showPopFiltro('filtroPresaVisioneVisible')"
                        slot="reference"
                      >
                        <i class="ti-eye icon-large"></i>
                      </button>
                    </el-popover>
                  </div>
                  <div class="col-xs-1 text-center btn-filter">
                    <el-popover
                      placement="top-start"
                      trigger="hover"
                      v-model="filtroSottoscrizioneVisible"
                    >
                      <el-radio-group
                        size="mini"
                        v-model="filtroSottoscrizione"
                      >
                        <el-radio-button label="all">Tutti</el-radio-button>
                        <el-radio-button label="not_read">
                          Da Sottoscrivere
                        </el-radio-button>
                        <el-radio-button label="read">
                          Già sottoscritti
                        </el-radio-button>
                      </el-radio-group>
                      <button
                        class="btn btn-simple pull-left"
                        :class="getActiveFilterClass('filtroSottoscrizione')"
                        @click="showPopFiltro('filtroSottoscrizioneVisible')"
                        slot="reference"
                      >
                        <i class="ti-list icon-large"></i>
                      </button>
                    </el-popover>
                  </div>
                  <div class="col-xs-5 head-label">
                    <el-popover
                      placement="top-start"
                      trigger="hover"
                      v-model="filtroOggettoVisible"
                      width="250"
                    >
                      <el-input
                        v-model="filtroOggetto"
                        prefix-icon="ti-search"
                        placeholder="Cerca nel messaggio"
                        size="mini"
                      ></el-input>
                      <button
                        class="btn btn-simple pull-left"
                        :class="getActiveFilterClass('filtroOggetto')"
                        @click="showPopFiltro('filtroOggettoVisible')"
                        slot="reference"
                      >
                        Oggetto
                      </button>
                    </el-popover>
                    <button
                      v-if="filtroOggetto"
                      class="btn btn-simple btn-icon pull-right"
                      :class="getActiveFilterClass('filtroOggetto')"
                      @click="filtroOggetto = ''"
                    >
                      <i class="ti-close"></i>
                    </button>
                  </div>
                  <div class="col-xs-1 text-center btn-filter">
                    <el-popover
                      placement="top-start"
                      trigger="hover"
                      v-model="filtroAllegatiVisible"
                    >
                      <el-radio-group size="mini" v-model="filtroAllegati">
                        <el-radio-button label="all">Tutti</el-radio-button>
                        <el-radio-button label="has_attachments">
                          Con allegati
                        </el-radio-button>
                      </el-radio-group>
                      <button
                        class="btn btn-simple pull-left"
                        :class="getActiveFilterClass('filtroAllegati')"
                        @click="showPopFiltro('filtroAllegatiVisible')"
                        slot="reference"
                      >
                        <i class="ti-clip icon-large"></i>
                      </button>
                    </el-popover>
                  </div>
                  <div class="col-xs-2 text-center">
                    <div class="btn btn-simple btn-icon">
                      <i class="ti-calendar"></i>
                    </div>
                  </div>
                </div>
                <fade-transition
                  group
                  tag="div"
                  class=""
                  :duration="150"
                  v-infinite-scroll="cScrollHandler"
                >
                  <div
                    class="row comunicazioni-list"
                    v-for="comunicazione in comunicazioniDisplay"
                    :key="comunicazione.id"
                    @click="openDialog(comunicazione)"
                  >
                    <div
                      class="col-xs-2"
                      :class="getDaLeggereClass(comunicazione)"
                      v-if="comunicazione.mittente !== null"
                    >
                      {{ comunicazione.mittente.nome }}
                      {{ comunicazione.mittente.cognome }}
                    </div>
                    <div class="col-xs-2" v-else>
                      {{ comunicazione.sottotitolo }}
                    </div>
                    <div class="col-xs-1">
                      <div
                        v-if="isPresaVisione(comunicazione)"
                        class="text-center"
                      >
                        <div
                          class="btn btn-icon btn-sm btn-fill"
                          :class="
                            getPresaVisioneClass(comunicazione.presa_visione)
                          "
                        >
                          <span class="ti-check"></span>
                        </div>
                      </div>
                    </div>
                    <div class="col-xs-1">
                      <div
                        v-if="isSottoscrizione(comunicazione)"
                        class="text-center"
                      >
                        <div
                          class="btn btn-icon btn-sm btn-fill"
                          :class="
                            getPresaVisioneClass(comunicazione.presa_visione)
                          "
                        >
                          <span class="ti-check"></span>
                        </div>
                      </div>
                    </div>
                    <div
                      class="col-xs-5"
                      :class="getDaLeggereClass(comunicazione)"
                    >
                      {{ comunicazione.titolo }}
                    </div>
                    <div class="col-xs-1 text-center">
                      <div
                        class="btn btn-icon btn-sm btn-info btn-fill btn-allegati"
                        v-if="comunicazione.allegati > 0"
                      >
                        {{ comunicazione.allegati }}
                      </div>
                    </div>
                    <div
                      class="col-xs-2 text-center"
                      :class="getDaLeggereClass(comunicazione)"
                    >
                      {{ comunicazione.data | simpledate }}
                    </div>
                  </div>
                </fade-transition>
              </div>
            </v-tab>
            <v-tab title="Inviati" id="inviati" v-if="inviati" icon="ti-export">
              <div v-loading="loading">
                <div
                  class="row comunicazioni-list table-header"
                  v-if="comunicazioniInviateDisplay"
                >
                  <div class="col-xs-2">A:</div>
                  <div class="col-xs-1 text-center">
                    <span class="ti-eye icon-large"></span>
                  </div>
                  <div class="col-xs-6"></div>
                  <div class="col-xs-1 text-center">
                    <span class="ti-clip icon-large"></span>
                  </div>
                  <div class="col-xs-2">Data</div>
                </div>
                <div
                  class="row comunicazioni-list"
                  v-for="comunicazione in comunicazioniInviateDisplay"
                  :key="comunicazione.id"
                  @click="openDialogInviate(comunicazione)"
                >
                  <div
                    class="col-xs-2 ellipsed"
                    v-html="formattaDestinatari(comunicazione)"
                  ></div>
                  <div class="col-xs-1 text-center">
                    <span
                      :class="getIconaPresaVisione(comunicazione)"
                      v-if="comunicazione.presa_visione"
                      @click.stop.prevent="openPresaVisionePanel(comunicazione)"
                    ></span>
                  </div>
                  <div class="col-xs-6">
                    {{ comunicazione.titolo }}
                  </div>
                  <div class="col-xs-1 text-center">
                    <div
                      class="btn btn-icon btn-sm btn-info btn-fill btn-allegati"
                      v-show="comunicazione.allegati.length"
                    >
                      {{ comunicazione.allegati.length }}
                    </div>
                  </div>
                  <div class="col-xs-2">
                    {{ comunicazione.data | simpledate }}
                  </div>
                </div>
              </div>
            </v-tab>
            <v-tab title="Bacheca" id="bacheca" icon="ti-blackboard">
              <div v-loading="loading">
                <div class="row comunicazioni-list" v-if="bachecaDisplay">
                  <div class="col-xs-2">Mittente:</div>
                  <div class="col-xs-7">Oggetto</div>
                  <div class="col-xs-1 text-center">
                    <span class="ti-clip icon-large"></span>
                  </div>
                  <div class="col-xs-2">Data</div>
                </div>
                <div
                  class="row comunicazioni-list"
                  v-for="bacheca in bachecaDisplay"
                  :key="bacheca.id"
                  @click="openDialogBacheca(bacheca)"
                >
                  <div
                    class="col-xs-2 ellipsed"
                    v-html="formattaDestinatari(bacheca)"
                  ></div>
                  <div class="col-xs-7">
                    {{ bacheca.titolo }}
                  </div>
                  <div class="col-xs-1 text-center">
                    <div
                      class="btn btn-icon btn-sm btn-info btn-fill btn-allegati"
                      v-show="bacheca.allegati.length"
                    >
                      {{ bacheca.allegati.length }}
                    </div>
                  </div>
                  <div class="col-xs-2 text-center">
                    {{ bacheca.data | simpledate }}
                  </div>
                </div>
              </div>
            </v-tab>
          </vue-tabs>
        </div>
      </el-card>
    </div>
    <el-dialog
      :visible.sync="showDialog"
      :append-to-body="true"
      custom-class="comunicazioni-dialog"
      :destroy-on-close="true"
      ref="dialogMsg"
      top="2vh"
      @open="dialogOpen"
      @close="dialogClose"
    >
      <div slot="title">
        <h6 v-if="dialogContent">
          <span class="ti-calendar"></span>
          {{ $dayjs(dialogContent.data) | fulldate }} -
          <span class="ti-time"></span>
          {{ $dayjs(dialogContent.data) | simpletime }}
          <button
            class="btn btn-fill btn-xs btn-presa-visione"
            :class="getPresaVisioneClass(dialogContent.presa_visione)"
            @click.stop.prevent="doInviaPresaVisione"
            v-if="
              isPresaVisione(dialogContent) || isSottoscrizione(dialogContent)
            "
          >
            <spinner
              class="wait-spinner"
              :radius="28"
              v-if="btnSpinner"
            ></spinner>
            <span class="ti-check" v-if="dialogContent.presa_visione"></span>
            <span v-if="isPresaVisione(dialogContent)">
              {{ getPresaVisioneTesto(dialogContent.presa_visione) }}
            </span>
            <span v-if="isSottoscrizione(dialogContent)">
              {{ getSottoscrizioneTesto(dialogContent.presa_visione) }}
            </span>
          </button>
        </h6>
      </div>

      <comunicazioni-detail
        :item="dialogContent"
        v-if="dialogContent"
      ></comunicazioni-detail>

      <comunicazioni-inviate-detail
        :item="dialogInviateContent"
        v-if="dialogInviateContent"
      ></comunicazioni-inviate-detail>

      <bacheca-detail
        :item="dialogBachecaContent"
        v-if="dialogBachecaContent"
      ></bacheca-detail>

      <div class="comunicazioni-answer" v-if="canAnswer">
        <div class="col-xs-12 text-right">
          <button
            class="btn btn-fill btn-info"
            @click.stop.prevent="answerMessage()"
          >
            <span class="btn-label">
              <i class="ti-back-right"></i>
              Rispondi
            </span>
          </button>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      :visible.sync="showEditPanel"
      :append-to-body="true"
      custom-class="comunicazioni-dialog"
      :destroy-on-close="true"
      ref="dialogMsg"
      top="1vh"
      @open="clearEditorContent"
      @close="clearEditorPanel"
      :close-on-click-modal="canCloseEditPanel"
      :close-on-press-escape="canCloseEditPanel"
      :show-close="canCloseEditPanel"
    >
      <div slot="title">
        <h6>Nuovo messaggio</h6>
      </div>
      <message-edit-panel
        v-if="showEditPanel"
        @message-sent="messageSentHandler"
      ></message-edit-panel>
    </el-dialog>

    <el-dialog
      :visible.sync="showPresaVisionePanel"
      :append-to-body="true"
      custom-class="comunicazioni-dialog presa-visione"
      top="10vh"
      :destroy-on-close="true"
      v-if="inviaMessaggi"
    >
      <div slot="title">
        <h6>Presa Visione</h6>
      </div>
      <lista-presa-visione
        :destinatari="dialogPresaVisioneContent"
      ></lista-presa-visione>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common'
import {
  Spinner,
  Dialog,
  Loading,
  Card,
  Popover,
  Select,
  Option,
  RadioGroup,
  RadioButton,
  Input,
  InfiniteScroll
} from 'element-ui'
import VueTabs from 'vue-nav-tabs'
import { mapGetters, mapActions } from 'vuex'

import ComunicazioniDetail from '@/components/Mastercom/UIComponents/ComunicazioniDetail.vue'
import ComunicazioniInviateDetail from '@/components/Mastercom/UIComponents/ComunicazioniInviateDetail.vue'
import BachecaDetail from '@/components/Mastercom/UIComponents/BachecaDetail.vue'
import MessageEditPanel from '@/components/Mastercom/UIComponents/Panels/MessageEditPanel.vue'
import ListaPresaVisione from '@/components/Mastercom/UIComponents/ListaPresaVisione.vue'

Vue.use(Card)
Vue.use(VueTabs)
Vue.use(Loading)
Vue.use(Popover)
Vue.use(Dialog)
Vue.use(Select)
Vue.use(Option)
Vue.use(RadioGroup)
Vue.use(RadioButton)
Vue.use(Input)
Vue.use(InfiniteScroll)
export default {
  name: 'ComunicazioniMainPanel',
  mixins: [Common],
  components: {
    ComunicazioniDetail,
    ComunicazioniInviateDetail,
    MessageEditPanel,
    ListaPresaVisione,
    BachecaDetail,
    Spinner
  },
  computed: {
    cAnnoScolasticoComunicazioni() {
      return ''
      // let anno1 = this.$dayjs(this._date_params.data_inizio).year()
      // let anno2 = this.$dayjs(this._date_params.data_fine).year()
      // return `A.S. ${anno1}/${anno2}`
    },
    comunicazioniDisplay() {
      let data = this._comunicazioniDisplay
      return data.slice(0, this.maxCItem)
    },
    _comunicazioniDisplay() {
      let data = this.comunicazioni
      if (this.filtriData.length) {
        data = data.filter(a => {
          return (
            this.$dayjs(a.data) >= this.filtriData[0] &&
            this.$dayjs(a.data) <= this.filtriData[1]
          )
        })
      }
      if (this.filtroMittenti) {
        data = data.filter(a => {
          return (
            `${a.mittente.nome} ${a.mittente.cognome}` === this.filtroMittenti
          )
        })
      }

      if (this.filtroPresaVisione === 'not_read') {
        data = data.filter(a => {
          return a.sottoscrizione === false && a.presa_visione === 0
        })
      }

      if (this.filtroPresaVisione === 'read') {
        data = data.filter(a => {
          return a.sottoscrizione === false && a.presa_visione === 1
        })
      }

      if (this.filtroSottoscrizione === 'not_read') {
        data = data.filter(a => {
          return a.sottoscrizione === true && a.presa_visione === 0
        })
      }

      if (this.filtroSottoscrizione === 'read') {
        data = data.filter(a => {
          return a.sottoscrizione === true && a.presa_visione === 1
        })
      }

      if (this.filtroAllegati === 'has_attachments') {
        data = data.filter(a => {
          return a.allegati > 0
        })
      }

      if (
        this.filtroOggetto !== '' &&
        this.filtroOggetto.length >= this.filtroOggettoMinLength
      ) {
        data = data.filter(a => {
          return a.titolo
            .toLowerCase()
            .includes(this.filtroOggetto.toLowerCase())
        })
      }

      return data
    },
    comunicazioniInviateDisplay() {
      let data = this._comunicazioniInviateDisplay
      return data.slice(0, this.maxCItem)
    },
    _comunicazioniInviateDisplay() {
      let data = this.comunicazioniInviate
      if (this.filtriData.length) {
        data = data.filter(a => {
          return (
            this.$dayjs(a.data) >= this.filtriData[0] &&
            this.$dayjs(a.data) <= this.filtriData[1]
          )
        })
      }
      return data
    },
    bachecaDisplay() {
      let data = this._bachecaDisplay
      return data.slice(0, this.maxCItem)
    },
    _bachecaDisplay() {
      let data = this.bacheca
      if (this.filtriData.length) {
        data = data.filter(a => {
          return (
            this.$dayjs(a.data) >= this.filtriData[0] &&
            this.$dayjs(a.data) <= this.filtriData[1]
          )
        })
      }
      return data
    },
    canAnswer() {
      let result = false
      if (
        this.dialogContent !== null &&
        this.dialogContent.id_mittente !== null
      ) {
        result = true
      }
      return result
    },
    canCloseEditPanel() {
      return !this.attachmentUploading
    },
    mittenti() {
      return [
        ...new Set(
          this.comunicazioni
            .map(v => `${v.mittente.nome} ${v.mittente.cognome}`)
            .sort((a, b) => {
              if (a < b) {
                return -1
              }
              if (a > b) {
                return 1
              }
            })
        )
      ]
    },
    ...mapGetters({
      comunicazioni: 'comunicazioni/comunicazioni_elenco',
      _date_params: 'comunicazioni/date_params',
      comunicazioniInviate: 'comunicazioni/comunicazioni_inviate',
      bacheca: 'comunicazioni/bacheca',
      destinatari: 'comunicazioni/destinatari',
      id_lettura: 'comunicazioni/id_lettura',
      attachmentUploading: 'comunicazioni/attachment_uploading',
      dettaglio: 'comunicazioni/dettaglio',
      id_presa_visione: 'comunicazioni/id_presa_visione'
    })
  },
  props: {
    inviati: {
      type: Boolean,
      default: false
    },
    inviaMessaggi: {
      type: Boolean,
      default: false
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      showDialog: false,
      showEditPanel: false,
      showPresaVisionePanel: false,
      dialogTitle: null,
      dialogContent: null,
      dialogBachecaContent: null,
      dialogInviateContent: null,
      dialogPresaVisioneContent: null,
      filtroMittentiVisible: false,
      filtroMittenti: null,
      filtroPresaVisioneVisible: false,
      filtroPresaVisione: null,
      filtroSottoscrizioneVisible: false,
      filtroSottoscrizione: null,
      filtroAllegatiVisible: false,
      filtroAllegati: null,
      filtroOggettoVisible: false,
      filtroOggetto: '',
      activeFilterClass: 'filter-active',
      filtroOggettoMinLength: 3,
      btnSpinner: false,
      test: 'aaa',
      tabName: '',
      pageItem: 20,
      maxCItem: 0
    }
  },
  mounted() {
    // let dialog = this.$refs.dialogNuovoMessaggio
    this.maxCItem = this.pageItem
  },
  unmounted() {
    this.reset()
  },
  methods: {
    cScrollHandler() {
      this.maxCItem += this.pageItem
    },
    doReset() {
      this.maxCItem = this.pageItem
    },
    getActiveFilterClass(type) {
      let filter = this[`${type}`]
      return filter !== null && filter !== 'all' && filter !== ''
        ? this.activeFilterClass
        : ''
    },
    hideFiltroMittenti() {
      this.filtroMittentiVisible = false
    },
    showFiltroMittenti() {
      this.filtroMittentiVisible = !this.filtroMittentiVisible
    },
    hidePopFiltro(type) {
      this[`${type}`] = false
    },
    showPopFiltro(type) {
      this[`${type}`] = !this[`${type}`]
    },
    isPresaVisione(item) {
      return item.presa_visione >= 0 && item.sottoscrizione !== true
    },
    isSottoscrizione(item) {
      return item.presa_visione >= 0 && item.sottoscrizione === true
    },
    getIconaPresaVisione(c) {
      let icona = 'ti-menu-alt'
      if (c.presa_visione.toLowerCase() === 'tutti') {
        icona = 'ti-check'
      }
      return icona
    },
    messageSentHandler() {
      if (this.inviati) {
        // console.log('switch to inviati')
        this.tabName = 'Inviati'
      }
      // this.$emit('message-sent')
      this.showEditPanel = false
    },
    dialogOpen() {
      return
    },
    answerMessage() {
      this.setRisposta(this.dialogContent)
      this.showDialog = false
      this.showEditPanel = true
    },
    dialogClose() {
      this.dialogContent = null
      this.dialogInviateContent = null
    },
    clearEditorPanel() {
      this.clearRisposta()
    },
    hasAttachments(item) {
      if (item.allegati.length > 0) {
        return true
      }
      return false
    },
    openPresaVisionePanel(item) {
      this.dialogPresaVisioneContent = item.lista_presa_visione
      this.showPresaVisionePanel = true
    },
    openDialog(item) {
      this.dialogTitle = item.titolo
      this.showDialog = true
      this.btnSpinner = false
      this.setIdDettaglio(item.id)
        .then(() => {
          return this.loadDettaglio(this.authObject)
        })
        .then(() => (this.dialogContent = this.dettaglio))
      this.setIdLettura(item.id)
        .then(() => {
          return this.inviaLettura(this.authObject)
        })
        .then(() => {
          item.da_leggere = false
        })
    },
    openDialogInviate(item) {
      this.dialogInviateContent = item
      this.dialogTitle = item.titolo
      this.showDialog = true
    },
    openDialogBacheca(item) {
      this.dialogBachecaContent = item
      this.dialogTitle = item.titolo
      this.showDialog = true
    },
    formattaDestinatari(item) {
      return item.sottotitolo
    },
    getDaLeggereClass(item) {
      if (item.da_leggere) {
        return 'da-leggere'
      }
    },
    getPresaVisioneClass(item) {
      return item ? 'btn-success' : 'btn-warning'
    },
    getPresaVisioneTesto(item) {
      return item ? 'Segnato come letto' : 'Segna come letto'
    },
    getSottoscrizioneTesto(item) {
      return item
        ? 'Sottoscritto'
        : 'Sottoscrivi quanto contenuto nel messaggio'
    },
    tabChangeManager(tabIndex, newTab, oldTab) {
      this.maxCItem = this.pageItem
      this.$emit('tab-changed', tabIndex, newTab, oldTab)
    },
    doInviaPresaVisione() {
      this.btnSpinner = true
      this.setIdPresaVisione(this.dialogContent.id)
        .then(() => {
          return this.inviaPresaVisione(this.authObject)
        })
        .then(result => {
          this.dialogContent.presa_visione = result ? 1 : 0
          this.btnSpinner = false
        })
    },
    ...mapActions({
      clearEditorContent: 'messageEditor/clearContent',
      setIdPresaVisione: 'comunicazioni/setIdPresaVisione',
      setIdLettura: 'comunicazioni/setIdLettura',
      setIdDettaglio: 'comunicazioni/setIdDettaglio',
      inviaPresaVisione: 'comunicazioni/inviaPresaVisione',
      inviaLettura: 'comunicazioni/inviaLettura',
      setRisposta: 'comunicazioni/setRisposta',
      clearRisposta: 'comunicazioni/clearRisposta',
      loadDettaglio: 'comunicazioni/loadDettaglio',
      reset: 'comunicazioni/reset'
    })
  }
}
</script>

<style lang="scss" scoped>
.fullwidth {
  width: 100%;
}
.head-label {
  .btn-simple {
    &.pull-left {
      padding-left: 0px;
    }
  }
}

.table-header {
  text-align: center;
  .btn-filter {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.ellipsed {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.last-row {
  padding: 10px 0;
}
.comunicazioni-list {
  text-align: left;
  padding-bottom: 10px;
  padding-top: 10px;
  border-bottom: 2px solid #e0e0e0;
  color: #666666;
  cursor: pointer;
  overflow: hidden;

  &.da-leggere {
    color: #000000;
    font-weight: 700;
  }
  &:not(:first-child):hover {
    font-size: 1.1em;
    background-color: #f8f8f8;
    transition: background-color 0.3s, font-size 0.3s;
  }
}

.da-leggere {
  font-weight: 700;
}
.visualizzato {
  text-transform: lowercase;
  float: left;
  width: 99%;
  font-size: 0.8em;
  margin: 0.5em 0;
}

#comunicazioni-main-panel {
  .vue-tabs {
    .left-vertical-tabs {
      width: 10%;
    }
  }
}

.btn-allegati {
  min-width: 28px;
}

.filter-active {
  color: #409eff;
}

/deep/ .messageEditorDialog {
  height: 95%;
  .el-dialog__body {
    height: 95%;
  }
}
#destinatari {
  overflow: scroll;
  height: 100%;
}

/deep/ .wait-spinner {
  &.el-spinner {
    .el-spinner-inner {
      .path {
        stroke: rgba(255, 255, 255, 1);
      }
    }
  }
}

/deep/ .comunicazioni-dialog {
  height: 96%;
  max-width: 960px;
  width: 90%;
  margin-top: 50px;
  border-radius: 5px;
  &.presa-visione {
    height: auto;
    .el-dialog__body {
      height: 80%;
    }
  }
  .el-dialog__header {
    height: 50px;

    background-color: #f4f4f4;
    border-top-left-radius: 5px;
    border-top-right-radius: 5px;
    border-bottom: 1px solid #e2e2e2;
    h6 {
      margin-top: 0;
    }
    .btn-presa-visione {
      float: right;
      text-transform: uppercase;
      font-size: 0.9em;
      margin-top: -5px;
      margin-right: 30px;
    }
  }
  .el-dialog__body {
    margin-top: 0;
    padding: 10px 0 0 0;
    height: 90%;
    word-break: break-word;
    h4 {
      margin-top: 10px;
      padding-bottom: 10px;
      padding-left: 20px;
      padding-right: 20px;
      margin-bottom: 10px;
      border-bottom: 1px solid #e2e2e2;
    }
    .comunicazioni-detail {
      height: 100%;
      overflow-y: auto;
      width: 100%;
      &.can-answer {
        height: 90%;
      }
      .mittente {
        padding: 10px 20px 0;
      }
      .body {
        padding: 20px;
      }
      .allegati {
        padding: 20px;
      }
    }
    .comunicazioni-answer {
      position: absolute;
      bottom: 0;
      width: 100%;
      padding: 10px;
    }
  }
}
</style>
