<template>
  <div @click.stop="gotoCompiti()" class="clickable">
    <mini-card card-color="brown">
      <h6 slot="header">Nuovi compiti</h6>
      <div class="numbers" slot="content">
        {{ compiti }}
      </div>
      <div slot="footer">
        <p class="category" v-if="loading">
          <spinner :radius="30"></spinner>
          Loading
        </p>
        <p class="category" v-else>
          <span class="ti-time"></span>
          {{ lastTimeLoaded | simpletime }}
        </p>
      </div>
    </mini-card>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common.js'
import MiniCard from '@/components/Mastercom/UIComponents/MiniCard'

import { mapState, mapGetters, mapActions } from 'vuex'
import { Spinner } from 'element-ui'

export default {
  name: 'CompitiPanel',
  mixins: [Common],
  components: {
    MiniCard,
    Spinner
  },
  data() {
    return {
      loading: false,
      lastTimeLoaded: null,
      compiti: 0
    }
  },
  watch: {
    aggiornamenti() {
      if ('compiti' in this.aggiornamenti) {
        this.compiti = this.aggiornamenti.compiti.numero
      }
    }
  },
  computed: {
    ...mapState({
      aggiornamenti: state => state.aggiornamenti.all
    }),
    ...mapGetters({
      authObject: 'main/authObject'
    })
  },
  methods: {
    gotoCompiti() {
      this.setSoloNovita(true)
      this.$router.push('Compiti')
    },
    ...mapActions({
      setSoloNovita: 'compiti/setSoloNovita'
    })
  }
}
</script>

<style></style>
