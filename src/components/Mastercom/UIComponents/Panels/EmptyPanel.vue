<template>
  <div>
    <el-card class="box-card card">
      <h6>
        <slot name="header"></slot>
      </h6>
      <div class="content">
        <slot name="content"></slot>
      </div>
    </el-card>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common.js'

import { Card } from 'element-ui'

Vue.use(Card)
export default {
  name: 'OrarioPanel',
  mixins: [Common],
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped></style>
