<template>
  <div>
    <button
      class="btn btn-fill btn-sm"
      :class="prenotato === true ? 'btn-success' : 'btn-info'"
      @click="openPrenotazioniPanel"
    >
      <span v-if="prenotato" class="ti-arrow-right">
        {{ $t('colloqui.btn_gestione_prenotato') }}
      </span>
      <span class="ti-arrow-right" v-else>
        {{ $t('colloqui.label_seleziona_data') }}
      </span>
    </button>
    <el-dialog
      :append-to-body="true"
      :visible.sync="prenotazioniPanelVisible"
      top="5vh"
      :width="_dialogWidth()"
      @close="currentStep = 0"
    >
      <el-steps :active="currentStep" simple>
        <el-step
          :title="$t('colloqui.label_seleziona_data_step')"
          icon="ti-calendar"
          @click.native="currentStep = 0"
          class="clickable"
        ></el-step>
        <el-step
          :title="$t('colloqui.label_prenotazione_slot')"
          icon="ti-check"
        ></el-step>
      </el-steps>

      <div v-if="showSelezionaDateStep">
        <div class="row current-colloquio">
          <div class="col-xs-12">
            <i class="ti-user has-float"></i>
            <insegnante
              :nome="colloquio.nome"
              :titolo="colloquio.titolo"
            ></insegnante>
          </div>
          <div class="col-xs-12">
            <i class="ti-blackboard"></i>
            {{ colloquio.materia }}
          </div>
        </div>
        <div class="row header">
          <div class="col-xs-2 col-md-2 col-lg-2 posti-disponibili">
            {{ $t('colloqui.label_seleziona_slot') }}
          </div>
          <div class="col-xs-2 col-md-3 col-lg-3">{{ $t('main.date') }}</div>
          <div class="col-xs-2 col-md-5 col-lg-5">
            {{ $t('colloqui.label_orario_note') }}
          </div>
          <div class="col-xs-2 col-md-2 col-lg-2 text-center">
            {{ $t('main.actions') }}
          </div>
        </div>
        <div
          class="row date-colloqui clickable"
          v-for="(item, index) in colloquiInsegnante"
          :key="index"
          @click.stop.prevent="gotoPrenotazionePosti(item)"
        >
          <div class="col-xs-2 col-md-2 col-lg-2 posti-disponibili">
            <badge
              background-color="#fff"
              :small="true"
              :outline="backgroundColor(item.stato)"
              v-if="item.stato === 'prenotato'"
            >
              <i class="ti-check"></i>
            </badge>
            <badge
              :background-color="backgroundColor(item.stato)"
              :small="true"
              v-else
            >
              {{ item.slot_prenotabili.length }}
            </badge>
          </div>
          <div class="col-xs-12 col-md-3 col-lg-3">
            {{ item.data | extdate }}
          </div>
          <div class="col-xs-12 col-md-5 col-lg-5">
            <colloquio-detail :item="item" :hide-data="true"></colloquio-detail>
          </div>

          <div class="col-xs-12 col-md-2 col-lg-2 text-center">
            <button
              class="btn btn-sm btn-fill btn-warning"
              v-if="item.stato === 'prenotato'"
            >
              <spinner
                v-if="spinner"
                :radius="28"
                class="download-spinner"
              ></spinner>
              <i class="ti-pencil" v-else></i>
              {{ $t('main.edit') }}
            </button>

            <button
              class="btn btn-sm btn-fill btn-success"
              v-if="
                item.stato !== 'prenotato' && item.slot_prenotabili.length > 0
              "
            >
              <spinner
                v-if="spinner"
                :radius="28"
                class="download-spinner"
              ></spinner>
              <i class="ti-check" v-else></i>
              {{ $t('main.select') }}
            </button>
          </div>
        </div>
      </div>

      <div v-if="showPrenotaPostoStep">
        <div class="row current-colloquio">
          <div class="col-xs-12 col-sm-6">
            <insegnante
              :nome="colloquio.nome"
              :titolo="colloquio.titolo"
            ></insegnante>
            {{ colloquio.materia }}
          </div>
          <div class="col-xs-12 col-sm-6">
            <colloquio-detail :item="currentColloquio"></colloquio-detail>
          </div>
        </div>
        <div class="row header">
          <div class="col-xs-3 col-lg-1">{{ $t('colloqui.label_stato') }}</div>
          <div class="col-xs-3 col-lg-9">
            {{ $t('colloqui.label_descrizione') }}
          </div>
          <div class="col-xs-3 col-lg-2 text-center">
            {{ $t('main.actions') }}
          </div>
        </div>
        <div
          class="row elenco-posti"
          :class="{ offuscato: item.stato === 'occupato' }"
          v-for="(item, index) in currentColloquio.slot_disponibili"
          :key="index"
        >
          <div class="col-xs-3 col-lg-1">
            <badge
              background-color="#fff"
              :small="true"
              :outline="backgroundColor(item.stato)"
              v-if="item.stato === 'prenotato'"
            >
              <i class="ti-check"></i>
            </badge>
            <badge
              :background-color="backgroundColor(item.stato)"
              :small="true"
              v-else
            ></badge>
          </div>
          <div class="col-xs-12 col-lg-9">
            {{ item.descrizione }}
          </div>
          <div class="col-xs-12 col-lg-2 text-center">
            <button
              class="btn btn-fill btn-sm btn-success"
              @click.stop.prevent="doPrenotaColloquio(item.id)"
              v-if="canPrenotaColloquio(item)"
            >
              <spinner
                v-if="spinner == item.id"
                :radius="28"
                class="download-spinner"
              ></spinner>
              <i class="ti-check" v-else></i>
              {{ $t('colloqui.btn_prenota') }}
            </button>

            <button
              class="btn btn-fill btn-sm btn-danger"
              @click.stop.prevent="doCancellaPrenotazione(item)"
              v-if="canCancellaPrenotazione(item)"
            >
              <spinner
                v-if="spinner"
                :radius="28"
                class="download-spinner"
              ></spinner>
              <i class="ti-close" v-else></i>
              {{ $t('colloqui.btn_cancella') }}
            </button>
          </div>
        </div>
        <div class="row footer">
          <div class="col-xs-2 col-xs-offset-10 text-center">
            <button @click="currentStep = 0" class="btn btn-sm btn-primary">
              <i class="ti-angle-left"></i>
              {{ $t('colloqui.btn_indietro') }}
            </button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common.js'
import Colloqui from '@/components/Mastercom/Mixins/Colloqui.js'
import Badge from '@/components/Mastercom/UIComponents/Badge'
import Insegnante from '@/components/Mastercom/UIComponents/Insegnante.vue'
import ColloquioDetail from '@/components/Mastercom/UIComponents/ColloquioDetail'
import { Dialog, Steps, Step, Spinner } from 'element-ui'

Vue.use(Dialog)
Vue.use(Steps)
Vue.use(Step)

export default {
  name: 'ColloquiPrenotaPanel',
  mixins: [Colloqui, Common],
  components: {
    Badge,
    Spinner,
    ColloquioDetail,
    Insegnante
  },
  data() {
    return {
      colloquiPrenotato: 0,
      currentStep: 0,
      spinner: 0
    }
  },
  computed: {
    showSelezionaDateStep() {
      return this.currentStep === 0
    },
    showPrenotaPostoStep() {
      return this.currentStep === 1
    }
  },
  methods: {
    canPrenotaColloquio(item) {
      return (
        item.stato === 'libero' &&
        this.currentColloquio.stato !== 'prenotato' &&
        this.$dayjs(this.currentColloquio.data) > this.$dayjs() &&
        (this.spinner == 0 || this.spinner == item.id)
      )
    },
    canCancellaPrenotazione(item) {
      return (
        item.stato === 'prenotato' &&
        this.$dayjs(this.currentColloquio.data) > this.$dayjs()
      )
    },
    gotoPrenotazionePosti(item) {
      this.showLoader()
      this.spinner = true
      this.loadColloqui(this.authObject)
        .then(() => {
          let colloquio = this.colloquiInsegnante.filter(
            c => c.id === item.id
          )[0]
          this.setCurrent(colloquio)
          this.currentStep = 1
        })
        .finally(() => {
          this.spinner = false
          this.hideLoader()
        })
    },
    _dialogWidth() {
      return window.outerWidth > 1024 ? '60%' : '100%'
    },

    _dialogHeight() {
      return `${window.outerHeight - 20}px`
    },
    doPrenotaColloquio(id_slot = null) {
      this.spinner = id_slot
      this.setTargetSlot(id_slot)
      this.initScuolaStudente(this.authObject)
        .then(() => {
          return this.isSectionEnabled('colloqui')
        })
        .then(enabled => {
          if (enabled === true) {
            return this.prenota(this.authObject)
          }
          return enabled
        })
        .then(enabled => {
          if (enabled !== false) {
            return this.loadColloqui(this.authObject)
          }
          return enabled
        })
        .then(() => {
          this.refreshCurrent()
        })
        .catch(error => {
          this.setError(error.response)
        })
        .finally(() => {
          this.spinner = 0
          this.setTargetSlot(null)
        })
    },
    doCancellaPrenotazione(id_slot = null) {
      this.setTargetSlot(id_slot)
      this.spinner = id_slot
      this.initScuolaStudente(this.authObject)
        .then(() => {
          return this.isSectionEnabled('colloqui')
        })
        .then(enabled => {
          if (enabled === true) {
            return this.cancella(this.authObject)
          }
        })
        .then(enabled => {
          if (enabled !== false) {
            return this.loadColloqui(this.authObject)
          }
          return enabled
        })
        .then(() => {
          this.refreshCurrent()
        })
        .catch(error => {
          this.setError(error.response)
        })
        .finally(() => {
          this.setTargetSlot(null)
          this.spinner = 0
        })
    }
  },
  props: {
    idProfessore: {
      type: Number,
      default: 0
    },
    prenotato: {
      type: Boolean,
      default: false
    },
    colloquio: {
      type: Object,
      default: () => {}
    }
  }
}
</script>
<style lang="scss" scoped>
.header {
  div {
    font-weight: 700;
    margin-bottom: 5px;
    margin-top: 10px;
  }
}
.offuscato {
  color: #ccc;
}
.elenco-posti,
.date-colloqui {
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
  padding-top: 8px;
  &:nth-of-type(even) {
    background-color: #f8f8f8;
  }
}
.posti-disponibili {
  text-align: center;
}

/deep/ .download-spinner {
  &.el-spinner {
    .el-spinner-inner {
      .path {
        stroke: rgba(200, 200, 200, 0.8);
      }
    }
  }
}

.footer {
  margin-top: 10px;
  text-align: right;
}
.current-colloquio {
  margin-top: 10px;
  line-height: 1.8em;
  font-weight: 700;
  margin-bottom: 10px;
}

.has-float {
  margin-top: 5px;
  margin-right: 5px;
  float: left;
  line-height: 1.8em;
}
</style>
