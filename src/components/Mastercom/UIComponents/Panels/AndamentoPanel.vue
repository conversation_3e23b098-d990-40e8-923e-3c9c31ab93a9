<template>
  <div></div>
</template>

<script>
import Vue from 'vue'
// import Common from '@/components/Mastercom/Mixins/Common.js'
// import MiniCard from '@/components/Mastercom/UIComponents/MiniCard'

// import {mapGetters} from 'vuex'
import { Card } from 'element-ui'
import { mapActions } from 'vuex'

Vue.use(Card)

export default {
  name: 'AndamentoPanel',
  data() {
    return {
      loading: false
    }
  },
  methods: {
    doLoadVoti() {
      this.loading = true
      this.loadVoti().then(() => {
        this.loading = false
      })
    },
    ...mapActions({
      loadVoti: 'voti/loadVoti'
    })
  }
}
</script>

<style></style>
