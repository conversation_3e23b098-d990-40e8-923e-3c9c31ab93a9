<template>
  <div class="wrapper mastercom">
    <el-card class="box-card card">
      <div slot="header" class="row">
        <h6 class="col-xs-12">{{ $t('mense.pasti_panel_title') }}</h6>
        <div class="col-sm-4 col-lg-4">
          <date-picker
            v-model="currentDay"
            type="date"
            :placeholder="$t('main.filter_data')"
            :picker-options="PickerOptions"
            format="ddd, dd MMMM yyyy"
            @change="doLoadFixedDay()"
            :clearable="false"
            :editable="false"
            size="mini"
          ></date-picker>
        </div>
        <div class="col-xs-4 col-sm-4 col-md-3 col-lg-2 pull-right text-right">
          <button
            @click.stop="doLoadPreviousDay"
            type="button"
            class="btn btn-default btn-simple btn-icon btn-xs"
            v-if="mostraPastoPrecedente"
          >
            <span class="ti-angle-left"></span>
          </button>
          <button
            @click.stop="doLoadNextDay"
            type="button"
            class="btn btn-default btn-simple btn-icon btn-xs"
            v-if="pastoCorrente.id_pasto_successivo"
          >
            <span class="ti-angle-right"></span>
          </button>
        </div>
      </div>

      <fade-transition :duration="500">
        <div v-if="loaded">
          <div class="content" v-if="pastoCorrente">
            <h4>{{ dataPastoFromId }}</h4>
            <div v-if="isPrenotazionePossibile">
              <div v-if="pastoCorrente.scadenza">
                <h5 v-if="countdownTime > 0" class="countdown text-success">
                  <countdown :time="countdownTime">
                    <template slot-scope="props">
                      {{ $t('mense.scadenza_pasto') }}: {{ props.days }} giorni,
                      {{ props.hours }} ore, {{ props.minutes }} minuti,
                      {{ props.seconds }} secondi.
                    </template>
                  </countdown>
                </h5>
                <h5 v-else class="text-info">
                  {{ $t('mense.reservation_ended') }}
                </h5>
              </div>
              <dettaglio-servizio-pasto
                v-for="(item, index) in pastoCorrenteDisplay.servizi"
                :key="index"
                :selezionato="item.selezionato"
                :codice="item.codice"
                class="row row-dettaglio-servizio"
              >
                <div slot="descrizione" class="row">
                  <h5 class="col-xs-10">{{ item.servizio }}</h5>
                </div>
              </dettaglio-servizio-pasto>
            </div>
            <div v-else>
              {{ $t('mense.no_reservable') }}
            </div>
          </div>
          <div class="content" v-else>{{ $t('mense.no_lunch') }}</div>
        </div>
      </fade-transition>
    </el-card>
  </div>
</template>

<script>
import VueCountdown from '@chenfengyuan/vue-countdown'
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common.js'
import Mense from '@/components/Mastercom/Mixins/Mense.js'

import DettaglioServizioPasto from '@/components/Mastercom/UIComponents/DettaglioServizioPasto.vue'

import { Card, DatePicker } from 'element-ui'

Vue.use(Card)
Vue.component(VueCountdown.name, VueCountdown)
export default {
  name: 'PastiPanel',
  mixins: [Common, Mense],
  components: {
    DatePicker,
    DettaglioServizioPasto
  },
  data() {
    return {
      loaded: true,
      currentDay: null
    }
  },
  computed: {
    PickerOptions() {
      let me = this
      return {
        firstDayOfWeek: 1,
        disabledDate: function(d) {
          let date = me.$dayjs(d)
          return !me.dataAnnoScolastico(d) || date.day() === 0
        }
      }
    },
    countdownTime() {
      let now = this.$dayjs()
      if (now < this.$dayjs(this.pastoCorrente.scadenza)) {
        return this.$dayjs(this.pastoCorrente.scadenza) - now
      }
      return 0
    },
    mostraPastoPrecedente() {
      return this.pastoCorrente.id_pasto_precedente && this.canLoadPreviousDay
    },
    idDataCorrente() {
      return this.$dayjs().format('YYYYMMDD')
    },
    dataPastoFromId() {
      return this.idPastoCorrente
        ? this.$dayjs(this.idPastoCorrente).format('dddd DD MMMM')
        : ''
    },
    canLoadPreviousDay() {
      return this.idDataCorrente <= this.pastoCorrente.id_pasto_precedente
    },
    pastoCorrenteDisplay() {
      let result = {}
      result.descrizione = this.pastoCorrente.descrizione.replace(/-/g, '/')
      result.servizi = this.pastoCorrente.servizi
        .filter(x => {
          if (this.isPastoPrenotabile === false && x.codice === '--') {
            return true
          }
          return true
        })
        .sort((a, b) => {
          if (a.codice == '--') {
            return 1
          }
          if (a.codice > b.codice) {
            return 1
          }
          if (a.codice < b.codice) {
            return -1
          }
          return 0
        })
      return result
    }
  },
  methods: {
    doLoadFixedDay() {
      this.setIdPastoCorrente(this.$dayjs(this.currentDay).format('YYYYMMDD'))
      this.doLoadCurrentDay()
    },
    doLoadCurrentDay() {
      this.loaded = false
      this.loadPastoCorrente(this.authObject).finally(() => {
        this.loaded = true
      })
    },
    doLoadPreviousDay() {
      if (this.canLoadPreviousDay) {
        this.setIdPastoCorrente(this.pastoCorrente.id_pasto_precedente)
        this.doLoadCurrentDay()
      }
    },
    doLoadNextDay() {
      this.setIdPastoCorrente(this.pastoCorrente.id_pasto_successivo)
      this.doLoadCurrentDay()
    }
  }
}
</script>

<style scoped lang="scss">
.row-dettaglio-servizio {
  border-bottom: 1px solid #eaeaea;
  margin-bottom: 10px;
  h5 {
    margin-top: 0;
  }
}

.countdown {
  font-size: 1.2em;
  margin: 10px 0;
}
</style>
