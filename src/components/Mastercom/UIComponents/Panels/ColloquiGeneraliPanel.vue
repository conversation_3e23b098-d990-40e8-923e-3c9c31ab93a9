<template>
  <div>
    <el-card class="box-card card">
      <div slot="header" class="row">
        <h6 class="col-xs-12 col-sm-4">RICEVIMENTI GENERALI</h6>
        <div class="col-xs-12 col-sm-5">
          <el-select v-model="dataColloquio" @change="filtraColloquiGenerali()">
            <el-option
              v-for="(item, index) in dateColloquiGenerali"
              :key="index"
              :value="index"
              :label="item | longdate"
            ></el-option>
          </el-select>
        </div>
        <div class="col-xs-12 col-sm-3 print-button">
          <download-item
            :url="urlColloquiPrenotati"
            :icons="{ idle: 'ti-printer' }"
            descrizione="STAMPA ELENCO PRENOTAZIONI"
          ></download-item>
        </div>
      </div>

      <div class="row header" v-if="elencoInsegnantiGenerali.length">
        <div class="col-xs-12 col-sm-4">Insegnante</div>
        <div class="col-xs-12 col-sm-4">Orari disponibili</div>
        <div class="col-xs-12 col-sm-4">Colloquio prenotato</div>
      </div>
      <fade-transition group tag="div" :duration="500">
        <div
          v-for="(item, index) in elencoInsegnantiGenerali"
          :key="index"
          class="row insegnante"
          :class="getColloquioClass(item)"
        >
          <div>
            <div class="col-xs-12 col-sm-4">
              <insegnante :nome="item.nome" :titolo="item.titolo"></insegnante>
              {{ item.materia }}
            </div>
            <div class="col-xs-12 col-sm-4">
              <div v-if="isUpdating(item)" class="align-center">
                <spinner></spinner>
              </div>
              <div v-else>
                <div
                  class="content-block"
                  v-for="(citem, cindex) in item.colloqui_generali"
                  :key="cindex"
                >
                  <div
                    v-if="item.colloqui_generali.length"
                    class="colloquio-group-label"
                  >
                    {{ citem.titolo_colloqui }}
                    dalle {{ citem.ora_inizio }} alle {{ citem.ora_fine }}
                  </div>
                  <select-slot-colloqui
                    :colloquio="getSlotOrari(citem)"
                    :selected="0"
                    class="colloquio-select"
                  ></select-slot-colloqui>
                  <div class="note">{{ citem.note }}</div>
                </div>
              </div>
            </div>
            <div class="col-xs-12 col-sm-4">
              <colloquio-detail
                v-if="isColloquioPrenotato(item)"
                :item="getColloquioPrenotato(item.colloqui_generali)"
              ></colloquio-detail>
            </div>
          </div>
        </div>
      </fade-transition>
    </el-card>
  </div>
</template>
<script>
import Vue from 'vue'

import Common from '@/components/Mastercom/Mixins/Common.js'
import Colloqui from '@/components/Mastercom/Mixins/Colloqui.js'
import DownloadItem from '@/components/Mastercom/UIComponents/DownloadItem.vue'

import CloneDeep from 'lodash/cloneDeep'

// import ColloquiPrenotaPanel from '@/components/Mastercom/UIComponents/Panels/ColloquiPrenotaPanel'
import ColloquioDetail from '@/components/Mastercom/UIComponents/ColloquioDetail'
import Insegnante from '@/components/Mastercom/UIComponents/Insegnante.vue'
import SelectSlotColloqui from '@/components/Mastercom/UIComponents/SelectSlotColloqui'
import { Spinner, Card, Select, Option } from 'element-ui'

Vue.use(Card)

export default {
  name: 'ColloquiGeneraliPanel',
  mixins: [Common, Colloqui],
  components: {
    SelectSlotColloqui,
    ColloquioDetail,
    DownloadItem,
    Insegnante,
    ElSelect: Select,
    ElOption: Option,
    Spinner
  },
  watch: {
    preferenzePanelVisible(oldVal, newVal) {
      return newVal
    }
  },
  computed: {
    giornataColloqui() {
      return this.colloqui
    },
    elencoInsegnantiGenerali() {
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      let sortBy = [
        {
          prop: 'prenotato_generali',
          direction: -1
        },
        {
          prop: 'materia',
          direction: 1
        }
      ]
      let elenco = CloneDeep(this.elencoInsegnanti)
      return elenco.sort((a, b) => {
        let i = 0,
          result = 0
        while (i < sortBy.length && result === 0) {
          result =
            sortBy[i].direction *
            (a[sortBy[i].prop].toString() < b[sortBy[i].prop].toString()
              ? -1
              : a[sortBy[i].prop].toString() > b[sortBy[i].prop].toString()
              ? 1
              : 0)
          i++
        }
        return result
      })
    }
  },
  data() {
    return {
      lastIdMateria: 0,
      isPanelVisible: false,
      dataColloquio: null
    }
  },
  methods: {
    isUpdating(item) {
      if (this.dataColloquio === null) {
        return false
      }
      if (item.colloqui_generali === null) {
        return false
      }

      if (item.colloqui_generali.length === 0) {
        return false
      }
      if (this.dataColloquio in item.colloqui_generali === false) {
        return false
      }
      if (item.colloqui_generali[this.dataColloquio] === null) {
        return false
      }
      for (let c of item.colloqui_generali) {
        if (c.id === this.updatingColloquio) {
          return true
        }
      }
      return false
    },
    getColloquioClass(item) {
      if (this.isUpdating(item)) {
        return 'aggiornamento'
      }
      return ''
    },
    isColloquioPrenotato(c) {
      return c.prenotato_generali
    },
    getColloquioPrenotato(colloqui) {
      let colloquio = colloqui.filter(c => {
        return c.stato === 'prenotato'
      })
      if (colloquio.length) {
        return colloquio[0]
      }
      return null
      // return colloqui.filter(c => c.stato === 'prenotato')
    },
    showSlotSelector(colloqui) {
      if (colloqui.length < 1) {
        return false
      }

      colloqui = colloqui.filter(c =>
        this.$dayjs(c.data).isSame(
          this.dateColloquiGenerali[this.dataColloquio],
          'day'
        )
      )
      return colloqui.length > 0 ? true : false
    },
    showIntestazioneMateria(idMateria) {
      if (this.lastIdMateria !== idMateria) {
        this.lastIdMateria = idMateria
        return true
      }
      return false
    },
    getSlotOrari(colloqui) {
      // return colloqui.filter(c =>
      //   this.$dayjs(c.data).isSame(
      //     this.dateColloquiGenerali[this.dataColloquio],
      //     'day'
      //   )
      // )[0]
      return colloqui
    },
    doLoadColloquiFiltrati() {
      this.showLoader()
      this.loadColloqui(this.authObject).finally(() => {
        this.hideLoader()
      })
    },
    filtraColloquiGenerali() {
      let d = this.dateColloquiGenerali[this.dataColloquio]
      this.setDataInizio(d.format('YYYY-MM-DD'))
      this.setDataFine(d.add(1, 'day').format('YYYY-MM-DD'))

      this.doLoadColloquiFiltrati()
    }
  },
  created() {
    if (this.dateColloquiGenerali.length) {
      this.dataColloquio = 0
    }
  }
}
</script>
<style lang="scss" scoped>
.header {
  font-weight: 700;
  margin-bottom: 10px;
  padding: 5px;
  border-radius: 4px;
  background-color: #f4f3ef;
}
.print-button {
  text-align: right;
  font-weight: 700;
}
.card {
  .insegnante {
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 10px;
    padding-top: 10px;
    &:nth-of-type(even) {
      background-color: #f8f8f8;
    }
    h6 {
      margin-bottom: 0px;
    }
    .azioni {
      padding-top: 17px;
    }
  }
}
.contenuto {
  padding-top: 30px;
  display: inline-block;
}
.content-block {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 0.3em;
  padding-bottom: 0.3em;
  border-bottom: 1px solid #d5d5d5;
  .colloquio-group-label {
    display: flex;
    flex-basis: 50%;
    align-items: center;
    font-weight: 700;
  }
  .colloquio-select {
    display: flex;
    flex-basis: 50%;
    align-items: center;
  }
  .note {
    flex-basis: 100%;
    width: 100%;
    font-size: 90%;
  }
}
.aggiornamento {
  background-color: #cdcdcd !important;
  transition: background-color 300ms linear;
}

.colloquio-group-label {
  padding-top: 10px;
}
</style>
