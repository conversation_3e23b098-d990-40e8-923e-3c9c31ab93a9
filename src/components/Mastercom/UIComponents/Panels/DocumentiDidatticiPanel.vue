<template>
  <div v-if="loaded">
    <div v-if="documenti.length">
      <h5 class="col-xs-3">
        <i class="ti-calendar"></i>
        Data
      </h5>
      <h5 class="col-xs-7">
        <i class="ti-import"></i>
        Download
      </h5>
      <fade-transition group tag="div" :duration="250">
        <div
          v-for="documento in documenti"
          :key="documento.uid"
          class="row document-list"
        >
          <div class="col-xs-3">
            {{ documento.data | fulldate }}
          </div>
          <div class="col-xs-7">
            <download-item
              :url="documento.url"
              :descrizione="documento.descrizione"
            ></download-item>
          </div>
        </div>
      </fade-transition>
    </div>
  </div>
  <div v-else>Loading</div>
</template>

<script>
import Vue from 'vue'
import { Card } from 'element-ui'
import Common from '@/components/Mastercom/Mixins/Common'
// import AllegatiList from '@/components/Mastercom/UIComponents/AllegatiList.vue'
import { mapGetters, mapActions } from 'vuex'
import DownloadItem from '@/components/Mastercom/UIComponents/DownloadItem'
Vue.use(Card)

export default {
  name: 'Documenti',
  components: {
    DownloadItem
  },
  mixins: [Common],
  data() {
    return {
      loaded: true
    }
  },
  computed: {
    ...mapGetters({
      documenti: 'documenti/all'
    })
  },
  methods: {
    doLoadDocumenti() {
      this.loaded = false
      this.showLoader()
      this.initScuolaStudente(this.authObject)
        .then(() => {
          return this.isSectionEnabled('documenti')
        })
        .then(enabled => {
          if (enabled === true) {
            this.loadDocumenti(this.authObject)
          }
          return enabled
        })
        .then(enabled => {
          this.loaded = enabled
        })
        .catch(error => {
          this.doSetError(error)
        })
        .finally(() => {
          this.hideLoader()
        })
    },
    ...mapActions({
      loadDocumenti: 'documenti/loadDocumenti'
    })
  },
  mounted() {
    this.doLoadDocumenti()
  }
}
</script>

<style lang="scss" scoped>
.document-list {
  text-align: left;
  padding-bottom: 10px;
  padding-top: 10px;
  border-bottom: 1px solid #f8f8f8;
  color: #666666;

  &:nth-of-type(even) {
    background-color: #f8f8f8;
  }
}
</style>
