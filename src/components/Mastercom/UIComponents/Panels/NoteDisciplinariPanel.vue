<template>
  <div @click.stop="gotoNote()" class="clickable">
    <mini-card card-color="purple">
      <h6 slot="header">Note Disciplinari</h6>
      <div class="numbers" slot="content">
        {{ noteDisciplinari }}
      </div>
      <div slot="footer">
        <p class="category" v-if="loading">
          <spinner :radius="30"></spinner>
          Loading
        </p>
        <p class="category" v-else>
          <span class="ti-time"></span>
          {{ lastTimeLoaded | simpletime }}
        </p>
      </div>
    </mini-card>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common.js'
import MiniCard from '@/components/Mastercom/UIComponents/MiniCard'

import { mapState, mapGetters, mapActions } from 'vuex'
import { Spinner } from 'element-ui'

export default {
  name: 'NoteDisciplinariPanel',
  mixins: [Common],
  components: {
    MiniCard,
    Spinner
  },
  data() {
    return {
      loading: false,
      lastTimeLoaded: null,
      noteDisciplinari: 0
    }
  },
  watch: {
    aggiornamenti() {
      if ('note' in this.aggiornamenti) {
        this.noteDisciplinari = this.aggiornamenti.note.numero
      }
    }
  },
  computed: {
    ...mapState({
      aggiornamenti: state => state.aggiornamenti.all
    }),
    ...mapGetters({
      authObject: 'main/authObject'
    })
  },
  methods: {
    gotoNote() {
      this.setSoloNovita(true)
      this.$router.push('note-disciplinari')
    },
    ...mapActions({
      setSoloNovita: 'noteDisciplinari/setSoloNovita'
    })
  }
}
</script>

<style></style>
