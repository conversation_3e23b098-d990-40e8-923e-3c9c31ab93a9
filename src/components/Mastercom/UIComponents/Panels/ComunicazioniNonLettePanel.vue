<template>
  <div
    @click.stop="gotoComunicazioni"
    v-if="studenteCorrente.servizi.comunicazioni"
  >
    <mini-card card-color="blue">
      <h6 slot="header">Comunicazioni con richiesta conferma lettura</h6>
      <div
        class="numbers"
        slot="content"
        v-number-transition="{
          target: comunicazioniDisplay || 0,
          iteration: comunicazioniDisplay || 1,
          speed: 2000
        }"
      ></div>
      <div slot="footer">
        <p class="category" v-if="loading">
          <spinner :radius="30"></spinner>
          Loading
        </p>
        <p class="category" v-else>
          <span class="ti-time"></span>
          {{ lastTimeLoaded | simpletime }}
        </p>
      </div>
    </mini-card>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common.js'
import MiniCard from '@/components/Mastercom/UIComponents/MiniCard'
import NumberTransition from 'vue-number-transition'

import { mapGetters, mapActions } from 'vuex'
import { Spinner } from 'element-ui'

export default {
  name: 'ComunicazioniPanel',
  directives: {
    NumberTransition
  },
  mixins: [Common],
  components: {
    MiniCard,
    Spinner
  },
  data() {
    return {
      loading: false,
      lastTimeLoaded: null
    }
  },
  computed: {
    comunicazioniDisplay() {
      return this.comunicazioni.length > 0 ? this.comunicazioni.length : 0
    },
    ...mapGetters({
      comunicazioni: 'comunicazioni/richiesta_presa_visione',
      authObject: 'main/authObject'
    })
  },
  methods: {
    doLoadComunicazioni() {
      this.loading = true
      this.setDataInizio(
        this.$dayjs(this.filter.last90.data_inizio).format('YYYY-MM-DD')
      )
      this.setDataFine(
        this.$dayjs(this.filter.last90.data_fine).format('YYYY-MM-DD')
      )
      this.setProfileUpdate(false)
      this.loadComunicazioni(this.authObject)
        .then(() => {
          this.lastTimeLoaded = this.$dayjs()
        })
        .finally(() => {
          this.loading = false
          this.setProfileUpdate(true)
        })
    },
    gotoComunicazioni() {
      this.$router.push('Comunicazioni')
    },
    ...mapActions({
      loadComunicazioni: 'comunicazioni/loadComunicazioni',
      setProfileUpdate: 'comunicazioni/setProfileUpdate',
      setDataInizio: 'comunicazioni/setDataInizio',
      setDataFine: 'comunicazioni/setDataFine'
    })
  },
  mounted() {
    this.doLoadComunicazioni()
  }
}
</script>

<style></style>
