<template>
  <div>
    <el-card class="box-card card">
      <div slot="header" class="row">
        <h6 class="col-xs-9">{{ $t('colloqui.title') }}</h6>
        <div class="col-xs-3 print-button">
          <download-item
            :url="urlColloquiPrenotati"
            :icons="{ idle: 'ti-printer' }"
            :descrizione="$t('colloqui.label_print_elenco')"
          ></download-item>
        </div>
      </div>

      <div class="row header" v-if="elencoInsegnanti.length">
        <div class="col-xs-12 col-sm-3 col-lg-3">
          {{ $t('colloqui.label_insegnante') }}
        </div>
        <div class="col-xs-12 col-sm-3 col-lg-3">
          {{ $t('colloqui.label_ultimo_colloquio') }}
        </div>
        <div class="col-xs-12 col-sm-3 col-lg-3">
          {{ $t('colloqui.label_prossimo_colloquio') }}
        </div>
        <div class="col-xs-12 col-sm-3 col-lg-3">{{ $t('main.actions') }}</div>
      </div>
      <fade-transition group tag="div" :duration="150">
        <div
          v-for="(item, index) in elencoInsegnanti"
          :key="index"
          class="row insegnante"
        >
          <div class="col-xs-12 col-sm-3 col-lg-3">
            <insegnante :nome="item.nome" :titolo="item.titolo"></insegnante>
            {{ item.materia }}
          </div>
          <div class="col-xs-12 col-sm-3 col-lg-3">
            <span class="contenuto"></span>
          </div>
          <div class="col-xs-12 col-sm-3 col-lg-3">
            <colloquio-detail
              v-if="item.prossimoColloquio"
              :item="item.prossimoColloquio"
            ></colloquio-detail>
          </div>
          <div class="col-xs-12 col-sm-3 col-lg-3 azioni">
            <colloqui-prenota-panel
              class="azione"
              :id-professore="item.id"
              :colloquio="item"
              :prenotato="item.prenotato"
              v-if="item.colloqui.length"
            ></colloqui-prenota-panel>
            <span
              v-if="
                richiestaColloquiIndividuali == false && !item.colloqui.length
              "
            >
              {{ $t('colloqui.text_non_disponibili') }}
            </span>

            <richiesta-colloquio-btn
              v-if="richiestaColloquiIndividuali && !item.prossimoColloquio"
              class="azione"
              :id-professore="item.id"
            ></richiesta-colloquio-btn>
          </div>
        </div>
      </fade-transition>
    </el-card>
  </div>
</template>
<script>
import Vue from 'vue'

import Common from '@/components/Mastercom/Mixins/Common.js'
import Colloqui from '@/components/Mastercom/Mixins/Colloqui.js'
import DownloadItem from '@/components/Mastercom/UIComponents/DownloadItem.vue'
import RichiestaColloquioBtn from '@/components/Mastercom/UIComponents/RichiestaColloquioBtn'
import ColloquiPrenotaPanel from '@/components/Mastercom/UIComponents/Panels/ColloquiPrenotaPanel'
import ColloquioDetail from '@/components/Mastercom/UIComponents/ColloquioDetail'
import Insegnante from '@/components/Mastercom/UIComponents/Insegnante.vue'

import { Card } from 'element-ui'

Vue.use(Card)

export default {
  name: 'ColloquiPanel',
  mixins: [Common, Colloqui],
  components: {
    ColloquiPrenotaPanel,
    ColloquioDetail,
    DownloadItem,
    RichiestaColloquioBtn,
    Insegnante
  },
  watch: {
    preferenzePanelVisible(oldVal, newVal) {
      return newVal
    }
  },
  data() {
    return {
      lastIdMateria: 0,
      isPanelVisible: false
    }
  },
  methods: {
    showIntestazioneMateria(idMateria) {
      if (this.lastIdMateria !== idMateria) {
        this.lastIdMateria = idMateria
        return true
      }
      return false
    }
  }
}
</script>
<style lang="scss" scoped>
.header {
  font-weight: 700;
  margin-bottom: 10px;
  padding: 5px;
  border-radius: 4px;
  background-color: #f4f3ef;
}
.print-button {
  text-align: right;
  font-weight: 700;
  text-transform: uppercase;
}
.card {
  .insegnante {
    border-bottom: 1px solid #ebeef5;
    padding-bottom: 10px;
    padding-top: 10px;
    &:nth-of-type(even) {
      background-color: #f8f8f8;
    }
    h6 {
      margin-bottom: 0px;
    }
    .azioni {
      .azione {
        float: left;
        margin-bottom: 10px;
        width: 99%;
        border: 0 !important;
      }
    }
  }
}
.contenuto {
  padding-top: 30px;
  display: inline-block;
}
</style>
