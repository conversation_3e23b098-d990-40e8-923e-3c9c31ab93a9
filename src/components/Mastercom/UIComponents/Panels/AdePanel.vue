<template>
  <div @click.stop="gotoAdE()" class="clickable">
    <mini-card card-color="dark-blue">
      <h6 slot="header">DICHIARAZIONE DATI PAGANTE AGENZIA DELLE ENTRATE</h6>
      <div slot="text" class="text">
        Accedi a questa sezione per inviare alla scuola la dichiarazione
        obbligatoria dei dati del pagante.
      </div>
      <div slot="footer">
        <p class="category">
          <span class="ti-arrow-right"></span>
          ENTRA
        </p>
      </div>
    </mini-card>
  </div>
</template>

<script>
import MiniCard from '@/components/Mastercom/UIComponents/MiniCard'

export default {
  name: 'AdEPanel',
  components: {
    MiniCard
  },
  methods: {
    gotoAdE() {
      this.$router.push('/agenzia-entrate')
    }
  }
}
</script>

<style lang="scss" scoped>
.card {
  &[data-background-color='dark-blue'] {
    background-color: #06c;
    color: #fff;
  }
  .text {
    font-size: 1.2em;
    padding: 15px 0;
  }
}
</style>
