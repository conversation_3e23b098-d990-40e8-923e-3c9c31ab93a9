<template>
  <div class="table-list col-xs-12" v-loading="panelLoading">
    <div class="row fatture-list table-header">
      <div class="col-xs-1">Num.</div>
      <div class="col-xs-2">Data emissione</div>
      <div class="col-xs-2">Data scadenza</div>

      <div class="col-xs-2">Intestatario</div>
      <div class="col-xs-2">Importo</div>
      <div class="col-xs-2">Allegato</div>
    </div>
    <fade-transition
      group
      tag="div"
      class=""
      :duration="250"
      v-if="docDisplay.length"
    >
      <div
        class="row fatture-list"
        v-for="(item, index) in docDisplay"
        :key="`${indexName}_${index}`"
      >
        <div class="col-xs-1">{{ item.numero }}</div>
        <div class="col-xs-2">{{ item.data_emissione | simpledate }}</div>
        <div class="col-xs-2">{{ item.data_scadenza | simpledate }}</div>
        <div class="col-xs-2">{{ item.intestatario }}</div>
        <div class="col-xs-2 currency">{{ item.totale }}</div>
        <div class="col-xs-3">
          <download-item
            :url="item.allegati[0].allegato"
            :descrizione="item.allegati[0].descrizione"
          ></download-item>
        </div>
      </div>
    </fade-transition>
    <div v-else>
      <div class="row">
        <div class="col-xs-12">Nessun documento disponibile</div>
      </div>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common'
import Pagamenti from '@/components/Mastercom/Mixins/Pagamenti'
import DownloadItem from '@/components/Mastercom/UIComponents/DownloadItem'
import { Loading } from 'element-ui'
Vue.use(Loading)
export default {
  name: 'FatturePanel',
  mixins: [Common, Pagamenti],
  computed: {
    docDisplay() {
      return this.ndc === true ? this.noteCreditoDisplay : this.fattureDisplay
    },
    indexName() {
      return this.ndc === true ? 'note_credito' : 'fatture'
    }
  },
  components: {
    DownloadItem
  },
  props: {
    ndc: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {}
  },
  mounted() {
    this.doLoadFatture()
  }
}
</script>

<style lang="scss" scoped></style>
