<template>
  <div class="mastercom" id="comunicazioni-main-panel">
    <div class="col-xs-12">
      <el-card class="box-card card">
        <div slot="header" class="row">
          <h6 class="col-xs-9">
            {{ $t('pagamenti.title') }}
          </h6>
          <carrello class="col-xs-3" v-if="carrelloDisplay"></carrello>
        </div>
        <div class="card-content">
          <vue-tabs class="row pagamenti-tabs">
            <v-tab
              :title="$t('pagamenti.estratto_conto.title')"
              id="articoli"
              icon="ti-wallet"
            >
              <estratto-conto-panel></estratto-conto-panel>
            </v-tab>
            <v-tab
              :title="$t('pagamenti.marketplace.title')"
              id="pagamenti"
              icon="ti-package"
            >
              <marketplace-panel v-loading="panelLoading"></marketplace-panel>
            </v-tab>
            <v-tab
              :title="$t('pagamenti.estratto_conto_crediti.title')"
              id="crediti"
              icon="ti-money"
              v-if="estrattoContoCrediti.length > 0"
            >
              <estratto-conto-crediti-panel
                v-if="estrattoContoCrediti.length > 0"
                v-loading="panelLoading"
              ></estratto-conto-crediti-panel>
            </v-tab>
          </vue-tabs>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common'
import Pagamenti from '@/components/Mastercom/Mixins/Pagamenti'
import EstrattoContoPanel from '@/components/Mastercom/UIComponents/Panels/EstrattoContoPanel'
import MarketplacePanel from '@/components/Mastercom/UIComponents/Panels/MarketplacePanel'
import EstrattoContoCreditiPanel from '@/components/Mastercom/UIComponents/Panels/EstrattoContoCreditiPanel'
import Carrello from '@/components/Mastercom/UIComponents/Carrello'
import VueTabs from 'vue-nav-tabs'
Vue.use(VueTabs)
export default {
  name: 'PagamentiPanel',
  components: {
    EstrattoContoPanel,
    EstrattoContoCreditiPanel,
    MarketplacePanel,
    Carrello
  },
  mixins: [Common, Pagamenti],
  data() {
    return {
      loading: false
    }
  },
  mounted() {
    this.doLoadEstrattoContoCrediti()
  }
}
</script>

<style lang="scss" scoped>
.disabled {
  display: none;
}
</style>
