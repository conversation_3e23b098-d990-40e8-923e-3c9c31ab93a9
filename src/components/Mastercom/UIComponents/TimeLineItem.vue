<template>
  <li :class="`date_${data}`">
    <slot name="month-header" v-if="monthHeader" class="clearfix">
      <div class="timeline-badge" :class="badgeType">
        <span></span>
      </div>

      <div :class="['timeline-panel', 'month-panel', `header-${monthHeader}`]">
        {{ monthHeader }}
      </div>
      <div class="clearfix"></div>
    </slot>
    <slot name="badge">
      <div class="timeline-badge" :class="badgeType" v-if="badgeText">
        <span>{{ badgeText }}</span>
      </div>
      <div class="no-badge" v-else></div>
    </slot>

    <div class="timeline-panel">
      <div class="timeline-heading" v-if="hasHeader">
        <slot name="header"></slot>
      </div>
      <div class="timeline-subtitle">
        <slot name="title"></slot>
      </div>
      <div class="timeline-body">
        <slot name="body"></slot>
      </div>
      <h6>
        <slot name="footer" v-if="hasFooter"></slot>
      </h6>
    </div>
  </li>
</template>
<script>
export default {
  props: {
    data: {
      type: String,
      default: ''
    },
    badgeType: {
      type: String,
      default: ''
    },
    badgeIcon: {
      type: String,
      default: ''
    },
    badgeText: {
      type: String,
      default: ''
    },
    monthHeader: {
      type: String,
      default: ''
    },
    monthHeaderBadge: {
      type: String,
      default: ''
    },
    monthHeaderDate: {
      type: String,
      default: ''
    }
  },
  methods: {
    hasHeader() {
      return !!this.$slots.header
    },
    hasFooter() {
      return !!this.$slots.footer
    }
  }
}
</script>
<style lang="scss" scoped>
.month-panel {
  &:before,
  &:after {
    display: none !important;
  }
  height: 150px;
  font-size: 2em;
  font-weight: 700;
  text-align: right;
  color: #fff !important;
  line-height: 100px;
  background: blue !important;
}

.header-aprile {
  background: url('/static/img/months-header/aprile.png') !important;
  background-size: cover !important;
}
.no-badge {
  float: left;
  width: 50px;
  left: 5%;
  position: relative;
  padding: 5px;
  margin-left: -25px;
}
</style>
