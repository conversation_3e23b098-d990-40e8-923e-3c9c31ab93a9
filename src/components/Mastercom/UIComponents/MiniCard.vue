<template>
  <div class="card" :data-background-color="cardColor">
    <div class="card-content">
      <div class="row">
        <div class="col-xs-12 col-lg-8">
          <slot name="header"></slot>
        </div>
        <div class="col-xs-12 col-lg-4">
          <slot name="content"></slot>
        </div>
        <div class="col-xs-12">
          <slot name="text"></slot>
        </div>
      </div>
    </div>
    <div class="card-footer">
      <hr />
      <slot name="footer"></slot>
    </div>
  </div>
</template>
<script>
export default {
  name: 'MiniCard',
  props: {
    cardColor: {
      type: String,
      default: ''
    }
  }
}
</script>
<style scoped lang="scss">
.card-content {
  min-height: 85px;
}
</style>
