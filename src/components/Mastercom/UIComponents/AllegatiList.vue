<template>
  <div class="allegati" v-if="allegati.length" :class="customClass">
    <h6>Allegati</h6>
    <ul>
      <li v-for="(allegato, index) in allegati" :key="`${itemId}_${index}`">
        <download-item
          :url="allegato.allegato"
          :descrizione="allegato.descrizione"
        ></download-item>
      </li>
    </ul>
  </div>
</template>

<script>
import DownloadItem from '@/components/Mastercom/UIComponents/DownloadItem.vue'
export default {
  name: 'AllegatiList',
  components: {
    DownloadItem
  },
  props: {
    allegati: {
      type: Array,
      default: () => []
    },
    itemId: {
      type: String,
      default: null
    },
    customClass: {
      type: String,
      default: null
    }
  },
  methods: {
    downloadEventEmit(item) {
      this.$emit('downloadEvent', item)
    }
  }
}
</script>

<style lang="scss" scoped>
.allegati {
  margin-top: 5vh;
  border-top: 2px dashed #e2e2e2;
  ul {
    padding: 0;
    li {
      list-style-type: none;
      padding-bottom: 10px;
      margin-top: 20px;
      border-bottom: 1px solid #f0f0f0;
    }
  }
}
</style>
