<script>
import { Line, mixins } from 'vue-chartjs'
const { reactiveProp } = mixins

export default {
  extends: Line,
  mixins: [reactiveProp],
  // watch: {
  //   'chartData': {
  //     deep: true,
  //     immediate: true,
  //     handler (a, b) {
  //       if (this.$data._chart) {
  //         this.$data._chart.update()
  //       }
  //     }
  //   }
  // },
  props: {
    chartData: {
      type: Object,
      default: null
    },
    options: {
      type: Object,
      default: null
    }
  },
  mounted() {
    this.renderChart(this.chartData, this.options)
  }
}
</script>
