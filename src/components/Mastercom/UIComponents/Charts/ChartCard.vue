<template>
  <div class="card">
    <div class="card-header" v-if="$slots.header">
      <slot name="header"></slot>
    </div>
    <div class="card-content">
      <slot name="title-content">
        <div class="row">
          <div class="col-xs-7">
            <div class="numbers pull-left">
              <slot name="title"></slot>
            </div>
          </div>
          <div class="col-xs-5">
            <div class="pull-right">
              <slot name="title-label"></slot>
            </div>
          </div>
        </div>
      </slot>
      <slot name="subtitle"></slot>
      <div class="chart-container">
        <line-chart
          v-if="chartType === 'Line'"
          :loaded="loaded"
          :chart-data="chartData"
          :options="chartOptions"
        ></line-chart>
        <bar-line-chart
          v-if="chartType === 'BarLine'"
          :loaded="loaded"
          :chart-data="chartData"
          :options="chartOptions"
        ></bar-line-chart>
        <horizontal-bar-chart
          v-if="chartType === 'HorizontalBar'"
          :loaded="loaded"
          :chart-data="chartData"
          :options="chartOptions"
          :height="height"
          :width="width"
        ></horizontal-bar-chart>
      </div>
    </div>

    <div class="card-footer">
      <slot name="footer">
        <hr />
        <div class="footer-title">
          <slot name="footer-title"></slot>
        </div>
        <div class="pull-right">
          <slot name="footer-right"></slot>
        </div>
      </slot>
    </div>
  </div>
</template>
<script>
import LineChart from './LineChart.vue'
import BarLineChart from './BarLineChart.vue'
import HorizontalBarChart from './HorizontalBarChart.vue'

export default {
  name: 'ChartCardLine',
  components: {
    LineChart,
    BarLineChart,
    HorizontalBarChart
  },
  props: {
    height: {
      type: Number,
      default: null
    },
    width: {
      type: Number,
      default: null
    },
    footerText: {
      type: String,
      default: ''
    },
    headerTitle: {
      type: String,
      default: 'Chart title'
    },
    chartType: {
      type: String,
      default: 'Line' // Line | Pie | Bar
    },
    chartData: {
      type: Object,
      default: () => {
        return {
          labels: [],
          data: []
        }
      }
    },
    chartOptions: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loaded: false
    }
  },
  methods: {
    initChart() {
      this.loaded = true
    },
    /***
     * Assigns a random id to the chart
     */
    updateChartId() {
      var currentTime = new Date().getTime().toString()
      var randomInt = this.getRandomInt(0, currentTime)
      this.chartId = `div_${randomInt}`
    },
    getRandomInt(min, max) {
      return Math.floor(Math.random() * (max - min + 1)) + min
    }
  },
  async mounted() {
    this.updateChartId()
    this.initChart()
  }
}
</script>
<style></style>
