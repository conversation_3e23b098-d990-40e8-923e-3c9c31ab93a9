<script>
import { Bar, mixins } from 'vue-chartjs'
const { reactiveProp } = mixins

export default {
  extends: Bar,
  mixins: [reactiveProp],
  watch: {
    chartData: {
      deep: true,
      immediate: true,
      handler() {
        if (this.$data._chart) {
          this.$data._chart.update()
        }
      }
    }
  },
  methods: {
    doRenderChart() {
      this.renderChart(this.chartData, this.options)
    }
  },
  props: {
    chartData: {
      type: Object,
      default: null
    },
    options: {
      type: Object,
      default: null
    }
  },
  mounted() {
    this.doRenderChart()
  }
}
</script>
