<template>
  <div class="mastercom">
    <div class="col-xs-11">
      <slot name="descrizione"></slot>
      <slot name="importo"></slot>
    </div>

    <div class="col-xs-1">
      <div v-if="spinner">
        <spinner :radius="51" class="download-spinner"></spinner>
      </div>
      <div v-else>
        <button
          class="btn btn-sm btn-icon btn-success"
          :class="buttonClass"
          v-if="isPastoPrenotabile"
          @click.stop.prevent="doPrenotaServizio"
        >
          <i :class="iconClass"></i>
        </button>
        <span v-else>
          <button
            class="btn btn-sm btn-icon btn-fill btn-primary"
            v-if="selezionato"
          >
            <i :class="iconClass"></i>
          </button>
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common.js'
import Mense from '@/components/Mastercom/Mixins/Mense.js'
import { Spinner } from 'element-ui'
export default {
  name: 'DettaglioServizioPasto',
  mixins: [Common, Mense],
  components: {
    Spinner
  },
  props: {
    selezionato: {
      type: Boolean,
      default: false
    },
    codice: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      stoPrenotando: false
    }
  },
  computed: {
    buttonClass() {
      if (this.selezionato) {
        return 'btn-fill'
      }
      return ''
    },
    iconClass() {
      if (this.selezionato) {
        return 'ti-check'
      }
      return 'ti-close'
    },
    spinner() {
      return this.stoPrenotando
      // return true
    }
  },
  methods: {
    doPrenotaServizio() {
      this.stoPrenotando = true
      this.setServizioPrenotato(this.codice)
      this.prenotaPasto(this.authObject).finally(() => {
        this.stoPrenotando = false
        this.loadPastiFruiti(this.authObject)
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.color {
  color: hsl(133, 59%, 38%);
}
h5 {
  padding-top: 0;
}
/deep/ .download-spinner {
  &.el-spinner {
    .el-spinner-inner {
      .path {
        stroke: rgba(88, 167, 94, 0.8);
      }
    }
  }
}
</style>
