<template>
  <span
    class="btn btn-fill btn-sm has-padding"
    :class="{ 'btn-warning': loading, 'btn-success': !loading }"
    @click.stop="$emit('removing')"
  >
    <spinner :radius="25" v-if="loading"></spinner>
    <i class="ti-close" v-else></i>
    {{ fileName }}
  </span>
</template>

<script>
import { Spinner } from 'element-ui'
export default {
  name: 'UploadListItem',
  components: {
    Spinner
  },
  props: {
    loading: {
      type: Boolean,
      default: false
    },
    fileName: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.has-padding {
  float: left;
  margin-right: 8px;
  margin-bottom: 8px;
}
</style>
