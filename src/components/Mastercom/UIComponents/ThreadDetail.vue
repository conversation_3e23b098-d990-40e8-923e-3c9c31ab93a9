<template>
  <div class="comunicazioni-detail" :class="canAnswerClass">
    <div
      v-for="item in thread"
      :key="item.id_messaggio"
      class="bubble"
      :class="item.lettore ? 'lettore' : ''"
    >
      <div class="mittente">
        <h6>
          <span class="ti-user"></span>
          <span v-if="item.mittente !== null">
            {{ item.mittente.nome }} {{ item.mittente.cognome }}
          </span>
          <span v-else>{{ item.sottotitolo }}</span>
        </h6>
      </div>
      <div class="body">
        <div v-html="item.dettaglio"></div>
      </div>
      <div>
        <allegati-list
          :allegati="item.allegati"
          :item-id="item.id.toString()"
          custom-class="allegati-thread"
        ></allegati-list>
      </div>
      <div class="data">
        <span class="ti-time"></span>
        {{ item.data | datetime }}
      </div>
    </div>
  </div>
</template>

<script>
import Common from '@/components/Mastercom/Mixins/Common'
import Notifications from '@/components/Mastercom/Mixins/Notifications.js'
import AllegatiList from '@/components/Mastercom/UIComponents/AllegatiList.vue'

import { mapActions } from 'vuex'
export default {
  name: 'ThreadDetail',
  mixins: [Common, Notifications],
  components: {
    AllegatiList
  },
  computed: {
    canAnswerClass() {
      return ''
    }
  },
  props: {
    thread: {
      type: Array,
      default: null
    }
  },
  methods: {
    ...mapActions({
      loadAllegato: 'comunicazioni/loadAllegato',
      setUrlAllegato: 'comunicazioni/setUrlAllegato'
    })
  }
}
</script>

<style scoped lang="scss">
/deep/ .allegati-thread {
  margin-top: 1vh;
}
.bubble {
  margin-bottom: 5vh;
  margin-left: 1vw;
  margin-right: 0;
  padding: 0;
  border-radius: 5px;
  background-color: #f5f5f5;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.2);
  float: left;
  max-width: 55%;
  clear: both;
  img {
    max-width: 100%;
  }
  .data {
    float: right;
    font-size: 80%;
    font-weight: bold;
    padding: 10px 20px;
  }
  &.lettore {
    background-color: #dbecf7;
    float: right;
    margin-right: 1vw;
    margin-left: 0;
    .data {
      float: left;
    }
  }
}
h4 {
  margin-top: 0;
  margin-bottom: 5vh;
}
.mittente {
  margin: 1vh 0;
}

.el-dialog__body {
  margin-top: 0;
  padding-top: 0;
}
</style>
