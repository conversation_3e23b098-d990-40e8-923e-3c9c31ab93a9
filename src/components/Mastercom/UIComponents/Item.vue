<template>
  <li class="row">
    <div class="col-xs-10 col-xs-offset-2" v-if="hasNovita()">
      <slot name="novita"></slot>
    </div>
    <div class="item-simbolo col-xs-2" v-if="hasSimbolo()">
      <slot name="simbolo"></slot>
    </div>
    <div class="item-panel col-xs-10" :class="hasAction() ? 'col-lg-8' : ''">
      <div class="item-header" v-if="hasHeader()">
        <slot name="header"></slot>
      </div>
      <h5 slot name="title" v-if="hasTitle()"></h5>
      <p class="item-body">
        <slot></slot>
      </p>

      <h6>
        <slot name="footer" v-if="hasFooter()"></slot>
      </h6>
    </div>
    <div class="col-xs-12 col-lg-2" v-if="hasAction()">
      <slot name="action"></slot>
    </div>
  </li>
</template>

<script>
export default {
  methods: {
    hasHeader() {
      return !!this.$slots.header
    },
    hasFooter() {
      return !!this.$slots.footer
    },
    hasSimbolo() {
      return !!this.$slots.simbolo
    },
    hasNovita() {
      return !!this.$slots.novita
    },
    hasTitle() {
      return !!this.$slots.title
    },
    hasAction() {
      return !!this.$slots.action
    }
  }
}
</script>

<style scoped lang="scss">
.item-simbolo {
  padding-left: 0px;
}
</style>
