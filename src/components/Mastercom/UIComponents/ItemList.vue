<template>
  <div class="card">
    <div class="card-header">
      <h4 class="card-title">{{ titolo }}</h4>
      <p class="category">{{ sottotitolo }}</p>
    </div>
    <slot name="filters"></slot>

    <fade-transition tag="div" class="card-content">
      <fade-transition group tag="ul" class="item-list" :duration="150">
        <item v-for="(item, index) in oggetti" :key="index" :data="item">
          <div
            class="btn btn-danger btn-fill btn-xs"
            slot="novita"
            v-if="item.novita"
          >
            <span class="ti-shine"></span>
            Novità
          </div>
          <badge
            slot="simbolo"
            :background-color="item.colore_simbolo"
            v-if="item.simbolo"
          >
            {{ item.simbolo }}
          </badge>
          <div v-if="item.id_materia">
            <materia
              slot="header"
              :titolo="item.materia"
              :indice-colore="item.indice_colore"
            ></materia>
            <insegnante :nome="item.insegnante"></insegnante>
          </div>
          <span v-else>
            <h6 v-if="item.sottotitolo">{{ item.sottotitolo }}</h6>
            {{ item.titolo }}
            <div class="confirm-container" v-if="onConfirmAction">
              <button
                class="btn btn-sm btn-primary btn-fill"
                v-if="item.confirmed === false"
                @click.prevent="onConfirmAction(item)"
              >
                {{ confirmActionText }}
              </button>
              <div class="confirmed" v-else>{{ item.confirm_text }}</div>
            </div>
          </span>
          {{ item.descrizione }}
          <div v-if="tag" class="tag text-info">
            {{ item.tag.descrizione }}
          </div>
          <presenze-corso-detail
            v-if="dettaglioPresenzeCorso(item)"
            :detail="item.dettaglio"
          ></presenze-corso-detail>
          <assenze-dad
            v-if="assenzeDad(item)"
            :detail="item.dettaglio"
          ></assenze-dad>
          <div v-if="item.motivazione" class="motivazione-assenza text-primary">
            <h6>{{ $t('assenze.motivazione_title_list') }}</h6>
            {{ item.motivazione }}
          </div>
          <div slot="actions">
            {{ item.actions }}
            <button
              class="btn btn-sm btn-primary btn-fill"
              v-if="openInMsgBox"
              @click.prevent="openDialog(item)"
            >
              dettaglio
            </button>
            <button class="btn btn-sm btn-fill" v-if="isPrenotazione(item)">
              Annulla
            </button>
          </div>
          <h6 slot="footer" v-if="false">
            <i :class="item.icona" v-if="item.icona"></i>
            {{ item.data | fulldate }}
          </h6>
          <div slot="footer" class="footer" v-if="hasFooter">
            <date-display
              v-if="hasDate"
              :novita="item.novita"
              :data="item.data"
              :formato-data="formatoData"
            ></date-display>
          </div>
          <button
            class="btn btn-icon btn-sm btn-fill btn-info"
            v-if="openInMsgBox"
            @click="openDialog(item)"
          >
            <span class="ti-eye"></span>
          </button>
          <button
            class="btn btn-icon btn-sm btn-fill btn-download"
            @click="parentAction(item)"
            v-if="
              item.prenotazione &&
                $dayjs().isSameOrBefore($dayjs(item.data), 'day')
            "
          >
            {{ $t('assenze.btn_annulla_elenco') }}
          </button>
          <button
            class="btn btn-icon btn-xs btn-fill"
            v-if="giustifica(item)"
            @click="openGiustificaAssenzaDialog(item)"
            slot="action"
          >
            {{ $t('assenze.button_list') }}
          </button>
        </item>
      </fade-transition>
    </fade-transition>
    <my-dialog
      :visible.sync="showDialog"
      :append-to-body="true"
      :width="dialogWidth"
      :top="dialogTop"
      :custom-class="dialogClass"
    >
      <my-pagella>
        <h6 slot="header">{{ dialogTitle }}</h6>
        <div v-html="dialogContent"></div>
        <div v-if="dialogAttachments.length && showDownload === true">
          <allegati-list
            :allegati="dialogAttachments"
            :item-id="dialogContentId.toString()"
          ></allegati-list>
        </div>
      </my-pagella>
    </my-dialog>
    <my-dialog
      :visible.sync="showGiustificaAssenzeDialog"
      :append-to-body="true"
    >
      <div class="row">
        <h6 slot="header" class="col-xs-12" v-if="assenza">
          {{ $t('assenze.modal_title', [assenza.titolo]) }}
        </h6>
        <div class="col-xs-12">
          <radio
            v-for="(motivazioni_item, motivazioni_index) in motivazioniAssenze"
            :key="motivazioni_index"
            :data="motivazioni_item"
            v-model="motivazioneSelect"
            :label="motivazioni_item"
            class="col-xs-12 radio"
            @change="doShowMotivazioneText(false)"
            size="medium"
          >
            {{ motivazioni_item }}
          </radio>
          <radio
            class="col-xs-12 radio"
            @change="doShowMotivazioneText(true)"
            v-model="motivazioneSelect"
            :label="$t('assenze.label_altro')"
          >
            {{ $t('assenze.label_altro') }}
          </radio>
          <div class="col-xs-12 input-box">
            <my-input
              :placeholder="$t('assenze.placeholder')"
              v-model="motivazioneText"
              minlength="1"
              maxlength="256"
              size="medium"
              type="textarea"
              rows="2"
              resize="none"
              class="input-el"
              :disabled="showMotivazioneText == false"
            ></my-input>
          </div>
        </div>
        <div class="col-xs-12 text-center">
          <button
            class="btn btn-icon btn-fill"
            @click="doGiustificaAssenza(assenza)"
            slot="action"
            :disabled="buttonDisabled"
          >
            {{ $t('assenze.button_giustifica') }}
          </button>
        </div>
      </div>
    </my-dialog>
  </div>
</template>
<script>
import Materia from '@/components/Mastercom/UIComponents/Materia.vue'
import Insegnante from '@/components/Mastercom/UIComponents/Insegnante.vue'
import Item from '@/components/Mastercom/UIComponents/Item.vue'
import Badge from '@/components/Mastercom/UIComponents/Badge.vue'
import DateDisplay from '@/components/Mastercom/UIComponents/DateDisplay.vue'
import TemplatePagella from '@/components/Mastercom/UIComponents/TemplatePagella.vue'
import AllegatiList from '@/components/Mastercom/UIComponents/AllegatiList.vue'
import PresenzeCorsoDetail from '@/components/Mastercom/UIComponents/PresenzeCorsoDetail.vue'
import AssenzeDad from '@/components/Mastercom/UIComponents/AssenzeDad.vue'

import { Dialog, MessageBox, Radio, Input } from 'element-ui'
import { mapActions, mapGetters } from 'vuex'

import Common from '@/components/Mastercom/Mixins/Common.js'
export default {
  name: 'ItemList',
  mixins: [Common],
  components: {
    Item,
    Badge,
    DateDisplay,
    Materia,
    Insegnante,
    AllegatiList,
    PresenzeCorsoDetail,
    AssenzeDad,
    MyPagella: TemplatePagella,
    MyDialog: Dialog,
    Radio,
    MyInput: Input
  },
  data() {
    return {
      showDialog: false,
      showGiustificaAssenzeDialog: false,
      assenza: null,
      dialogTitle: null,
      dialogContent: null,
      dialogContentId: null,
      dialogAttachments: [],
      showMotivazioneText: false,
      motivazioneSelect: '',
      motivazioneText: ''
    }
  },
  computed: {
    hasFooter() {
      return this.hasDate
    },
    buttonDisabled() {
      return (
        (this.motivazioneSelect == 'ALTRO' && this.motivazioneText == '') ||
        this.motivazioneSelect == ''
      )
    },
    motivazioniAssenze() {
      if (this.assenza && this.assenza.tipo_assenza == 'assenza_dad') {
        return this._motivazioniAssenzeDAD
      }
      if (this.assenza && this.assenza.tipo_assenza != 'assenza_dad') {
        return this._motivazioniAssenze
      }
      return []
    },
    ...mapGetters({
      _motivazioniAssenze: 'assenze/motivazioniAssenze',
      _motivazioniAssenzeDAD: 'assenze/motivazioniAssenzeDAD'
    })
  },
  props: {
    parentAction: {
      type: Function,
      default: () => {}
    },
    titolo: {
      type: String,
      default: ''
    },
    sottotitolo: {
      type: String,
      default: ''
    },
    oggetti: {
      type: Array,
      default: () => []
    },
    openInMsgBox: {
      type: Boolean,
      default: false
    },
    downloadItem: {
      type: Boolean,
      default: false
    },
    hasDate: {
      type: Boolean,
      default: true
    },
    formatoData: {
      type: String,
      default: 'fulldate'
    },
    tag: {
      type: Boolean,
      default: false
    },
    onConfirmAction: {
      type: Function,
      default: null
    },
    confirmActionText: {
      type: String,
      default: ''
    },
    dialogWidth: {
      type: String,
      default: '50%'
    },
    dialogTop: {
      type: String,
      default: '10vh'
    },
    dialogClass: {
      type: String,
      default: 'dialog-item-list'
    },
    showDownload: {
      type: Boolean,
      default: true
    }
  },
  methods: {
    isPrenotazione(item) {
      return item.prenotazione
    },
    giustifica(item) {
      return item.giustificabile && item.giustificata == 'NO'
    },
    dettaglioPresenzeCorso(item) {
      return item.dettaglio_presenze_corso
    },
    assenzeDad(item) {
      return item.simbolo === 'a'
    },
    doShowMotivazioneText(active) {
      if (active) {
        this.showMotivazioneText = true
      } else {
        this.motivazioneText = ''
        this.showMotivazioneText = false
      }
    },
    openGiustificaAssenzaDialog(item) {
      this.showLoader()
      this.assenza = item
      this.loadMotivazioniAssenze(this.authObject)
        .then(() => {
          this.showGiustificaAssenzeDialog = true
        })
        .finally(() => {
          this.hideLoader()
        })
    },
    doGiustificaAssenza(item) {
      let title = `Giustifica ${item.titolo}`
      let motivazione = this.motivazioneText
        ? this.motivazioneText
        : this.motivazioneSelect
      MessageBox.confirm(title, {
        message: `${title} con la motivazione ${motivazione}`,
        confirmButtonText: 'ok',
        cancelButtonText: 'annulla',
        beforeClose: (action, instance, done) => {
          if (action == 'confirm') {
            this.setMotivazioneText(motivazione)
            this.setAssenzaDaGiustificare(item.id)
            this.postGiustificazione(this.authObject)
              .then(() => {
                this.loadAssenze(this.authObject)
              })
              .then(() => {
                this.motivazioneSelect = ''
                this.motivazioneText = ''
                this.showMotivazioneText = false
                this.showGiustificaAssenzeDialog = false
                this.$router.push('/assenze')
              })
              .then(() => done())
              .catch(error => {
                this.setError(error)
              })
          } else {
            done()
          }
        }
      })
      // .then(() => {
      //   this.setAssenzaDaGiustificare(item.id)
      //   return this.postGiustificazione(this.authObject)
      // })
      // .then(() => {
      //   this.$router.push('/assenze')
      // })
      // .catch(error => {

      //   this.setError(error)
      // })
    },
    hasDownload(item) {
      return this.downloadItem && item.allegati.length
    },
    openDialog(item) {
      this.dialogContent = item.dettaglio
      this.dialogTitle = item.titolo
      this.dialogAttachments = []
      if (item.allegati.length) {
        this.dialogAttachments = item.allegati
      }
      if ('id' in item) {
        this.dialogContentId = item.id
      } else {
        this.dialogContentId = this.$dayjs().unix()
      }
      this.showDialog = true
    },
    ...mapActions({
      postGiustificazione: 'assenze/postGiustificazione',
      setAssenzaDaGiustificare: 'assenze/setAssenzaDaGiustificare',
      loadMotivazioniAssenze: 'assenze/loadMotivazioniAssenze',
      loadAssenze: 'assenze/loadAssenze',
      setMotivazioneText: 'assenze/setMotivazioneText'
    })
  }
}
</script>

<style lang="scss" scoped>
ul {
  margin-left: 0;
  padding-left: 0;
}
.motivazione-assenza {
  font-size: 0.8em;
}
.tag {
  font-size: 0.8em;
  text-transform: uppercase;
}
.footer {
  padding-top: 3px;
}
.radio {
  label {
    font-size: 1.2em;
  }
  margin: 0.2em;
  padding: 0.4em 0;
  border-bottom: 1px solid #dedede;
}
.input-box {
  margin: 0.3em;
  padding: 0.5em 0;
}

.confirm-container {
  margin: 10px 0;
  .confirmed {
    color: #54b07d;
    font-size: 0.8em;
  }
}
</style>
