<template>
  <div>
    <div class="container cart-container" @click="doShowCart">
      <el-avatar>
        <i class="ti-shopping-cart"></i>
      </el-avatar>
      {{ qtaCarrello }}
    </div>
    <el-dialog
      :visible.sync="showCart"
      :append-to-body="true"
      :destroy-on-close="true"
      ref="dialogMsg"
      top="1vh"
      @close="closeMe"
    >
      <div slot="title">
        <h4>
          <i class="ti-shopping-cart"></i>
          Carrello
        </h4>
      </div>
      <div v-if="checkOutResult">
        <div class="row">
          <div class="col-xs-12 checkout">
            <h6 class="success">{{ checkOutResult }}</h6>
          </div>
          <div class="col-xs-12 checkout">
            <button class="btn btn-sm" @click.stop.prevent="showCart = false">
              {{ $t('main.close_modal') }}
            </button>
          </div>
        </div>
      </div>
      <div v-else>
        <p>{{ $t('pagamenti.carrello.testo') }}</p>
        <div
          v-for="(item, index) in articoliCarrelloDisplay"
          :key="`articoliCarrello_${index}`"
          class="row carrello-list"
        >
          <div class="col-xs-6">
            <span v-if="item.tipo == 'movimento'">{{ item.sottotitolo }}</span>
            <span v-else>{{ item.titolo }}</span>
          </div>
          <div class="col-xs-3">
            {{ item.prezzo | currency('EUR', 'it-IT') }}
          </div>
          <div class="col-xs-3">
            <button
              class="btn btn-sm btn-success"
              @click="removeFromCart(item.cart_id)"
            >
              <i class="ti-close"></i>
              {{ $t('pagamenti.carrello.label_rimuovi') }}
            </button>
          </div>
        </div>
        <div class="row">
          <div class="col-xs-6">
            <h4>{{ $t('pagamenti.carrello.totale') }}</h4>
          </div>
          <div class="col-xs-3">
            <h4>{{ totaleCarrello | currency('EUR', 'it-IT') }}</h4>
          </div>
        </div>
        <div class="row">
          <div v-if="displaySatispayButton || displayStripeButton">
            <div class="col-xs-12 checkout" v-if="displaySatispayButton">
              <h4 class="checkout-item">
                {{ $t('pagamenti.app_satispay_title') }}
              </h4>
              <img
                src="https://online.satispay.com/images/en-pay-red.svg"
                alt="Paga con Satispay"
                id="pay-with-satispay"
                style="height: 50px; cursor: pointer"
                @click.prevent="doSatispayOpen"
              />
            </div>
            <div class="col-xs-12 checkout" v-if="displayStripeButton">
              <h4 class="checkout-item">
                {{ $t('pagamenti.credit_card_title') }}
              </h4>
              <div class="col-xs-12 full checkout-item">
                <stripe-element-payment
                  ref="paymentRef"
                  :test-mode="stripeTestMode"
                  :pk="stripePK"
                  :elements-options="elementsOptions"
                  :confirm-params="confirmParams"
                  redirect="if_required"
                  @confirmed="stripeConfirmed"
                  @loading="stripeLoading"
                  @error="stripeError"
                />
              </div>
              <div
                class="full text-center checkout-item"
                v-if="stripeProcessing"
              >
                Pagamento in corso...
              </div>
              <div class="full text-center checkout-item" v-else>
                <button
                  class="btn btn-info btn-lg btn-fill"
                  @click.prevent="stripeConfirm"
                >
                  {{ $t('pagamenti.carrello.paga_stripe') }}
                </button>
              </div>
            </div>
          </div>
          <div class="col-xs-12 checkout" v-else>
            <button
              class="btn btn-success btn-lg btn-fill"
              @click="doCheckOut"
              :disabled="totaleCarrello == 0 || onlinePayments.length == 0"
            >
              {{ btnCheckoutText }}
            </button>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import Common from '@/components/Mastercom/Mixins/Common'
import Pagamenti from '@/components/Mastercom/Mixins/Pagamenti'
import { StripeElementPayment } from '@vue-stripe/vue-stripe'
import { Dialog, Avatar } from 'element-ui'

Vue.use(Avatar)
Vue.use(Dialog)
export default {
  name: 'Carrello',
  mixins: [Common, Pagamenti],
  components: {
    StripeElementPayment
  },
  data() {
    return {
      onlinePayments: [],
      satispayConfig: null,
      stripeConfig: null,
      stripeProcessing: false,
      stripeResult: null,
      showCart: false,
      displaySatispayButton: false,
      displayStripeButton: false,
      satispay: null,
      elementsOptions: {
        fonts: [
          {
            cssSrc: 'https://fonts.googleapis.com/css?family=Roboto'
          }
        ],
        // mode: 'payment',
        // currency: 'eur',
        // paymentMethodTypes: ['card'],
        // amount: 0,
        clientSecret: null
      },

      confirmParams: {},
      satispayWebButton: null,
      stringCheckoutText: this.$i18n.t('pagamenti.carrello.carrello_btn'),
      stringCheckoutWait: this.$i18n.t(
        'pagamenti.carrello.carrello_btn_waiting'
      ),
      stringCheckout: {
        PENDING: this.$i18n.t('pagamenti.carrello.pagamento_attesa'),
        ACCEPTED: this.$i18n.t('pagamenti.carrello.pagamento_confermato'),
        CANCELED: this.$i18n.t('pagamenti.carrello.pagamento_annullato')
      },
      btnCheckoutText: this.stringCheckoutText,
      checkOutResult: null
    }
  },
  computed: {
    articoliCarrelloDisplay() {
      let me = this
      let a = JSON.parse(JSON.stringify(this.articoli))
      let b = JSON.parse(JSON.stringify(this.marketplace))
      return a.concat(b).filter(x => me.carrello.includes(x.cart_id))
    },
    qtaCarrello() {
      return this.$i18n.tc('pagamenti.carrello.vuoto', this.carrello.length)
    }
  },
  methods: {
    stripeError() {},
    stripeLoading(event) {
      this.stripeProcessing = event
    },
    stripeConfirmed(event) {
      this.stripeProcessing = false
      let result = event.status
      if (result == 'succeeded') {
        result = 'ACCEPTED'
        this.stripeResult = true
      }
      this.checkOutResult = this.stringCheckout[result]
    },
    stripeConfirm() {
      this.$refs.paymentRef.submit()
    },
    closeMe() {
      if (this.satispayResult || this.stripeResult) {
        this.emptyCart()
      }
      this.stripeProcessing = false
      this.displaySatispayButton = false
      this.displayStripeButton = false
      this.btnCheckoutText = this.stringCheckoutText
      this.doLoadEstrattoConto()
      this.doLoadMarketplace()
      this.checkOutResult = null
    },
    doShowCart() {
      this.showCart = !this.showCart
    },
    satispaySetUp() {
      if (!this.isSatispayEnabled) {
        return false
      }

      this.loadSatispayConfig(this.authObject).then(() => {
        this.satispayWebButton = document.createElement('script')
        this.satispayWebButton.setAttribute('src', this.satispayScriptUrl)
        document.head.appendChild(this.satispayWebButton)
        this.satispayConfig = true
        this.onlinePayments.push('satispay')
        this.btnCheckoutText = this.stringCheckoutText
      })
    },
    stripeSetUp() {
      if (!this.isStripeEnabled) {
        return false
      }
      this.loadStripeConfig(this.authObject).then(() => {
        this.stripeConfig = true
        this.onlinePayments.push('stripe')
        this.btnCheckoutText = this.stringCheckoutText
      })
    },
    doCheckOut() {
      if (!this.isSatispayEnabled && !this.isStripeEnabled) {
        return false
      }
      let me = this
      this.btnCheckoutText = this.stringCheckoutWait
      if (this.isSatispayEnabled) {
        this.getSatispayPaymentId(this.authObject).then(result => {
          // eslint-disable-next-line no-undef
          this.satispay = window.SatispayWebButton.configure({
            paymentId: this.satispayPaymentId,
            completed: function() {
              me.displayPaymentDetail()
            }
          })
          this.displaySatispayButton = result
          this.btnCheckoutText = this.stringCheckoutText
        })
      }
      if (this.isStripeEnabled) {
        this.getStripePaymentIntent(this.authObject).then(result => {
          // this.elementsOptions.amount = this.totaleCarrello * 100
          this.elementsOptions.clientSecret = this.stripePaymentIntent
          this.displayStripeButton = result
        })
      }
    },
    displayPaymentDetail() {
      this.getSatispayPaymentDetail(this.authObject).then(() => {
        this.checkOutResult = this.stringCheckout[this.satispayResult]
      })
    },
    doSatispayOpen() {
      this.satispay.open()
    }
  },
  mounted() {
    this.stripeSetUp()
    this.satispaySetUp()
  }
}
</script>

<style lang="scss" scoped>
.container {
  float: left;
  width: auto;
}
.cart-container {
  justify-content: justify;
  display: flex;
  align-items: center;
  transition: color 0.2s ease-in-out;
  &:hover {
    color: green;
    cursor: pointer;
    .el-avatar {
      background-color: green;
    }
  }
  .el-avatar {
    transition: background-color 0.2s ease-in-out;
    margin-right: 20px;
  }
}

/deep/ .carrello-dialog {
  height: 96%;
}

.carrello-list {
  padding: 10px 0;
  border-bottom: 1px solid #c0c0c0;
  span {
    word-break: normal;
  }
}

.checkout {
  justify-content: center;
  display: flex;
  align-items: center;
  padding: 20px 0;
  flex-direction: column;
  align-items: center;
  align-content: space-between;
  .full {
    max-width: 70%;
  }
  .checkout-item {
    margin: 10px 0;
  }
}
</style>
