<template>
  <span :class="['ellipsis', 'label', 'materia', 'title', bg_class]">
    {{ display }}
  </span>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: '<PERSON><PERSON>',
  props: {
    classIndex: {
      type: Number,
      default: 0
    },
    idMateria: {
      type: Number,
      default: 0
    },
    displayField: {
      type: String,
      default: 'label'
    },
    titolo: {
      type: String,
      default: ''
    },
    indiceColore: {
      type: Number,
      default: 1
    }
  },
  computed: {
    display() {
      return this.titolo
    },
    bg_class() {
      return this.indiceColore === ''
        ? 'bg_1'
        : `bg_${parseInt(this.indiceColore)}`
    },
    ...mapGetters({
      materie: 'materie/materieStudenteCorrente'
    })
  }
}
</script>

<style scoped lang="scss">
.ellipsis {
  width: inherit;
  max-width: max-content;
}
</style>
