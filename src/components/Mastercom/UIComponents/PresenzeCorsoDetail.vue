<template>
  <div>
    <div v-if="elencoOre" class="elenco-ore">
      <h6>ELENCO CORSI</h6>
      <div v-for="(item, index) in elencoOre" :key="index" class="row ore">
        <div class="col-xs-1">
          <badge
            mini
            slot="simbolo"
            :background-color="item.colore"
            v-if="item.stato"
          >
            {{ item.stato }}
          </badge>
        </div>
        <div class="col-xs-7">
          <h6>{{ item.corso }}</h6>
          <div class="dettaglio-assenza">
            <span v-if="item.stato === 'E'">Entrata in ritardo</span>
            <span v-if="item.stato === 'U'">Uscita in anticipo</span>
            <span v-if="item.stato === 'ND'">
              Nessuna informazione disponibile
            </span>
            <span v-if="item.orario">&nbsp;alle {{ item.orario }}</span>
          </div>
        </div>
        <div class="col-xs-4 text-center">
          <h6>{{ item.ora_inizio }} - {{ item.ora_fine }}</h6>
        </div>
      </div>
    </div>
    <div v-if="getDataSetPresenze.length" class="col-xs-12 grafico">
      <chart-card
        :chart-data="graficoPresenze.data"
        :chart-options="graficoPresenze.options"
        chart-type="HorizontalBar"
        :height="120"
      >
        <h6 slot="header">Dettaglio presenze</h6>
      </chart-card>
    </div>
  </div>
</template>

<script>
import Badge from '@/components/Mastercom/UIComponents/Badge.vue'
import ChartCard from '@/components/Mastercom/UIComponents/Charts/ChartCard.vue'
import dayjs from 'dayjs'

export default {
  name: 'PresenzeCorsoDetail',
  data() {
    return {
      backgroundTransparent: 'rgba(100,0,0,0)',
      backgroundPresenza: '#00CC00'
    }
  },
  components: {
    Badge,
    ChartCard
  },
  props: {
    detail: {
      type: Object,
      default: () => {}
    }
  },
  computed: {
    elencoOre() {
      let result = []
      for (let item of this.detail.elenco_ore) {
        if (item.stati.length > 0) {
          for (let stato of item.stati) {
            result.push({
              stato: stato.stato,
              corso: item.corso,
              orario: stato.orario,
              ora_inizio: item.ora_inizio,
              ora_fine: item.ora_fine,
              colore: stato.stato === 'A' ? '#FF0000' : '#00CC00'
            })
          }
        } else {
          result.push({
            stato: 'ND',
            corso: item.corso,
            orario: '',
            ora_inizio: item.ora_inizio,
            ora_fine: item.ora_fine,
            colore: '#b8b8b8'
          })
        }
      }
      return result
    },
    estremiTemporali() {
      return [
        dayjs(new Date(parseInt(this.detail.estremi_temporali.inizio) * 1000)),
        dayjs(new Date(parseInt(this.detail.estremi_temporali.fine) * 1000))
      ]
    },
    inizioCorsi() {
      return parseInt(this.detail.estremi_temporali.inizio)
    },
    fineCorsi() {
      return parseInt(this.detail.estremi_temporali.fine)
    },
    durataCorsi() {
      return this.fineCorsi - this.inizioCorsi
    },
    blocchiPresenze() {
      let result = []
      for (let item of this.detail.blocchi_presenze) {
        result.push(item)
      }
      return result
      // return this.detail.blocco_presenze
    },
    getDataSetPresenze() {
      let result = []

      // for (const [index, item] of this.blocchiPresenze.entries()) {
      //   console.log(item)
      //   console.log(index)
      // }
      for (const [index, item] of this.blocchiPresenze.entries()) {
        let ritardo = null
        if (index === 0) {
          ritardo = this.calcIntervalloPerc(
            item.inizio_ts,
            this.inizioCorsi,
            this.durataCorsi
          )
        } else {
          ritardo = this.calcIntervalloPerc(
            item.inizio_ts,
            this.blocchiPresenze[index - 1].fine_ts,
            this.durataCorsi
          )
        }
        result.push({
          data: [ritardo],
          backgroundColor: this.backgroundTransparent
        })
        result.push({
          data: [
            this.calcIntervalloPerc(
              item.fine_ts,
              item.inizio_ts,
              this.durataCorsi
            )
          ],
          backgroundColor: this.backgroundPresenza
        })
      }
      if (result.length === 0) {
        result.push({
          data: [100],
          backgroundColor: this.backgroundTransparent
        })
      }
      return result
    },
    graficoPresenze() {
      return {
        data: {
          // labels: this.elencoOre.map(x => x.corso),
          labels: [''],
          // datasets: [
          //   {
          //     data: this.blocchiPresenze.map(x => x.inizio_left),
          //     backgroundColor: 'rgba(100,0,0,0)',
          //     hoverBackgroundColor: 'rgba(100,100,100,0)'
          //   },
          //   {
          //     data: this.blocchiPresenze.map(x => x.durata_perc),
          //     backgroundColor: 'green'
          //   }
          // ]
          datasets: this.getDataSetPresenze
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          hover: {
            animationDuration: 10
          },
          scales: {
            xAxes: [
              {
                ticks: {
                  min: 0,
                  max: 100,
                  stepSize: 25,
                  beginAtZero: true,
                  fontFamily: "'Ubuntu', sans-serif",
                  fontSize: 11,
                  fontColor: '#333',
                  callback: (value, index, values) => {
                    if (index === 0) {
                      return this.estremiTemporali[0].format('HH:mm')
                    }
                    if (index === values.length - 1) {
                      return this.estremiTemporali[1].format('HH:mm')
                    }
                    return this.estremiTemporali[0]
                      .add(
                        parseInt(this.durataCorsi / (values.length - 1)) *
                          index,
                        'seconds'
                      )
                      .format('HH.mm')
                  }
                },
                scaleLabel: {
                  display: false
                },
                gridLines: {},
                stacked: true
              }
            ],
            yAxes: [
              {
                gridLines: {
                  display: false,
                  color: '#fff',
                  zeroLineColor: '#fff',
                  zeroLineWidth: 0
                },
                stacked: true
              }
            ]
          },
          legend: {
            display: false
          }
        }
      }
    }
  },
  methods: {
    calcIntervalloPerc(a, b, durata) {
      if (durata === 0) {
        return null
      }
      return ((a - b) * 100) / durata
    }
  }
}
</script>

<style lang="scss" scoped>
.elenco-ore {
  .ore {
    h6 {
      padding: 5px 0;
      margin: 0;
    }
    .dettaglio-assenza {
      font-size: 10pt;
      color: #606060;
    }
    margin-bottom: 4px;
    padding-bottom: 4px;
    padding-top: 4px;
    border-bottom: 1px solid #f8f8f8;
  }
}
.grafico {
  padding: 10px 0 0 0;
  /deep/ .card-header {
    padding-left: 0;
  }
}
</style>
