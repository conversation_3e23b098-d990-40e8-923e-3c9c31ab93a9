<template>
  <div class="col-xs-12 filter-bar">
    <div v-if="filtraMaterie" class="col-xs-5 filter">
      <h6>{{ $t('main.filter_materie') }}</h6>
      <materie-filter class="filter-box"></materie-filter>
    </div>
    <div v-if="filtraDate" class="col-xs-7 filter">
      <h6>{{ $t('main.filter_periodo') }}</h6>
      <date-filter class="filter-box"></date-filter>
    </div>
    <div v-if="filtraTags && tags.length" class="col-xs-5 filter">
      <h6>{{ $t('main.filter_tag') }}</h6>
      <tag-filter class="filter-box" :tags="tags"></tag-filter>
    </div>
  </div>
</template>

<script>
import MaterieFilter from '@/components/Mastercom/UIComponents/MaterieFilter.vue'
import DateFilter from '@/components/Mastercom/UIComponents/DateFilter.vue'
import TagFilter from '@/components/Mastercom/UIComponents/TagFilter'

export default {
  components: {
    Mat<PERSON>Filter,
    DateFilter,
    TagFilter
  },
  props: {
    filtraMaterie: {
      type: Boolean,
      default: true
    },
    filtraDate: {
      type: Boolean,
      default: true
    },
    filtraTags: {
      type: Boolean,
      default: false
    },
    tags: {
      type: Array,
      default: () => []
    },
    filterSelectedTag: {
      type: String,
      default: ''
    },
    dateRange: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    tagUpdate() {
      alert('fava')
    }
  }
}
</script>

<style lang="scss" scoped>
.filter-bar {
  margin-top: -12px;
  padding-bottom: 17px;
  padding-left: 0px;
  .filter {
    padding-left: 0px;
  }
  h6 {
    float: left;
    margin-right: 20px;
    padding-top: 3px;
  }
  .filter-box {
    float: left;
    margin-right: 50px;
  }
}
</style>
