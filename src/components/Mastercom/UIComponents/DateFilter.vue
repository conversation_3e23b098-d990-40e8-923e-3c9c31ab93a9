<template>
  <div class="date-filter">
    <date-picker
      v-model="FilterDateRange"
      type="daterange"
      range-separator="a"
      start-placeholder="Data inizio"
      end-placeholder="Data fine"
      :picker-options="PickerOptions"
      format="dd MMMM yyyy"
      @change="setDateFilter()"
    ></date-picker>
  </div>
</template>

<script>
import { DatePicker } from 'element-ui'

import Common from '@/components/Mastercom/Mixins/Common.js'

import { mapActions } from 'vuex'

export default {
  name: 'DateFilter',
  mixins: [Common],
  components: {
    DatePicker
  },
  data() {
    return {
      FilterDateRange: []
    }
  },
  computed: {
    PickerOptions() {
      // Imposta scelte rapide in base a periodi scolastici
      let me = this
      let shortcuts = this.periodiScolastici.map(p => {
        return {
          text: `${p.nome}`,
          onClick(picker) {
            const start = me.$dayjs(p.data_inizio)
            const end = me.$dayjs(p.data_fine)
            picker.$emit('pick', [start, end])
          }
        }
      })
      shortcuts.push({
        text: 'ultimi 90 giorni',
        onClick(picker) {
          const start = me.$dayjs().subtract(90, 'days')
          const end = me.$dayjs()
          picker.$emit('pick', [start, end])
        }
      })
      return {
        shortcuts: shortcuts,
        firstDayOfWeek: 1
      }
    }
  },
  methods: {
    setDateFilter() {
      let filter = []
      if (this.FilterDateRange) {
        filter = this.FilterDateRange.map((x, index) => {
          let timePart = '00:00:00'
          if (index === 1) {
            timePart = '23:59:59'
          }
          return this.$dayjs(x).format(`YYYY-MM-DD ${timePart}`)
        })
      }
      this.dateFilter(filter)
    },
    ...mapActions({
      dateFilter: 'main/dateFilter'
    })
  }
}
</script>

<style lang="scss">
.el-picker-panel__shortcut {
  font-size: 11px;
}
</style>
