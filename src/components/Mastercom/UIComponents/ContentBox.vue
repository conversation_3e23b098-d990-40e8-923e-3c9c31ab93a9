<template>
  <div>
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        {{ title }}
        <div v-if="lastVisit">{{ lastVisit }}x</div>
      </div>
      <slot>
        {{ content }}
      </slot>
    </el-card>
  </div>
</template>

<script>
import Vue from 'vue'
import { Card } from 'element-ui'

Vue.use(Card)
export default {
  name: 'ContentBox',
  props: {
    title: {
      type: String,
      default: ''
    },
    lastVisit: {
      type: String,
      default: null
    }
  }
}
</script>

<style></style>
