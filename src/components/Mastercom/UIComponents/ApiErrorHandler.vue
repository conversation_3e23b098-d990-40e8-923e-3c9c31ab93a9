<template>
  <div></div>
</template>

<script>
import { MessageBox } from 'element-ui'
import Common from '@/components/Mastercom/Mixins/Common'
import { mapState } from 'vuex'
export default {
  mixins: [Common],
  data() {
    return {
      msgBoxProperties: {
        type: 'warning',
        customClass: 'mastercom-msg-box'
      }
    }
  },
  computed: {
    ...mapState({
      error: state => state.apiErrorHandler.error
    })
  },
  watch: {
    error: function() {
      if (this.error === null) {
        return null
      }
      this.hideLoader()
      // console.log('ERROR STATUS', this.error.status)
      switch (this.error.status) {
        case 901:
          MessageBox.alert(this.error.message, {
            ...this.msgBoxProperties
          }).then(() => {
            this.$router.push('Home')
          })
          break
        case 401:
          MessageBox.alert(this.$i18n.t('api-error.401'), {
            ...this.msgBoxProperties
          }).then(() => {
            this.doLogout()
          })
          break
        case 403:
          MessageBox.alert(this.$i18n.t('api-error.403'), {
            ...this.msgBoxProperties
          }).then(() => {
            this.doLogout()
          })
          break
        case 404:
          MessageBox.alert(this.$i18n.t('api-error.404'), {
            ...this.msgBoxProperties
          })
          break
        case 408:
          MessageBox.alert(this.$i18n.t('api-error.408'), {
            ...this.msgBoxProperties,
            showCancelButton: true,
            showConfirmButton: true,
            cancelButtonText: 'Annulla',
            confirmButtonText: 'Ricarica pagina',
            beforeClose: (action, instance, done) => {
              if (action == 'confirm') {
                this.$router.go()
              } else {
                done()
              }
            }
          })
          break
        case 409:
          MessageBox.alert(this.error.data.error, {
            ...this.msgBoxProperties
          })
          break
        case 500:
          MessageBox.alert(this.$i18n.t('api-error.500'), {
            ...this.msgBoxProperties
          })
          break
        default:
          MessageBox.alert(this.$i18n.t('api-error.generic'), {
            ...this.msgBoxProperties
          }).then(() => {
            this.doLogout()
          })
      }
    }
  }
}
</script>

<style></style>
