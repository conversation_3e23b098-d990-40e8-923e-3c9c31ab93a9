<template>
  <footer class="footer">
    <div class="container-fluid">
      <nav class="pull-left" v-if="false">
        <ul>
          <li>
            <a href="http://www.creative-tim.com">Creative Tim</a>
          </li>
          <li>
            <a href="http://blog.creative-tim.com">Blog</a>
          </li>
          <li>
            <a href="http://www.creative-tim.com/license">Licenses</a>
          </li>
        </ul>
      </nav>
      <div class="copyright pull-right">
        Mastercom portale famiglie ver. {{ appVersion }}
      </div>
    </div>
  </footer>
</template>
<script>
import { mapGetters } from 'vuex'
export default {
  computed: {
    ...mapGetters({
      appVersion: 'app/appVersion'
    })
  }
}
</script>
<style></style>
