<template>
  <div>
    <div class="col-xs-12 col-sm-3">
      <label class="btn btn-fill">
        <i class="ti-clip" />
        Aggiungi allegati
        <input
          type="file"
          id="file"
          ref="file"
          @change="doHandleUpload()"
          multiple
        />
      </label>
    </div>
    <div class="col-xs-12 col-sm-9">
      <upload-list-item
        :loading="item.loading"
        :file-name="item.fileName"
        v-for="(item, index) in fileList"
        :key="index"
        @removing="doRemoveAttachment(index)"
      ></upload-list-item>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import { Api } from '@/data/api'
import Common from '@/components/Mastercom/Mixins/Common.js'
import { mapActions } from 'vuex'
import uploadListItem from '@/components/Mastercom/UIComponents/uploadListItem'

export default {
  name: 'AddAttachment',
  mixins: [Common],
  components: {
    uploadListItem
  },
  // computed: {
  //   headers() {
  //     return {
  //       Authorization: this.jwtToken
  //     }
  //   },
  //   urlPostAttachment() {
  //     return `https://mp-dev.registroelettronico.com${apiUrl(
  //       this.studenteCorrente,
  //       this.annoCorrente
  //     )}/messaggi/add_attachment`
  //   }
  // },
  data() {
    return {
      fileList: [],
      uploadPercentage: 0
    }
  },
  methods: {
    handleUploadError() {
      // window.alert('error')
    },
    handleBeforeUpload() {
      this.uploadPercentage = 0
      // this.$emit('upload-start')
    },
    handleOnRemove(data) {
      this.removeAttachment(data)
    },
    doRemoveAttachment(index) {
      let file = this.fileList[index]
      this.removeAttachment(file.data)
      this.fileList.splice(index, 1)
      this.$emit('upload-removed')
    },
    doHandleUpload() {
      let files = this.$refs.file.files
      let uploadFiles = []
      let fileListLastIndex = this.fileList.length
      for (let i = 0; i < files.length; i++) {
        let file = files.item(i)
        uploadFiles.push(file)
        this.fileList.push({ loading: true, fileName: file.name })
      }

      for (let file of uploadFiles) {
        this.$emit('upload-start')
        let api = new Api(this.authObject)
        api.setMultiPart()
        api.setFormData('file', file)
        let config = {
          ...api.getConfig(),
          onUploadProgress: function(progressEvent) {
            this.uploadPercentage = parseInt(
              Math.round((progressEvent.loaded / progressEvent.total) * 100)
            )
          }.bind(this)
        }
        axios
          .post(
            api.getApiUrl('messaggi/add_attachment'),
            api.getFormValues(),
            config
          )
          .then(result => {
            this.$set(this.fileList, fileListLastIndex, {
              loading: false,
              fileName: file.name,
              data: result.data
            })
            fileListLastIndex++
            return this.setAttachmentId(result)
          })
          .catch(error => {
            this.doSetError(error)
          })
          .finally(() => {
            this.$emit('upload-end')
          })
      }
    },
    setUploadProgressCallback() {
      return progressEvent => {
        return parseInt(
          Math.round(progressEvent.loaded / progressEvent.total) * 100
        )
      }
    },
    ...mapActions({
      postAttachment: 'comunicazioni/postAttachment',
      setUploadProgressBinding: 'comunicazioni/setUploadProgressBinding',
      setAttachmentId: 'comunicazioni/setAttachmentId',
      removeAttachment: 'comunicazioni/removeAttachment'
    })
  }
}
</script>

<style lang="scss" scoped>
/deep/ .el-upload__input {
  display: none;
}
#file {
  display: none;
}
</style>
