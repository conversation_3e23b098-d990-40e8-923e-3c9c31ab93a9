<template>
  <li class="row">
    <div class="col-xs-12 text-right" v-if="hasNovita()">
      <slot name="novita"></slot>
    </div>
    <div class="voto-simbolo col-xs-2">
      <slot name="voto"></slot>
      <slot name="desc-voto"></slot>
    </div>
    <div class="voto-panel col-xs-10">
      <div class="voto-header" v-if="hasHeader">
        <slot name="header"></slot>
      </div>
      <p class="text-primary" v-if="voto.confirmed">
        {{ voto.dettaglio }}
      </p>
      <div class="confirm-container" v-if="onConfirmAction">
        <button
          class="btn btn-sm btn-primary btn-fill"
          v-if="voto.confirmed === false"
          @click.prevent="onConfirmAction(voto)"
        >
          {{ confirmActionText }}
        </button>
        <div class="confirmed" v-else>{{ voto.confirm_text }}</div>
      </div>
      <div
        class="note-competenze-docente"
        v-if="isCompetenza(voto) && (voto.note_competenza || voto.dettaglio)"
      >
        <p v-if="voto.dettaglio">{{ voto.dettaglio }}</p>
        <span v-if="voto.note_competenza">
          <h6>{{ $t('voti.label_note_competenze') }}</h6>
          {{ voto.note_competenza }}
        </span>
      </div>
      <div class="dimensioni-competenze" v-if="hasDimensioni(voto)">
        <h6>{{ $t('voti.label_dimensioni') }}</h6>
        <ul>
          <li v-for="(d, dindex) in voto.dimensioni" :key="`d_${dindex}`">
            {{ d }}
          </li>
        </ul>
      </div>
      <h6>
        <span v-if="isCompetenza(voto) == false">c{{ voto.sottotitolo }}</span>
        <slot name="footer" v-if="hasFooter"></slot>
      </h6>
    </div>
  </li>
</template>
<script>
export default {
  props: {
    voto: {
      type: Object,
      default: () => {
        return {
          descrizione: '',
          simbolo: '',
          colore: ''
        }
      }
    },
    index: {
      type: Number,
      default: 0
    },
    onConfirmAction: {
      type: Function,
      default: null
    },
    confirmActionText: {
      type: String,
      default: ''
    }
  },
  methods: {
    isCompetenza(v) {
      return (
        ['SCRITTO', 'ORALE', 'PRATICO'].includes(
          v.sottotitolo.toLowerCase()
        ) === false
      )
    },
    hasDimensioni(v) {
      try {
        return this.isCompetenza(v) && v.dimensioni.length
      } catch (e) {
        return false
      }
    },
    hasHeader() {
      return !!this.$slots.header
    },
    hasFooter() {
      return !!this.$slots.footer
    },
    hasNovita() {
      return !!this.$slots.novita
    }
  }
}
</script>
<style scoped lang="scss">
p {
  margin: 10px 0;
}
.voto-simbolo {
  padding-left: 0px;
  text-align: center;
}

.text-detail {
  font-size: 0.8em;
}
.note-competenze-docente {
  background-color: #f3f3f3;
  padding: 0.4em;
  border-radius: 4px;
}
.dimensioni-competenze {
  li {
    list-style-type: circle;
  }
}
.confirm-container {
  margin: 10px 0;
  .confirmed {
    color: #54b07d;
    font-size: 0.8em;
  }
}
</style>
