<template>
  <div>
    <el-select v-model="selectedValues" clearable placeholder="materie">
      <el-option
        v-for="item in materie"
        :key="item.id"
        class="select-primary"
        :value="item.id"
        :label="item.label"
      ></el-option>
    </el-select>
  </div>
</template>

<script>
import { Select, Option } from 'element-ui'
import { mapState, mapGetters, mapActions } from 'vuex'
import 'element-theme-chalk/lib/input.css'
import 'element-theme-chalk/lib/select.css'
export default {
  name: 'MaterieFilter',
  components: {
    'el-select': Select,
    'el-option': Option
  },
  watch: {
    selectedValues: function(newVal) {
      if (typeof newVal !== 'number') {
        this.setMaterieFilter(newVal)
      } else {
        this.setMaterieFilter([newVal])
      }
    }
  },
  computed: {
    ...mapState({
      materieFilter: state => state.materie.materieFilter
    }),
    ...mapGetters({
      materie: 'materie/materieStudenteCorrente'
    })
  },
  data() {
    return {
      selectedValues: []
    }
  },
  methods: {
    ...mapActions({
      setMaterieFilter: 'materie/filter'
    })
  }
}
</script>

<style lang="css" scoped>
.el-select .el-input:hover .el-input__icon {
  color: black;
}
</style>
