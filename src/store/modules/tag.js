const getDefaultState = () => {
  return {
    _selectedTag: ''
  }
}
const state = getDefaultState()

const getters = {
  selectedTag(state) {
    return state._selectedTag
  }
}

const mutations = {
  setTag(state, data) {
    state._selectedTag = data
  }
}

const actions = {
  setTag({ commit }, data) {
    commit('setTag', data)
  },
  refreshReset() {
    return getDefaultState()
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
