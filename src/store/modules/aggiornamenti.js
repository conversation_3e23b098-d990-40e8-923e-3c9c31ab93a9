import { Api } from '@/data/api'

const state = {
  // argomenti
  all: []
}

const actions = {
  loadAggiornamenti(context) {
    return new Promise((resolve, reject) => {
      let api = new Api(context.rootGetters['main/authObject'])
      api
        .fetchAll('aggiornamenti_nextapi')
        .then(value => {
          context.commit('set', { result: value })
          resolve()
        })
        .catch(error => {
          reject(error.response)
        })
    })
  }
}

const mutations = {
  set(state, payload) {
    state.all = payload.result.data
  }
}

const getters = {
  aggiornamenti(state) {
    return state.all
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
