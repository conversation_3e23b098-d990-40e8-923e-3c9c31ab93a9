import { Api } from '@/data/api'
import { getMateriaDetails } from '@/utils'
const getDefaultState = () => {
  return {
    _all: []
  }
}
const state = getDefaultState()

const actions = {
  loadAnnotazioni(context, data) {
    return new Promise((resolve, reject) => {
      let api = new Api(data)
      api
        .fetchAll('annotazioni_plain')
        .then(value => {
          context.commit('set', { result: value })
          resolve(true)
        })
        .catch(error => {
          reject(error.response)
        })
    })
  },
  reset({ commit }) {
    commit('resetState')
  },
  refreshReset() {
    return getDefaultState()
  }
}

const mutations = {
  set(state, payload) {
    state._all = payload.result.data
  },
  resetState(state) {
    for (const [key, value] of Object.entries(getDefaultState())) {
      state[key] = value
    }
    return true
  }
}

const getters = {
  all(state) {
    return state._all.map(v => {
      v.descrizione =
        v.dettaglio.trim() !== '' ? v.dettaglio.trim() : v.descrizione_simbolo
      v = { ...v, ...getMateriaDetails(v.id_materia) }
      return v
    })
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
