import { Api } from '@/data/api'
import { getMateriaDetails } from '@/utils'

const getDefaultState = () => {
  return {
    _all: [],
    showVotoScritto: true,
    showVotoOrale: true,
    showVotoPratico: true,
    idPresaVisione: null
  }
}
const state = getDefaultState()

const actions = {
  loadVoti(context, data) {
    return new Promise((resolve, reject) => {
      let api = new Api(data)
      api
        .fetchAll('voti_plain')
        .then(value => {
          context.commit('set', { result: value })
          resolve(true)
        })
        .catch(error => {
          return reject(error.response)
        })
    })
  },
  setPresaVisione({ commit }, data) {
    commit('setPresaVisione', data)
  },
  async inviaPresaVisione({ state }, data) {
    try {
      let api = new Api(data)
      let result = await api.put(`presa_visione_voti/${state.idPresaVisione}`)
      return result
    } catch (error) {
      throw error
    }
  },
  reset({ commit }) {
    commit('resetState')
  },
  showPratico(context) {
    return context.commit('setPratico', true)
  },
  showOrale(context) {
    return context.commit('setOrale', true)
  },
  showScritto(context) {
    return context.commit('setScritto', true)
  },
  hidePratico(context) {
    return context.commit('setPratico', false)
  },
  hideOrale(context) {
    return context.commit('setOrale', false)
  },
  hideScritto(context) {
    return context.commit('setScritto', false)
  },
  refreshReset() {
    return getDefaultState()
  }
}

const mutations = {
  set(state, payload) {
    state._all = payload.result.data
  },
  setPratico(state, payload) {
    state.showVotoPratico = payload
  },
  setScritto(state, payload) {
    state.showVotoScritto = payload
  },
  setOrale(state, payload) {
    state.showVotoOrale = payload
  },
  resetState(state) {
    state = getDefaultState()
    return state
  },
  setPresaVisione(state, payload) {
    state.idPresaVisione = payload
  }
}

const getters = {
  all(state) {
    return state._all
      .filter(v => {
        return (
          ['SCRITTO', 'ORALE', 'PRATICO'].includes(
            v.sottotitolo.toUpperCase()
          ) || v.is_voto === true
        )
      })
      .sort((a, b) => {
        if (a.data < b.data) {
          return 1
        }
        if (a.data > b.data) {
          return -1
        }
        return 0
      })
      .map((x, i) => {
        x.id = i
        x = { ...x, ...getMateriaDetails(x.id_materia) }
        x.confirmed =
          (x.presa_visione === true && x.data_presa_visione > 0) ||
          x.presa_visione === false
        if (x.confirmed == true) {
          x.confirm_text = x.data_presa_visione_tradotta
          let [a, b] = x.data_presa_visione_tradotta.split(' ')
          x.confirm_date = a
          x.confirm_time = b
        }
        return x
      })
  },
  competenze(state) {
    return state._all
      .filter(v => {
        return v.is_competenza === true
      })
      .sort((a, b) => {
        if (a.data < b.data) {
          return 1
        }
        if (a.data > b.data) {
          return -1
        }
        return 0
      })
      .map((x, i) => {
        x.id = i
        x = { ...x, ...getMateriaDetails(x.id_materia) }
        return x
      })
  },
  votoPesato(state) {
    return state._all.filter(v => v.voto_pesato).length > 0
  },
  mediaPesataCalcolabile(state) {
    return (
      state._all.filter(v => v.voto_pesato === true && v.valore_peso > 0)
        .length === state._all.length
    )
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
