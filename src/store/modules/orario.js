import { Api } from '@/data/api'
import { getMateriaDetails } from '@/utils'

const getDefaultState = () => {
  return {
    _all: [],
    data_inizio: null,
    data_fine: null
  }
}
const state = getDefaultState()

const getters = {
  date_params(state) {
    if (state.data_inizio && state.data_fine) {
      return {
        data_inizio: state.data_inizio,
        data_fine: state.data_fine
      }
    }
    return null
  },
  all(state) {
    return state._all.map((x, i) => {
      x.id = i
      x = { ...x, ...getMateriaDetails(x.id_materia) }
      if (x.materia === '') {
        x.materia = x.titolo
        x.indice_colore = 1
        x.insegnante = x.sottotitolo
      }
      return x
    })
  }
}

const actions = {
  loadOrario({ commit, getters }, data) {
    // console.log('orario loading')
    return new Promise((resolve, reject) => {
      let api = new Api(data)
      if (getters.date_params) {
        api.addParams(getters.date_params)
      }
      api
        .fetchAll('orario_plain')
        .then(value => {
          commit('set', { result: value })
          resolve()
        })
        .catch(error => {
          reject(error.response)
        })
    })
  },

  setDataInizio(context, data) {
    context.commit('setDataInizio', data)
  },

  setDataFine(context, data) {
    context.commit('setDataFine', data)
  },
  refreshReset() {
    return getDefaultState()
  }
}

const mutations = {
  set(state, payload) {
    // console.log(payload.result.data)
    state._all = payload.result.data
  },
  setDataInizio(state, payload) {
    state.data_inizio = payload
  },
  setDataFine(state, payload) {
    state.data_fine = payload
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
