import { Api } from '@/data/api'

const getDefaultState = () => {
  return {
    _all: []
  }
}
const state = getDefaultState()

const actions = {
  load(context, data) {
    return new Promise((resolve, reject) => {
      let api = new Api(data)
      api.fetchAll('agenda_plain').then(
        value => {
          context.commit('set', { result: value })
          resolve(true)
        },
        error => {
          return reject(error.response)
        }
      )
    })
  },
  reset({ commit }) {
    commit('resetState')
  },
  refreshReset() {
    return getDefaultState()
  }
}

const getters = {
  all(state, getters, rootState, rootGetters) {
    let materie = rootGetters['materie/materieStudenteCorrente']
    return state._all
      .sort((a, b) => (a.date > b.date ? 1 : -1))
      .map((x, i) => {
        x.id = i
        try {
          let materia = materie.filter(k => k.id === x.id_materia)[0]
          x.materia = materia['descrizione']
          x.indice_colore = materia['indiceColore']
          x.insegnante = materia.professori.reduce((acc, cv) => {
            return acc !== '' ? `${acc}, ${cv.nome}` : cv.nome
          }, '')
        } catch (error) {
          x.materia = ''
          x.indice_colore = 0
          x.insegnante = ''
        }
        return x
      })
  }
}

const mutations = {
  set(state, payload) {
    state._all = payload.result.data
  },
  resetState(state) {
    state = getDefaultState()
    return state
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
