// import { fetchScuola } from 'src/data/scuole'
import { initLoginScreen } from '@/data/auth'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import cloneDeep from 'lodash/cloneDeep'
import sidebarLinks from '@/sidebarLinks'
import { Api } from '@/data/api'

dayjs.extend(isBetween)

const getDefaultState = () => {
  return {
    urlScuola: '',
    annoCorrente: '',
    _calendarioScolastico: [],
    scuolaCorrente: 0,
    studenteCorrente: {},
    links: [],
    _periodiScolastici: [],
    periodoCorrente: {},
    dateFilter: [],
    dataInizioAnno: null,
    dataFineAnno: null,
    nomeScuola: null,
    loginSPID: false,
    loginUsernamePassword: true,
    disclaimerLoginUsernamePassword: '',
    spid: null,
    recuperaPasswordEnabled: false,
    _richiestaColloquiIndividuali: false,
    displayOrarioPanel: true,
    _votiPesati: false,
    _mediaVoti: false,
    _presaVisioneVoti: false,
    _presaVisioneNote: false,
    _prenotazioneEntrate: false,
    _prenotazioneUscite: false,
    _versioneMessenger: null
  }
}

const state = getDefaultState()

const getters = {
  prenotazioneEntrate(state) {
    return state._prenotazioneEntrate
  },
  prenotazioneUscite(state) {
    return state._prenotazioneUscite
  },
  dateAttivita(state) {
    return state._calendarioScolastico[0].attivita
  },
  dateFestivita(state) {
    return state._calendarioScolastico[0].festivita
  },
  /**
   * Estrae gli anni scolastici disponibili per lo studente
   * @param {*} state
   */
  anniStorici(state) {
    if (!('anni' in state.studenteCorrente)) {
      return null
    }
    return state.studenteCorrente.anni.map(x => {
      x.descrizione = `${x.id.replace('_', '/')} - ${x.classe}`
      return x
    })
  },
  /**
   * Abilita il pannello con l'orario in homepage
   * @param {*} state
   */
  mostraOrario(state) {
    return (
      state.displayOrarioPanel === true &&
      state.studenteCorrente.servizi.orario === true
    )
  },
  /**
   * * Determina la validità dell'utente loggato
   * TODO: verificare condizioni
   * @param {*} state
   */
  activeUser(state, getters, rootState, rootGetters) {
    if (
      rootGetters['auth/isAuthenticated'] === true &&
      rootGetters['materie/materieStudenteCorrente']
    ) {
      return true
    }
    return false
  },
  /**
   * * Settimane scolastiche
   */
  settimaneScolastiche(state) {
    if (!state._periodiScolastici.length) {
      return null
    }
    let inizioFineScuola = [
      {
        inizio: state._periodiScolastici[0]['data_inizio'],
        fine: [...state._periodiScolastici].pop()['data_fine']
      }
    ]

    let settimane = []
    for (let v of inizioFineScuola) {
      let inizio = dayjs(v['inizio']).startOf('week')
      let fine = dayjs(v['fine']).endOf('week')
      let numeroSettimane = fine.diff(inizio, 'weeks')
      for (let cv = 0; cv <= numeroSettimane; cv++) {
        fine = inizio.add(6, 'days')
        settimane.push({
          inizio: dayjs(inizio).format('YYYY-MM-DD'),
          fine: fine.format('YYYY-MM-DD')
        })
        inizio = fine.add(1, 'days')
      }
    }
    return settimane
  },
  /**
   * * Settimane Scolastiche trascorse
   */
  settimaneScolasticheTrascorse(state, getters) {
    let settimane = getters['settimaneScolastiche']
    try {
      return settimane.filter(v => {
        return dayjs(v.inizio) <= dayjs()
      })
    } catch (error) {
      return null
    }
  },
  /**
   * * links
   */
  sidebarLinks(state) {
    // if (rootState.aggiornamenti.all) {
    //   console.log('AGGIORNAMENTI OBJECT', rootState.aggiornamenti.all)
    //   let aggiornamenti = rootState.aggiornamenti.all
    //   return state.links.map(item => {
    //     console.log('OBJECT ITEM', item)
    //     if (typeof (item.children) === 'object') {
    //       item.children = item.children.map(child => {
    //         console.log('OBJECT LINKNAME 2', child.meta.linkName)
    //         if ('linkName' in child.meta) {
    //           // child.meta.badge = aggiornamenti[child.meta.linkName]['numero']
    //         }
    //       })
    //     } else {
    //       console.log('OBJECT LINKNAME 1', aggiornamenti, item.meta.linkName, aggiornamenti[item.meta.linkName])
    //       // item.meta.badge = aggiornamenti[item.meta.linkName].numero
    //     }
    //   })
    // }
    return state.links
  },
  /**
   * AuthObject
   * oggetto base per autenticazione API
   */
  authObject(state, getters, rootState, rootGetters) {
    return {
      studente: state.studenteCorrente,
      anno: state.annoCorrente,
      token: rootGetters['auth/jwtToken']
    }
  },
  /**
   * PeriodiScolastici
   */
  periodiScolastici(state) {
    return state._periodiScolastici.map(p => {
      p.nome = `${p.numero}° ${p.periodo}`
      return p
    })
  },
  annoCorrenteDisplay(state) {
    return state.annoCorrente.replace('_', '/')
  },
  votiPesati(state) {
    return state._votiPesati === 'SI'
  },
  mediaVoti(state) {
    return state._mediaVoti === 'SI'
  },
  richiestaColloquiIndividuali(state) {
    return state._richiestaColloquiIndividuali
  }
}

const mutations = {
  setCalendarioScolastico(state, payload) {
    state._calendarioScolastico = payload
  },
  setStudenteCorrente(state, payload) {
    state.studenteCorrente = payload
    state.studenteCorrente.classe = payload.anni.filter(
      x => x.id === payload.anno_corrente
    )[0].classe
    state.studenteCorrente.indirizzo = payload.anni.filter(
      x => x.id === payload.anno_corrente
    )[0].indirizzo
    state.studenteCorrente.servizi =
      payload.anni.filter(x => x.id === payload.anno_corrente)[0].servizi || []
    state.studenteCorrente.anno_corrente_display = state.studenteCorrente.anno_corrente.replace(
      '_',
      '/'
    )
    if ('presa_visione_voti' in state.studenteCorrente.servizi) {
      state.studenteCorrente.presa_visione_voti =
        state.studenteCorrente.servizi.presa_visione_voti
    } else {
      state.studenteCorrente.presa_visione_voti = false
    }
    if ('presa_visione_note' in state.studenteCorrente.servizi) {
      state.studenteCorrente.presa_visione_note =
        state.studenteCorrente.servizi.presa_visione_note
    } else {
      state.studenteCorrente.presa_visione_note = false
    }
  },
  setAnnoCorrente(state, annoCorrente) {
    if (annoCorrente === null) {
      state.annoCorrente = state.studenteCorrente.anno_corrente
    } else {
      state.studenteCorrente.anno_corrente = annoCorrente
      state.annoCorrente = annoCorrente
    }
  },
  setServiziStudente(state, payload) {
    state.studenteCorrente.servizi = payload.result.data.servizi
  },
  setLinks(state) {
    let servizi = state.studenteCorrente.servizi

    let versione_messenger = 'VERSIONE_1'
    try {
      versione_messenger = state.studenteCorrente.versione_messenger
    } catch (error) {
      console.log('VERSIONE MESSENGER NON TROVATA', error)
    }
    console.log('INIZIO LOGIN MESSENGER V', versione_messenger)
    // console.log('SERVIZI', servizi)
    console.log('INIZIO LOGIN LINKS', sidebarLinks)
    let links = cloneDeep(sidebarLinks)
    links.forEach(function(item, index) {
      if (typeof item.children === 'object') {
        item.children.forEach(function(child, childIndex) {
          // console.log('CHILD', child)
          if ('linkName' in child.meta) {
            // console.log('linkName OK', child.meta.linkName)
            if (!servizi[child.meta.linkName]) {
              // console.log('fava')
              links[index].children[childIndex].meta.visible = false
            } else {
              // console.log('rava')
              links[index].children[childIndex].meta.visible =
                servizi[child.meta.linkName]

              let m = links[index].children[childIndex].meta
              if ('versione_messenger' in m) {
                console.log('INIZIO LOGIN M', m)
                if (m.versione_messenger !== versione_messenger) {
                  links[index].children[childIndex].meta.visible = false
                }
              }
              // console.log(
              //   'LINK: ',
              //   child.meta.linkName,
              //   servizi[child.meta.linkName]
              // )
            }
          }
        })
      }
    })
    state.links = links
  },

  setConfigScuola(state, payload) {
    console.log('PAYLOAD', payload)
    state._periodiScolastici = payload.result.data.periodi_scolastici.sort(
      (a, b) => {
        return a.numero - b.numero
      }
    )
    state._periodiScolastici = state._periodiScolastici.map(p => {
      p.data_inizio = dayjs(p.data_inizio)
        .second(0)
        .minute(0)
        .hour(0)
        .format('YYYY-MM-DDTHH:mm:ss')
      p.data_fine = dayjs(p.data_fine)
        .second(59)
        .minute(59)
        .hour(23)
        .format('YYYY-MM-DDTHH:mm:ss')
      return p
    })
    state.periodoCorrente = payload.result.data.periodo_corrente
      ? payload.result.data.periodo_corrente
      : state._periodiScolastici[0]
    state.nomeScuola = payload.result.data.scuola
    state.dataInizioAnno = dayjs(state._periodiScolastici[0].data_inizio)
    state.dataFineAnno = dayjs([...state._periodiScolastici].pop().data_fine)
    state._votiPesati = payload.result.data.voti_pesati
    state._mediaVoti = payload.result.data.media_voti
    state._richiestaColloquiIndividuali =
      payload.result.data.richiesta_colloqui_individuali
    state._presaVisioneVoti = payload.result.data.presa_visione_voti
    state._prenotazioneEntrate = payload.result.data.prenotazione_entrate_attiva
    state._prenotazioneUscite = payload.result.data.prenotazione_uscite_attiva
  },

  setDataFilter(state, payload) {
    if (!payload) {
      payload = []
    }
    state.dateFilter = payload
  },

  resetState(state) {
    Object.assign(state, getDefaultState())
  },

  setDisplayOrarioPanel(state, value) {
    state.displayOrarioPanel = value
  },

  setInitLoginScuola(state, value) {
    state.recuperaPasswordEnabled = value.recupera_password
    state.nomeScuola = value.nome_scuola
    state.urlScuola = value.url_scuola
    state.loginSPID = value.login_spid
    state.spid = value.parametri_spid
    state.loginUsernamePassword = value.login_username_password
    state.disclaimerLoginUsernamePassword =
      value.disclaimer_login_username_password
  }
}

const actions = {
  /**
   * permessi schermata login
   */
  async loginInit({ commit }, data) {
    try {
      let result = await initLoginScreen(data.mastercom)
      commit('setInitLoginScuola', result.data)
      return true
    } catch (error) {
      throw error
    }
  },
  /**
   * configurazione iniziale dati applicazione
   */
  init({ commit }, payload) {
    return new Promise((resolve, reject) => {
      commit('setStudenteCorrente', payload.studenti[0])
      commit('setAnnoCorrente', null)
      commit('setLinks')
      resolve('init studenti')
      reject('failed init studenti')
    })
  },

  /**
   * configurazione dati applicazione al cambio studente
   */
  initStudente({ commit }, studente) {
    return new Promise((resolve, reject) => {
      commit('setStudenteCorrente', studente)
      commit('setAnnoCorrente', null)
      commit('setLinks')
      resolve()
      reject()
    })
  },

  setAnnoCorrente({ commit }, anno) {
    commit('setAnnoCorrente', anno)
  },
  /**
   * configurazione dati scuola dello studente
   */
  initScuolaStudente({ commit, state }, data) {
    return new Promise((resolve, reject) => {
      let api = new Api(data)
      api
        .fetchAll('studente/info')
        .then(value => {
          commit('setConfigScuola', { result: value })
          document.title = state.nomeScuola
          commit('setServiziStudente', { result: value })
          commit('setLinks')
          resolve(value)
        })
        .catch(error => {
          let msg = 'errore generico configurazione scuola'
          if (error.response.status == 401) {
            msg =
              'Le credenziali di accesso sono corrette, ma non è stato possibile caricare con successo i dati dello studente.'
          }
          if (error.response.status == 500) {
            msg =
              'Le credenziali di accesso sono corrette, ma non è stato possibile caricare con successo i dati dello studente.'
          }
          reject(msg)
        })
    })
  },

  /**
   * Impostazione filtro per periodo/data
   */
  dateFilter({ commit }, data) {
    return new Promise((resolve, reject) => {
      commit('setDataFilter', data)
      resolve()
      reject()
    })
  },

  /**
   * Cancellazione filtro periodo/data
   */
  clearDateFilter({ commit }) {
    commit('setDataFilter', null)
  },

  /**
   * getFotoStudenti()
   * scarica immagini studenti
   * @param {*} param0
   */

  async getFotoStudenti(data) {
    let api = new Api(data)
    try {
      api.setRawUrl(state.url_allegato)
      let result = await api.fetchRaw('blob')
      return result
    } catch (error) {
      throw error
    }
  },
  /**
   * setServiziStudente
   * modifica i servizi visibili sullo studente
   */
  refreshStudente({ commit, state }, data) {
    // console.log('STATE', state.studenteCorrente.id)
    let datiStudente = data.studenti.filter(
      x => x.id === state.studenteCorrente.id
    )[0]
    if (state.annoCorrente) {
      datiStudente.anno_corrente = state.annoCorrente
    }
    commit('setStudenteCorrente', datiStudente)
    commit('setLinks')
  },
  displayOrarioPanel({ commit }, value) {
    commit('setDisplayOrarioPanel', value)
  },
  /**
   * carica il calendario con i giorni di festività della scuola
   * @param {*} param0 store dependency injection
   * @param {*} data json parametri funzione
   */
  async loadCalendario({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('calendario_scolastico')
      commit('setCalendarioScolastico', result.data)
      return true
    } catch (error) {
      throw error
    }
  },
  /**
   * reset dati login
   */
  reset({ commit }) {
    commit('resetState')
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
