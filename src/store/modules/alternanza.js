import { Api } from '@/data/api'
const getDefaultState = () => {
  return {
    _alternanza: [],
    progettoIscrizione: null,
    progettoCancellazione: null,
    url_allegato: null,
    data_inizio: null,
    data_fine: null
  }
}
const state = getDefaultState()

const getters = {
  alternanza(state) {
    return state._alternanza
      .map(x => {
        x.show_iscrizione =
          x.stato.toUpperCase() === 'PROPOSTO' && x.prenotabile ? true : false
        x.show_cancellazione =
          x.stato.toUpperCase() === 'PROPOSTO' && x.prenotabile === false
            ? true
            : false
        return x
      })
      .sort((a, b) => {
        if (
          a.stato.toUpperCase() === 'PROPOSTO' &&
          b.stato.toUpperCase() !== 'PROPOSTO'
        )
          return -1
        if ('data_inizio' in a && 'data_inizio' in b) {
          if (a.data_inizio && b.data_inizio) {
            return a.data_inizio > b.data_inizio ? -1 : 1
          }
        }
        return a.titolo < b.titolo ? -1 : 1
      })
  }
}

const mutations = {
  setAlternanza(state, payload) {
    state._alternanza = payload
  },
  setProgettoIscrizione(state, idProgetto) {
    state.progettoIscrizione = idProgetto
  },
  setProgettoCancellazione(state, idProgetto) {
    state.progettoCancellazione = idProgetto
  }
}

const actions = {
  async loadAlternanza({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('alternanzaweb')
      commit('setAlternanza', result.data)
      return true
    } catch (error) {
      console.log('ERRORE ALTERNANZA', error)
      throw error
    }
  },
  async loadAllegato({ state }, data) {
    let api = new Api(data)
    try {
      api.setRawUrl(state.url_allegato)
      let result = await api.fetchRaw('blob')
      return result
    } catch (error) {
      throw error
    }
  },
  setProgettoIscrizione({ commit }, idProgetto) {
    commit('setProgettoIscrizione', idProgetto)
  },
  setProgettoCancellazione({ commit }, idProgetto) {
    commit('setProgettoCancellazione', idProgetto)
  },
  async iscrizioneAlternanza({ state }, data) {
    let api = new Api(data)
    try {
      let result = await api.put(`alternanzaweb/${state.progettoIscrizione}`)
      return result
    } catch (error) {
      throw error
    }
  },
  async cancellazioneAlternanza({ state }, data) {
    console.log('cancellazioneAlternanza', data)
    let api = new Api(data)
    try {
      let result = await api.delete(
        `alternanzaweb/${state.progettoCancellazione}`
      )
      return result
    } catch (error) {
      throw error
    }
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
