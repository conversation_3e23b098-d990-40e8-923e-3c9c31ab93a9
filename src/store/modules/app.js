const getDefaultState = () => {
  return {
    swUpdateAvailable: false,
    hasFocus: true,
    packageVersion: JSON.parse(
      decodeURIComponent(process.env.PACKAGE_JSON || '%7Bversion%3A0%7D')
    ).version
  }
}

const state = getDefaultState()

const getters = {
  appVersion: state => {
    return state.packageVersion
  },
  isAppFocused: state => {
    return state.hasFocus === true
  },
  isSWUpdateAvailable: state => {
    return state.swUpdateAvailable === true
  }
}

const actions = {
  setAppHasFocus: ({ commit, state }, data) => {
    if (state.hasFocus !== data) {
      commit('setAppHasFocus', typeof data == 'boolean' ? data === true : true)
    }
  },
  setSWUpdateAvailable: ({ commit }) => {
    commit('setSWUpdateAvailable')
  },
  setAppVersion: ({ commit }, data) => {
    commit('setAppVersion', data)
  },
  refreshReset() {
    return getDefaultState()
  }
}

const mutations = {
  setAppHasFocus: (state, data) => {
    state.hasFocus = data
  },
  setSWUpdateAvailable: state => {
    state.swUpdateAvailable = true
  },
  setAppVersion: (state, data) => {
    state.packageVersion = data
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
