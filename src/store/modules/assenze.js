import { Api } from '@/data/api'

const getDefaultState = () => {
  return {
    _all: [],
    showOnlyNonGiustificate: false,
    showOnlyPresenzeCorso: false,
    assenzaDaGiustificare: null,
    _motivazioniAssenze: null,
    _motivazioniAssenzeDAD: null,
    _opzioniPrenotazione: null,
    _idPrenotazioneAnnullata: null,
    motivazioneText: '',
    prenotazione: {
      data: '',
      orario: '',
      tipo: '',
      motivazione: ''
    },
    profileUpdate: true
  }
}
const state = getDefaultState()

const actions = {
  loadAssenze({ commit, state }, data) {
    return new Promise((resolve, reject) => {
      let api = new Api(data)
      if (state.profileUpdate === false) {
        api.addParams({ profile_update: 0 })
      }
      api
        .fetchAll('assenze_plain')
        .then(value => {
          commit('set', { result: value })
          resolve(true)
        })
        .catch(error => {
          reject(new Error(error.response))
        })
    })
  },
  async loadMotivazioniAssenze({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('giustificazioni')
      commit('setMotivazioniAssenze', result.data)
      return true
    } catch (error) {
      throw error
    }
  },
  setDatiPrenotazione({ commit }, data) {
    commit('setDatiPrenotazione', data)
  },
  setProfileUpdate({ commit }, data) {
    commit('setProfileUpdate', data)
  },
  setOnlyNonGiustificate({ commit }, data) {
    commit('setOnlyNonGiustificate', data)
  },
  setOnlyPresenzeCorso({ commit }, data) {
    commit('setOnlyPresenzeCorso', data)
  },
  reset({ commit }) {
    commit('resetState')
  },
  setAssenzaDaGiustificare({ commit }, data) {
    commit('setAssenzaDaGiustificare', data)
  },
  setMotivazioneText({ commit }, data) {
    commit('setMotivazioneText', data)
  },
  async postGiustificazione({ commit, state }, data) {
    let api = new Api(data)
    try {
      let tipoAssenza = state._all.filter(
        x => x.id === state.assenzaDaGiustificare
      )[0]['tipo_assenza']
      api.setPostData('tipo_assenza', tipoAssenza)
      api.setPostData('motivazione', state.motivazioneText)
      let result = await api.put(
        `giustificazioni/${state.assenzaDaGiustificare}`
      )
      commit('setAssenzaGiustificata', state.assenzaDaGiustificare)
      return result
    } catch (error) {
      throw error
    }
  },
  async loadOpzioniPrenotazione({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('prenotazioni_entrate_uscite')
      commit('setOpzioniPrenotazione', result.data)
      return result
    } catch (error) {
      throw error
    }
  },
  async inviaPrenotazione({ commit, state }, data) {
    let api = new Api(data)
    try {
      let json_payload = {}
      json_payload['data'] = state.prenotazione.data
      json_payload['orario'] = state.prenotazione.orario
      json_payload['tipo_assenza'] = state.prenotazione.tipo
      json_payload['motivazione'] = state.prenotazione.motivazione
      api.setJsonData(json_payload)
      let result = await api.postJson('prenotazioni_entrate_uscite')
      commit('resetDatiPrenotazione')
      return result
    } catch (error) {
      throw error
    }
  },
  setIdPrenotazioneAnnullata({ commit }, data) {
    commit('setIdPrenotazioneAnnullata', data)
  },
  async annullaPrenotazione({ state }, data) {
    let api = new Api(data)
    try {
      let result = await api.patch(
        `prenotazioni_entrate_uscite/${state._idPrenotazioneAnnullata}`
      )
      return result
    } catch (error) {
      throw error
    }
  },
  refreshReset() {
    return getDefaultState()
  }
}

const mutations = {
  set(state, payload) {
    state._all = payload.result.data
  },
  setProfileUpdate(state, payload) {
    state.profileUpdate = payload
  },
  setOnlyNonGiustificate(state, payload) {
    state.showOnlyNonGiustificate = payload
  },
  setOnlyPresenzeCorso(state, payload) {
    state.showOnlyPresenzeCorso = payload
  },
  setAssenzaDaGiustificare(state, payload) {
    state.assenzaDaGiustificare = payload
  },
  setMotivazioneText(state, payload) {
    state.motivazioneText = payload
  },
  setAssenzaGiustificata(state, payload) {
    state._all = state._all.map(x => {
      if (x.id == payload) {
        x.giustificabile = false
        x.giustificata = 'SI'
      }
      return x
    })
  },
  setIdPrenotazioneAnnullata(state, payload) {
    state._idPrenotazioneAnnullata = payload
  },
  setMotivazioniAssenze(state, payload) {
    state._motivazioniAssenze = payload[0]['normale']
    state._motivazioniAssenzeDAD = payload[0]['dad']
  },
  setOpzioniPrenotazione(state, payload) {
    state._opzioniPrenotazione = payload
  },
  setDatiPrenotazione(state, payload) {
    state.prenotazione = payload
  },
  resetState(state) {
    for (const [key, value] of Object.entries(getDefaultState())) {
      state[key] = value
    }
    return true
  },
  resetDatiPrenotazione(state) {
    state.prenotazione = {
      data: '',
      orario: '',
      tipo: '',
      motivazione: ''
    }
  }
}

const getters = {
  opzioniEntrata: state => {
    if (state._opzioniPrenotazione === null) {
      return []
    }
    return state._opzioniPrenotazione[0]['entrata'].map(x => {
      return { value: x.orario, label: x.descrizione }
    })
  },
  opzioniUscita: state => {
    if (state._opzioniPrenotazione === null) {
      return []
    }
    return state._opzioniPrenotazione[0]['uscita'].map(x => {
      return { value: x.orario, label: x.descrizione }
    })
  },
  daGiustificare: state => {
    let conta = 0
    for (var obj of state._all) {
      if (obj.giustificata === 'NO') {
        conta++
      }
    }
    return conta
  },
  totaleAssenze: state => {
    return state._all.length
  },
  all: state => {
    return state._all.map(x => {
      if (['C'].includes(x.simbolo) === false) {
        x.colore_simbolo = x.simbolo === 'a' ? x.colore_simbolo : '#FF0000'
        x.colore_simbolo = x.giustificata == 'SI' ? '#00CC00' : x.colore_simbolo
        x.colore_simbolo = x.giustificata == '' ? '#0000FF' : x.colore_simbolo
      }
      if (x.simbolo === 'C') {
        x.dettaglio_presenze_corso = true
      }
      if (x.simbolo === 'a') {
        x.dettaglio_assenze_dad = true
      }
      return x
    })
  },
  presenzeCorsi: state => {
    return state._all.filter(x => x.simbolo === 'C').length > 0
  },
  motivazioniAssenze(state) {
    return state._motivazioniAssenze
  },
  motivazioniAssenzeDAD(state) {
    return state._motivazioniAssenzeDAD
  }
}
export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
