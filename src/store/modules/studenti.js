const state = {
  studenti: [],
  annoCorrente: '',
  indiceCorrente: 0
}

const getters = {
  studenteCorrente(state) {
    return state.studenti[state.indiceCorrente]
  }
}

const mutations = {
  setData(state, payload) {
    state.studenti = payload
  }
}

const actions = {
  setData(context, payload) {
    return new Promise((resolve, reject) => {
      context.commit('setData', payload.jwt.studenti)
      if (context.getters.studenteCorrente) {
        resolve()
      } else {
        reject(Error('Errore lettura studenti'))
      }
    })
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
