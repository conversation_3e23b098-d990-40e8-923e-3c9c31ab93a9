import dayjs from 'dayjs'
import { Api } from '@/data/api'

const getDefaultState = () => {
  return {
    _all: [],
    current: {},
    _type: null,
    _id_professore_richiesta: null,
    updating: 0,
    data_inizio: null,
    data_fine: null,
    target_slot: null
  }
}
const state = getDefaultState()

const getters = {
  all(state) {
    return state._all.map(x => {
      x.has_alert = dayjs(x.data).diff(dayjs(), 'hour') < 36 ? true : false
      return x
    })
  },
  current(state) {
    return state.current
  },
  updating(state) {
    return state.updating
  },
  date_params(state) {
    return {
      data_inizio: state.data_inizio,
      data_fine: state.data_fine
    }
  },
  current_id(state) {
    return state.updating > 0 ? state.updating : state.current.id
  },
  current_type(state) {
    return state._type
  }
}

const mutations = {
  set(state, payload) {
    state._all = payload
  },
  resetState(state) {
    state = getDefaultState()
    return state
  },
  setUpdating(state, payload) {
    state.updating = payload
  },
  setCurrent(state, payload) {
    state.current = payload
  },
  setTargetSlot(state, payload) {
    state.target_slot = payload
  },
  setDataInizio(state, payload) {
    state.data_inizio = payload
  },
  setDataFine(state, payload) {
    state.data_fine = payload
  },
  setType(state, payload) {
    state._type = payload
  },
  setIdProfessoreRichiesta(state, payload) {
    state._id_professore_richiesta = payload
  }
}

const actions = {
  // load(context, data) {
  //   return new Promise((resolve, reject) => {
  //     let api = new Api(data)
  //     api.fetchAll('colloqui_plain').then(
  //       value => {
  //         context.commit('set', { result: value })
  //         resolve()
  //       },
  //       error => {
  //         return reject(error.response)
  //       }
  //     )
  //   })
  // },
  setType({ commit }, data) {
    commit('setType', data)
  },
  async load({ commit, state }, data) {
    let api = new Api(data)
    console.log('type', state._type)
    try {
      let result = await api.fetchAll(`colloqui-${state._type}`)
      commit('set', result.data)
      return true
    } catch (error) {
      throw error
    }
  },
  async loadFiltered({ commit, getters }, data) {
    let api = new Api(data)
    if (getters.date_params) {
      api.addParams(getters.date_params)
    } else {
      return
    }
    try {
      let result = await api.fetchAll('colloqui_plain')
      commit('set', result.data)
    } catch (error) {
      throw error
    }
  },
  setIdProfessoreRichiesta({ commit }, data) {
    commit('setIdProfessoreRichiesta', data)
  },
  async richiestaColloquio({ state }, data) {
    let api = new Api(data)
    api.setPostData('id_professore', state._id_professore_richiesta)
    try {
      let result = await api.post('richiesta_colloquio_individuale')
      return result
    } catch (error) {
      throw error
    }
  },
  setUpdating({ commit }, data) {
    commit('setUpdating', data)
  },
  setCurrent(context, data) {
    context.commit('setCurrent', data)
  },
  setTargetSlot(context, data) {
    context.commit('setTargetSlot', data)
  },
  setDataInizio(context, data) {
    context.commit('setDataInizio', data)
  },
  setDataFine(context, data) {
    context.commit('setDataFine', data)
  },
  async prenota({ getters, state }, data) {
    let api = new Api(data)

    api.addParams({ slot: state.target_slot })
    try {
      let result = await api.put(`colloqui/${getters.current_id}`)
      return result
    } catch (error) {
      throw error
    }
  },
  async cancella({ getters }, data) {
    let api = new Api(data)
    try {
      let result = await api.delete(`colloqui/${getters.current_id}`)
      return result
    } catch (error) {
      throw error
    }
  },
  reset({ commit }) {
    commit('resetState')
  },
  refreshReset() {
    return getDefaultState()
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
