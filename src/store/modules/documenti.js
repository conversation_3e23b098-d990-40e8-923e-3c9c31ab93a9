import { Api } from '@/data/api'

const getDefaultState = () => {
  return {
    _all: []
  }
}
const state = getDefaultState()

const actions = {
  async loadDocumenti({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('documenti')
      commit('setDocumenti', result.data)
    } catch (error) {
      throw error
    }
  },
  refreshReset() {
    return getDefaultState()
  }
}

const getters = {
  all(state) {
    return state._all
  }
}

const mutations = {
  setDocumenti(state, payload) {
    state._all = payload
  },
  resetState(state) {
    state = getDefaultState()
    return state
  },
  clear(state) {
    state._all = []
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
