import { Api } from '@/data/api'
import { getMateriaDetails } from '@/utils'

const getDefaultState = () => {
  return {
    _all: [],
    data_inizio: null,
    data_fine: null
  }
}
const state = getDefaultState()

const actions = {
  loadArgomenti({ commit, getters }, data) {
    return new Promise((resolve, reject) => {
      let api = new Api(data)
      if (getters.date_params) {
        api.addParams(getters.date_params)
      }
      api
        .fetchAll('argomenti_plain')
        .then(value => {
          commit('set', { result: value })
          resolve(true)
        })
        .catch(error => {
          reject(error.response)
        })
    })
  },
  clearArgomenti({ commit }) {
    commit('clear')
  },
  setDataInizio(context, data) {
    context.commit('setDataInizio', data)
  },
  setDataFine(context, data) {
    context.commit('setDataFine', data)
  },
  reset({ commit }) {
    commit('resetState')
  },
  refreshReset() {
    return getDefaultState()
  }
}

const mutations = {
  set(state, payload) {
    state._all = payload.result.data
  },
  resetState(state) {
    state = getDefaultState()
    return state
  },
  clear(state) {
    state._all = []
  },
  setDataInizio(state, payload) {
    state.data_inizio = payload
  },
  setDataFine(state, payload) {
    state.data_fine = payload
  }
}

const getters = {
  date_params(state) {
    if (state.data_inizio && state.data_fine) {
      return {
        data_inizio: state.data_inizio,
        data_fine: state.data_fine
      }
    }
    return null
  },
  all(state) {
    return state._all.map((x, i) => {
      x.id = i
      x.body = x.dettaglio
      x = { ...x, ...getMateriaDetails(x.id_materia) }
      if (x.modulo !== x.dettaglio) {
        x.body += x.modulo
      }
      return x
    })
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
