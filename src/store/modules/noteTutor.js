import { Api } from '@/data/api'

const getDefaultState = () => {
  return {
    _all: [],
    notaFirma: null
  }
}
const state = getDefaultState()

const actions = {
  async loadNoteTutor({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('note_tutor')
      commit('setNote', result.data)
      return true
    } catch (error) {
      throw error
    }
  },
  setNotaFirma({ commit }, data) {
    commit('setNotaFirma', data)
  },
  async postFirma({ state }, data) {
    let api = new Api(data)
    api.setPostData('id_nota', state.notaFirma)
    let result = await api.post(`note_tutor`)
    return result
  },
  reset({ commit }) {
    commit('resetState')
  },
  refreshReset() {
    return getDefaultState()
  }
}

const mutations = {
  setNote(state, payload) {
    state._all = payload
  },
  setNotaFirma(state, id) {
    state.notaFirma = id
  },
  resetState(state) {
    state = getDefaultState()
    return state
  }
}

const getters = {
  all(state) {
    return state._all
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
