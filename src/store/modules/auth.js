import { authenticate, validateJWT, refreshTokenJWT } from '@/data/auth'
import { Api } from '@/data/api'
import { apiLoginBaseUrl } from '@/utils'
// import {Events} from '@/utils'

const state = {
  user: {},
  jwt: null,
  payload: {},
  showChangePassword: false,
  urlScuola: null
}

const getters = {
  isAuthenticated: state => {
    if (state.jwt === null) {
      return false
    }
    // console.log('isvalid ' + state.jwt.id_utente + validateJWT(state.jwt.token, state.jwt.id_utente))
    return true
  },
  jwtToken: state => {
    return state.jwt
  },
  userName: state => {
    return `${state.user.nome} ${state.user.cognome}`
  }
}

const mutations = {
  setJwtToken(state, payload) {
    state.jwt = payload.data.token
  },
  setUserData(state, payload) {
    state.user = payload.data
  },
  setPayload(state, payload) {
    state.payload = payload
  },
  setUrlScuola(state, payload) {
    state.urlScuola = payload.data.url_scuola
  },
  showChangePassword(state, payload) {
    state.showChangePassword = payload
  }
}

const actions = {
  showChangePasswordWindow({ commit }, data) {
    commit('showChangePassword', data)
  },
  async changePassword({ commit }, data) {
    let api = new Api(data)
    try {
      api.setPostData('old_password', data.old_password)
      api.setPostData('new_password', data.new_password)
      api.setRawUrl(apiLoginBaseUrl())
      let result = await api.put(`utenti/${data.id_scuola}/aggiorna_password`)
      commit('showChangePassword', {
        payload: false
      })
      return result
    } catch (error) {
      throw error
    }
  },
  logout({ commit }) {
    return new Promise(resolve => {
      commit('setJwtToken', {
        data: { token: null }
      })
      commit('setUserData', {
        data: {}
      })
      resolve()
    })
  },
  login({ commit }, userData) {
    return new Promise((resolve, reject) => {
      authenticate(userData)
        .then(response => {
          commit('setJwtToken', {
            data: response.data
          })
          commit('setUrlScuola', {
            data: response.data
          })
          commit('setUserData', {
            data: response.data
          })
          resolve(response.data)
        })
        .catch(error => {
          let msg = 'errore generico al login'
          if (error.response.status == 401) {
            msg = 'Codice utente e/o password non validi'
          }
          if (error.response.status == 408) {
            msg = error.response.data.detail
          }
          reject(msg)
        })
    })
  },
  async refreshAuth({ state }) {
    try {
      let response = await refreshTokenJWT(state.jwt, state.user.mastercom_id)
      // commit('setJwtToken', {
      //   data: response.data
      // })
      // commit('setUserData', {
      //   data: response.data
      // })
      return response.data
    } catch (error) {
      throw error
    }
  },
  async validateAuth({ commit, state }) {
    try {
      let response = await validateJWT(state.jwt, state.user.id_utente)
      commit('setJwtToken', {
        data: response.data
      })
      commit('setUserData', {
        data: response.data
      })
      return response.data
    } catch (error) {
      throw error
    }
  }
  // authenticate ({commit}) {
  //   return new Promise((resolve, reject) => {
  //     let myToken = state.jwt
  //     if (!isNaN(state.user.id_utente)) {
  //       validateJWT(myToken, state.user.id_utente)
  //       .then((response) => {
  //         commit('setJwtToken', {
  //           data: response.data
  //         })
  //         commit('setUserData', {
  //           data: response.data
  //         })
  //         resolve(response)
  //       })
  //       .catch((error) => {
  //         Api.errCatcher(error.response)
  //         reject(new Error())
  //       })
  //     }
  //     reject(new Error())
  //   })
  // }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
