import { fetchScuola } from '@/data/scuole.js'

const state = {
  scuola: {}
}

const mutations = {
  set(state, payload) {
    state.scuola = payload.result.data
  }
}

const getters = {}

const actions = {
  load(context, data) {
    return new Promise((resolve, reject) => {
      data.api = 'compiti'
      let result = fetchScuola(data.api, data.studente, data.anno, data.token)
      result.then(value => context.commit('set', { result: value }))
      resolve()
      reject()
    })
  },
  loadScuola(context, data) {
    return new Promise((resolve, reject) => {
      fetchScuola(data.mastercom_id).then(
        value => {
          context.commit('set', { result: value })
          resolve()
        },
        error => {
          return reject(error.response)
        }
      )
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  getters,
  actions
}
