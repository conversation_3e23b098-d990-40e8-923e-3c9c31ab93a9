const getDefaultState = () => {
  return {
    _content: ''
  }
}
const state = getDefaultState()

const getters = {
  content(state) {
    return state._content
  }
}

const mutations = {
  clearContent(state) {
    state._content = ''
  }
}

const actions = {
  clearContent({ commit }) {
    commit('clearContent')
  },
  refreshReset() {
    return getDefaultState()
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
