import dayjs from 'dayjs'
import { Api } from '@/data/api'

const getDefaultState = () => {
  return {
    _all: [],
    pasti_consumati: null,
    credito_residuo: '',
    allarme_credito_residuo: false,
    pasto_corrente: {},
    id_pasto_corrente: null,
    servizio_prenotato: null,
    allegati: [],
    _pagamenti: [],
    _elenco_pasti_fruiti: []
  }
}
const state = getDefaultState()

const getters = {
  allarmeCredito(state) {
    return state.allarme_credito_residuo
  },
  pastiConsumati(state) {
    return state.pasti_consumati
  },

  creditoResiduo(state) {
    return state.credito_residuo
  },

  pagamenti(state) {
    return state._pagamenti
  },

  idPastoCorrente(state) {
    return state.id_pasto_corrente
  },

  elencoPastiFruiti(state) {
    return state._elenco_pasti_fruiti.map(x => {
      x.data = dayjs(x.data).format('DD/MM/YYYY')
      return x
    })
  },

  pastoCorrente(state) {
    let result = {}
    let servizi = []
    let x = state.pasto_corrente
    if (x.servizi && x.servizi.length) {
      let selezionato_found = false
      for (let s of x.servizi.filter(x => x.codice !== '--')) {
        servizi.push(s)
        if (s.selezionato === true) {
          selezionato_found = true
        }
      }
      let s = x.servizi.filter(x => x.codice === '--')[0]
      if (selezionato_found === false) {
        s.selezionato = true
      }
      servizi.push(s)
    }
    result.scadenza = state.pasto_corrente.scadenza
    result.descrizione = state.pasto_corrente.descrizione
    result.id_pasto_precedente = state.pasto_corrente.id_pasto_precedente
    result.id_pasto_successivo = state.pasto_corrente.id_pasto_successivo
    result.servizi = servizi

    return result
  },

  isPastoPrenotabile(state) {
    return dayjs(state.pasto_corrente.scadenza) > dayjs()
  },
  isPrenotazionePossibile(state) {
    if (Array.isArray(state.pasto_corrente.servizi)) {
      return state.pasto_corrente.servizi.length > 0
    }
    return false
  }
}

const mutations = {
  setMense(state, payload) {
    state.pasti_consumati = payload.pasti_consumati
    state.credito_residuo = payload.credito_residuo
    state.allarme_credito_residuo = payload.allarme_credito_residuo
    state.id_pasto_corrente = payload.pasto_corrente.id
    //state.pasto_corrente = payload.pasto_corrente
  },

  setMensePagamenti(state, payload) {
    state._pagamenti = payload
  },

  setPastoCorrente(state, payload) {
    state.pasto_corrente = payload
  },

  setIdPastoCorrente(state, payload) {
    state.id_pasto_corrente = payload
  },

  setPastiFruiti(state, payload) {
    state._elenco_pasti_fruiti = payload
  },

  setServizioPrenotato(state, payload) {
    state.pasto_corrente.servizi = state.pasto_corrente.servizi.map(x => {
      x.selezionato = x.codice == payload
      return x
    })
    state.servizio_prenotato = payload
  },

  resetState(state) {
    state = getDefaultState()
    return state
  }
}

const actions = {
  async loadMense({ commit }, data) {
    let api = new Api(data)
    try {
      // console.log('MENSA LOAD MENSE')
      let result = await api.fetchAll('mense')
      commit('setMense', result.data)
      return true
    } catch (error) {
      throw error
    }
  },

  async loadMensePagamenti({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('mense/credito')
      commit('setMensePagamenti', result.data)
    } catch (error) {
      throw error
    }
  },

  async loadPastoCorrente({ commit, getters }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll(`mense/pasti/${getters.idPastoCorrente}`)
      commit('setPastoCorrente', result.data)
    } catch (error) {
      throw error
    }
  },

  async loadPastiFruiti({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('mense/pasti')
      commit('setPastiFruiti', result.data)
    } catch (error) {
      throw error
    }
  },

  async prenotaPasto({ state, getters }, data) {
    let api = new Api(data)
    api.setJsonData({ servizi: [state.servizio_prenotato] })
    try {
      await api.putJson(`mense/pasti/${getters.idPastoCorrente}`)
    } catch (error) {
      throw error
    }
  },

  setIdPastoCorrente({ commit }, data) {
    commit('setIdPastoCorrente', data)
  },

  setServizioPrenotato({ commit }, data) {
    commit('setServizioPrenotato', data)
  },

  reset({ commit }) {
    commit('resetState')
  },
  refreshReset() {
    return getDefaultState()
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
