import { Api } from '@/data/api'

const getDefaultState = () => {
  return {
    _pagelle: [],
    url_allegato: null
  }
}
const state = getDefaultState()

const getters = {
  pagelle(state) {
    return state._pagelle
  }
}

const mutations = {
  setPagelle(state, payload) {
    state._pagelle = payload
  },
  resetState(state) {
    Object.assign(state, getDefaultState())
  },
  setUrlAllegato(state, payload) {
    state.url_allegato = payload
  }
}

const actions = {
  async loadPagelle({ commit }, data) {
    try {
      let api = new Api(data)
      let result = await api.fetchAll('pagelle_plain')
      commit('setPagelle', result.data)
      return true
    } catch (error) {
      throw error
    }
  },
  async loadAllegato({ state }, data) {
    let api = new Api(data)
    try {
      api.setRawUrl(state.url_allegato)
      let result = await api.fetchRaw('blob')
      return result
    } catch (error) {
      throw error
    }
  },
  async setUrlAllegato({ commit }, url) {
    await commit('setUrlAllegato', url)
  },
  async reset({ commit }) {
    await commit('resetState')
  },
  refreshReset() {
    return getDefaultState()
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
