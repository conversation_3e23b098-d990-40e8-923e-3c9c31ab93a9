import { Api } from '@/data/api'

const getDefaultState = () => {
  return {
    _pagante: null,
    _opposizione: null,
    opt_pagante: null,
    opt_opposizione: null,
    ae_status: null,
    errore: null
  }
}

const state = getDefaultState()

const getters = {
  pagante(state) {
    return state._pagante ? state._pagante.conferma_pagante_ade : ''
  },
  opposizione(state) {
    return state._opposizione ? state._opposizione.opposizione_ade : ''
  },
  data_pagante(state) {
    return state._pagante ? state._pagante.data : null
  },
  data_opposizione(state) {
    return state._opposizione ? state._opposizione.data : null
  },
  AEStatus(state) {
    return state.ae_status
  },
  opt_pagante(state) {
    return state.opt_pagante
  },
  opt_opposizione(state) {
    return state.opt_opposizione
  }
}

const mutations = {
  setPagante(state, payload) {
    state._pagante = payload.data
  },
  setOpposizione(state, payload) {
    state._opposizione = payload.data
  },
  setOptPagante(state, payload) {
    state.opt_pagante = payload
  },
  setOptOpposizione(state, payload) {
    state.opt_opposizione = payload
  },
  setAEStatus(state, payload) {
    console.log(payload)
    state.ae_status = payload.attivo
  },
  setErrore(state, payload) {
    state.errore = payload
  },
  resetState(state) {
    for (const [key, value] of Object.entries(getDefaultState())) {
      state[key] = value
    }
    return true
  }
}

const actions = {
  async loadStatus({ commit }, data) {
    let api = new Api(data)
    try {
      // console.log('SEZIONE AE', data)
      let result = await api.fetchAll('ade')
      commit('setAEStatus', result.data)
    } catch (error) {
      throw error
    }
  },
  async loadPagante({ commit }, data) {
    let api = new Api(data)
    try {
      // console.log('AE LOAD PAGANTE')
      let result = await api.fetchAll('ade-pagante')
      commit('setPagante', result)
    } catch (error) {
      throw error
    }
  },
  async loadOpposizione({ commit }, data) {
    let api = new Api(data)
    try {
      // console.log('AE LOAD OPPOSIZIONE')
      let result = await api.fetchAll('ade-opposizione')
      commit('setOpposizione', result)
    } catch (error) {
      throw error
    }
  },
  async postPagante({ state, commit }, data) {
    let api = new Api(data)
    api.setPostData('conferma_pagante_ade', state.opt_pagante)
    let result = await api.post('ade-pagante')
    try {
      if (result.data.success) {
        commit('setPagante', result)
      }
    } catch {
      commit('setErrore', result)
    }
  },
  async postOpposizione({ state, commit }, data) {
    let api = new Api(data)
    api.setPostData('opposizione_ade', state.opt_opposizione)
    let result = await api.post('ade-opposizione')
    try {
      if (result.data.success) {
        commit('setOpposizione', result)
      }
    } catch {
      commit('setErrore', result)
    }
  },
  setOptPagante({ commit }, data) {
    // console.log('AE OPT PAGANTE')
    commit('setOptPagante', data)
  },
  setOptOpposizione({ commit }, data) {
    // console.log('AE OPT OPPOSIZIONE')
    commit('setOptOpposizione', data)
  },
  async reset({ commit }) {
    await commit('resetState')
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
