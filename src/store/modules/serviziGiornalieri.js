import { Api } from '@/data/api'

const getDefaultState = () => {
  return {
    _prenotazioni_giornaliere: [],
    _marketplace: [],
    _credito: [],
    _error_message: '',
    data_adesione: null,
    marketplace_adesione: null
  }
}
const state = getDefaultState()

const actions = {
  async loadCredito({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('credito_servizi')
      commit('setCreditoServizi', result.data)
      return true
    } catch (error) {
      throw error
    }
  },
  async loadPrenotazioniGiornaliere({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('prenotazioni_giornaliere')
      commit('setPrenotazioniGiornaliere', result.data)
      return true
    } catch (error) {
      throw error
    }
  },
  async loadMarketplace({ commit, state }, data) {
    let api = new Api(data)
    api.addParams({ data_adesione: state.data_adesione })
    try {
      let result = await api.fetchAll('servizi_giornalieri')
      commit('setMarketplace', result.data)
    } catch (error) {
      throw error
    }
  },
  async adesione({ commit, state }, data) {
    let api = new Api(data)
    api.setPostData('data_adesione', state.data_adesione)
    api.setPostData('id_marketplace', state.marketplace_adesione)
    try {
      let result = await api.post('prenotazioni_giornaliere')
      commit('setPrenotazione', result.data)
    } catch (error) {
      throw error
    }
  },
  async cancellazione({ commit, state }, data) {
    let api = new Api(data)
    api.setPostData('data_adesione', state.data_adesione)
    api.setPostData('id_marketplace', state.marketplace_adesione)
    try {
      let result = await api.deleteBody('prenotazioni_giornaliere')
      commit('cancellaPrenotazione', result.data)
    } catch (error) {
      throw error
    }
  },
  setDataAdesione({ commit }, data_adesione) {
    commit('setDataAdesione', data_adesione)
  },
  setMarketplaceAdesione({ commit }, marketplace_adesione) {
    commit('setMarketplaceAdesione', marketplace_adesione)
  },
  clearMarketplace({ commit }) {
    commit('setMarketplace', [])
  }
}

const getters = {
  prenotazioniGiornaliere(state) {
    return state._prenotazioni_giornaliere
  },
  marketplace(state) {
    return state._marketplace.filter(x => x.adesione_giornaliera === 'ATTIVA')
  },
  credito(state) {
    return state._credito.map(x => {
      x.servizi.map(
        y =>
          (y.costo_unitario_prenotazione = parseFloat(
            y.costo_unitario_prenotazione
          ))
      )
      return x
    })
  },
  errorMessage(state) {
    return state._error_message
  }
}

const mutations = {
  setPrenotazioniGiornaliere(state, payload) {
    state._prenotazioni_giornaliere = payload
  },
  setMarketplace(state, payload) {
    state._marketplace = payload
    state._error_message = ''
  },
  setPrenotazione(state, payload) {
    // console.log('payload', payload.adesioni)
    if (payload.esito != 'OK') {
      state._error_message = payload.messaggio
      return
    }
    for (let adesione of payload.adesioni) {
      state._marketplace = state._marketplace.map(x => {
        if (x.id_marketplace === adesione.id_marketplace) {
          x.adesione = 'SI'
        }
        return x
      })
    }
  },
  cancellaPrenotazione(state, payload) {
    if (payload.esito == 'OK') {
      state._marketplace = state._marketplace.map(x => {
        if (x.id_marketplace == state.marketplace_adesione) {
          x.adesione = 'NO'
        }
        return x
      })
    } else {
      state._error_message = payload.messaggio
    }
  },
  setDataAdesione(state, payload) {
    state.data_adesione = payload
  },
  setMarketplaceAdesione(state, payload) {
    state.marketplace_adesione = payload
  },
  setCreditoServizi(state, payload) {
    state._credito = payload
  },
  resetState(state) {
    state = getDefaultState()
    return state
  },
  refreshReset() {
    return getDefaultState()
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
