import { Api } from '@/data/api'
import dayjs from 'dayjs'

const getDefaultState = () => {
  return {
    // pagamenti effettuati
    _storico: [],
    // scadenze
    _articoli: [],
    _articoli_marketplace: [],
    _marketplace: [],
    _estratto_conto: [],
    _fatture_ricevute: [],
    _report: [],
    _carrello: [],
    _estratto_conto_crediti: [],
    _satispay_env: '',
    _satispay_key_id: null,
    _satispay_payment_id: null,
    _satispay_payment_detail: null,
    _satispay_staging_url: 'https://staging.online.satispay.com/web-button.js',
    _satispay_production_url: 'https://online.satispay.com/web-button.js',
    _stripe_env: '',
    _stripe_pk: null,
    _stripe_payment_id: null
  }
}

const setArticolo = (elenco, payload) => {
  return [
    ...state._articoli.filter(x => x.elenco != elenco),
    ...payload.map(x => {
      x.elenco = elenco
      x.cart_id = x.tipo + x.id
      x.acquistabile =
        (x.pagamento_satispay === true || x.pagamento_cc === true) &&
        x.pagato !== true
      return x
    })
  ]
}

const setArticoloMarketplace = (elenco, payload) => {
  return [
    ...state._articoli_marketplace.filter(x => x.elenco != elenco),
    ...payload.map(x => {
      x.elenco = elenco
      x.cart_id = x.tipo + x.id
      x.acquistabile =
        (x.pagamento_satispay === true || x.pagamento_cc === true) &&
        x.pagato !== true
      return x
    })
  ]
}
const state = getDefaultState()

const getters = {
  storico(state) {
    return state._storico
  },
  stripePK(state) {
    return state._stripe_pk
  },
  stripePaymentIntent(state) {
    return state._stripe_payment_id
  },
  satispayEnabled(state) {
    let a =
      state._articoli.filter(x => x.pagamento_satispay === true).length > 0
    let b =
      state._articoli_marketplace.filter(x => x.pagamento_satispay === true)
        .length > 0
    return a || b
  },
  stripeEnabled(state) {
    let a = state._articoli.filter(x => x.pagamento_cc === true).length > 0
    let b =
      state._articoli_marketplace.filter(x => x.pagamento_cc === true).length >
      0
    return a || b
  },
  creditCardStatus(state) {
    let a = state._articoli.filter(x => x.pagamento_cc === true).length > 0
    let b =
      state._articoli_marketplace.filter(x => x.pagamento_cc === true).length >
      0
    return a || b
  },
  articoli(state) {
    return state._articoli
  },
  marketplace(state) {
    return state._articoli_marketplace
  },
  estrattoConto(state) {
    return state._articoli
      .filter(x => x.elenco == 'estratto_conto')
      .map(x => {
        let style = ''
        if (x.credito) {
          style = 'credito'
        } else if (x.pagato) {
          style = 'pagato'
        } else if (x.scaduto) {
          style = 'scaduto'
        }
        x.descrizione = x.tipo == 'movimento' ? x.titolo : x.sottotitolo
        x.style = style
        x.anno = dayjs(x.data_scadenza).format('YYYY')
        return x
      })
  },
  fatture(state) {
    return state._fatture_ricevute.filter(x => x.tipo === 'fattura')
  },
  notecredito(state) {
    return state._fatture_ricevute.filter(x => x.tipo === 'nota di credito')
  },
  report(state) {
    return state._report
  },
  estrattoContoCrediti(state) {
    return state._estratto_conto_crediti.map(x => {
      x.estratto_conto.map(c => {
        if (c.tipo == 'P') {
          c.tipo_esteso = 'pagamento'
        }
        if (c.tipo == 'S') {
          c.tipo_esteso = 'rimborso_pagamento'
        }
        if (c.tipo == 'D') {
          c.tipo_esteso = 'deposito'
        }
        if (c.tipo == 'I') {
          c.tipo_esteso = 'integrazione'
        }
        if (c.tipo == 'R') {
          c.tipo_esteso = 'restituzione'
        }
        c.css_riga = c.segno == 'A' ? 'avere' : 'dare'
        return c
      })
      return x
    })
  },
  carrello(state) {
    return state._carrello
  },
  totaleCarrello(state) {
    let a = state._articoli
      .filter(x => state._carrello.includes(x.cart_id) === true)
      .reduce((prev, curr) => prev + curr.prezzo, 0)

    let b = state._articoli_marketplace
      .filter(x => state._carrello.includes(x.cart_id) === true)
      .reduce((prev, curr) => prev + curr.prezzo, 0)
    return a + b
  },
  articoliCarrello(state) {
    let a = JSON.parse(
      JSON.stringify(
        state._articoli
          .filter(x => state._carrello.includes(x.cart_id) === true)
          .map(x => {
            return { id: x.id, tipo: x.tipo }
          })
      )
    )
    let b = JSON.parse(
      JSON.stringify(
        state._articoli_marketplace
          .filter(x => state._carrello.includes(x.cart_id) === true)
          .map(x => {
            return { id: x.id, tipo: x.tipo }
          })
      )
    )
    // console.log('articoli ', a)
    // console.log('marketplace ', b)
    // console.log('carrello ', a.concat(b))
    return a.concat(b)
  },
  satispayEnv(state) {
    return state._satispay_env
  },
  satispayScriptUrl(state) {
    if (state._satispay_env == 'STAGING') {
      return state._satispay_staging_url
    }
    return state._satispay_production_url
  },
  satispayKeyId(state) {
    return state._satispay_key_id
  },
  satispayPaymentId(state) {
    return state._satispay_payment_id
  },
  satispayResult(state) {
    if (state._satispay_payment_detail === null) {
      return null
    }
    if ('details' in state._satispay_payment_detail) {
      return state._satispay_payment_detail.details.status
    }
    return null
  }
}

const mutations = {
  setEstrattoContoCrediti(state, payload) {
    state._estratto_conto_crediti = payload
  },
  setPagamenti(state, payload) {
    state._storico = payload
  },
  setArticoli(state, payload) {
    state._articoli = payload.filter(
      x => x.pagamento_cc === true || x.pagamento_satispay === true
    )
  },
  setMarketplace(state, payload) {
    const elenco = 'marketplace'
    // state._articoli = setArticolo(elenco, payload)
    state._articoli_marketplace = setArticoloMarketplace(elenco, payload)
  },
  setEstrattoConto(state, payload) {
    const elenco = 'estratto_conto'
    state._articoli = setArticolo(elenco, payload)
  },
  setFatture(state, payload) {
    state._fatture_ricevute = payload
  },
  setReport(state, payload) {
    state._report = payload
  },
  setSatispayConfig(state, payload) {
    state._satispay_env = payload.env
    state._satispay_key_id = payload.key_id
  },
  setStripeConfig(state, payload) {
    state._stripe_pk = payload.chiave
  },
  addCart(state, itemID) {
    state._carrello.push(itemID)
  },
  removeCart(state, itemID) {
    state._carrello = state._carrello.filter(x => x !== itemID)
  },
  setSatispayPaymentId(state, data) {
    state._satispay_payment_id = data.payment_intent.id
  },
  setSatispayPaymentDetail(state, data) {
    state._satispay_payment_detail = data
  },
  setStripePaymentId(state, data) {
    state._stripe_payment_id = data.payment_intent.client_secret
  },
  emptyCart(state) {
    state._satispay_payment_id = null
    state._satispay_payment_detail = null
    state._stripe_payment_id = null
    state._carrello = []
  },
  reset(state) {
    for (const [key, value] of Object.entries(getDefaultState())) {
      state[key] = value
    }
    return true
  }
}

const actions = {
  reset({ commit }) {
    commit('reset')
  },
  async loadStripeConfig({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('chiavepagamenti')
      commit('setStripeConfig', result.data)
    } catch (error) {
      throw error
    }
  },
  async getStripePaymentIntent({ commit, getters }, data) {
    let api = new Api(data)
    api.setJsonData({ articoli: getters.articoliCarrello })
    try {
      let result = await api.postJson('transaction')
      commit('setStripePaymentId', result.data)
      return result.data
    } catch (error) {
      throw error
    }
  },
  async loadCrediti({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('crediti')
      commit('setEstrattoContoCrediti', result.data)
    } catch (error) {
      throw error
    }
  },
  async loadStorico({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('pagamenti')
      commit('setPagamenti', result.data)
    } catch (error) {
      throw error
    }
  },
  async loadArticoli({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('articoli')
      commit('setArticoli', result.data)
    } catch (error) {
      throw error
    }
  },
  async loadMarketplace({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('marketplace')
      commit('setMarketplace', result.data)
    } catch (error) {
      throw error
    }
  },
  async loadEstrattoConto({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('estratto-conto')
      commit('setEstrattoConto', result.data)
    } catch (error) {
      throw error
    }
  },
  async loadFatture({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('fatture_ricevute')
      commit('setFatture', result.data)
    } catch (error) {
      throw error
    }
  },

  async loadReport({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('report')
      commit('setReport', result.data)
    } catch (error) {
      throw error
    }
  },

  async loadSatispayConfig({ commit, getters }, data) {
    let api = new Api(data)
    api.setJsonData({ articoli: getters.articoliCarrello })
    try {
      let result = await api.fetchAll('satispay-config')
      commit('setSatispayConfig', result.data)
    } catch (error) {
      throw error
    }
  },
  async getSatispayPaymentId({ commit, getters }, data) {
    let api = new Api(data)
    api.setJsonData({ articoli: getters.articoliCarrello })
    try {
      let result = await api.postJson('satispay')
      commit('setSatispayPaymentId', result.data)
      return true
    } catch (error) {
      throw error
    }
  },
  async getSatispayPaymentDetail({ commit, getters }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll(`satispay/${getters.satispayPaymentId}`)
      commit('setSatispayPaymentDetail', result.data)
      return true
    } catch (error) {
      throw error
    }
  },
  addToCart({ commit }, data) {
    commit('addCart', data)
  },
  removeFromCart({ commit }, data) {
    commit('removeCart', data)
  },
  emptyCart({ commit }) {
    commit('emptyCart')
  },
  refreshReset() {
    return getDefaultState()
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
