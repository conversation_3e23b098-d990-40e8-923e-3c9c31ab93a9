import { Api } from '@/data/api'

const getDefaultState = () => {
  return {
    materie: [],
    materieFilter: [],
    _materieStudenteCorrente: {},
    _labelMaxLength: 30
  }
}
const state = getDefaultState()

const getters = {
  materieStudenteCorrente(state) {
    if (!state._materieStudenteCorrente.length) {
      return null
    }
    let data = state._materieStudenteCorrente.sort(
      (a, b) => a.ordinamento - b.ordinamento
    )

    data = data.map((x, i) => {
      x.indiceColore = i + 1
      x.label =
        x.descrizione.length > state._labelMaxLength
          ? `${x.descrizione.substring(0, state._labelMaxLength)}...`
          : `${x.descrizione}`
      x.insegnante = x.professori.reduce((acc, cv) => {
        return acc !== '' ? `${acc}, ${cv.nome}` : cv.nome
      }, '')
      return x
    })
    return data
  }
}

const mutations = {
  set(state, payload) {
    state._materieStudenteCorrente = payload.result.data
  },
  setFilter(state, data) {
    state.materieFilter = data
  },
  resetState(state) {
    state = Object.assign(state, getDefaultState())
    return state
  }
}

const actions = {
  load({ commit }, data) {
    return new Promise((resolve, reject) => {
      let api = new Api(data)
      api
        .fetchAll('materie_nextapi')
        .then(value => {
          commit('set', { result: value })
          resolve(value)
        })
        .catch(error => {
          let msg = 'errore generico caricamento materie studente'
          if (error.response.status == 401) {
            msg = 'Non è stato possibile caricare le materie'
          }
          if (error.response.status == 500) {
            msg = 'Impostazioni materie non corrette'
          }
          reject(msg)
        })
    })
  },
  async reset({ commit }) {
    await commit('resetState')
  },
  filter({ commit }, data) {
    commit('setFilter', data)
  },
  clearFilter({ commit }) {
    commit('setFilter', [])
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
