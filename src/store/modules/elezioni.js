import { Api } from '@/data/api'

const getDefaultState = () => {
  return {
    _all: [],
    data_inizio: null,
    data_fine: null
  }
}
const state = getDefaultState()

const actions = {
  loadElezioni({ commit }, data) {
    return new Promise((resolve, reject) => {
      let api = new Api(data)
      api
        .fetchAll('elezioni')
        .then(value => {
          commit('set', { result: value })
          resolve()
        })
        .catch(error => {
          reject(error.response)
        })
    })
  },
  refreshReset() {
    return getDefaultState()
  }
}

const getters = {
  all(state) {
    return state._all.map(x => {
      x.data = x.data_inizio
      return x
    })
  }
}

const mutations = {
  set(state, payload) {
    state._all = payload.result.data
  },
  resetState(state) {
    state = getDefaultState()
    return state
  },
  clear(state) {
    state._all = []
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
