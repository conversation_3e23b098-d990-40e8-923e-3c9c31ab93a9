import dayjs from 'dayjs'
import { Api } from '@/data/api'

const getDefaultState = () => {
  return {
    _file_list: [],
    _thread: [],
    _thread_inviati: [],
    _comunicazioni: [],
    _comunicazioni_elenco: [],
    _comunicazioni_inviate: [],
    _dettaglio: null,
    _dettaglio_thread: null,
    _bacheca: [],
    _destinatari: [],
    _attachment_ids: [],
    _json_payload: {},
    gruppo_selezionato: '',
    url_allegato: null,
    data_inizio: null,
    data_fine: null,
    short_size: 30,
    id_presa_visione: null,
    id_messaggio_presa_visione: null,
    id_lettura: null,
    id_dettaglio: null,
    id_dettaglio_thread: null,
    nuovo_messaggio: {},
    attachment: null,
    attachment_uploading: false,
    upload_percentage: 0,
    profile_update: true,
    upload_progress_callback: () => {},
    on_upload_progress(progressEvent) {
      return parseInt(
        Math.round(progressEvent.loaded / progressEvent.total) * 100
      )
    },
    risposta: {},
    _id_thread_risposta: null
  }
}
const state = getDefaultState()

const getters = {
  attachment_uploading(state) {
    return state.attachment_uploading
  },
  id_lettura(state) {
    return state.id_lettura
  },
  id_presa_visione(state) {
    return state.id_presa_visione
  },
  id_messaggio_presa_visione(state) {
    return state.id_messaggio_presa_visione
  },
  comunicazioni(state) {
    return state._comunicazioni
      .filter(x => x.mittente !== null)
      .map(x => {
        x.data_lettura_formatted = x.data_lettura
          ? dayjs(x.data_lettura).format('DD MMMM YYYY - HH:mm')
          : false
        return x
      })
  },
  thread(state) {
    return state._thread
  },
  dettaglio_thread(state) {
    if (state._dettaglio_thread === null) {
      return null
    }
    let data_lettura_formatted = state._dettaglio_thread.data_lettura
      ? dayjs(state._dettaglio_thread.data_lettura).format(
          'DD MMMM YYYY - HH:mm'
        )
      : false
    state._dettaglio_thread.data_lettura_formatted = data_lettura_formatted
    state._dettaglio_thread.titolo = state._dettaglio_thread[0].titolo
    return state._dettaglio_thread
  },
  thread_inviati(state) {
    return state._thread_inviati.map(x => {
      x.destinatari = []
      if (x.sottotitolo) {
        x.destinatari = x.sottotitolo.split(', ')
        x.destinatari_short = x.sottotitolo.trim().substr(0, state.short_size)
        if (x.destinatari_short.length < x.sottotitolo.trim().length) {
          x.destinatari_short = `${x.destinatari_short}&ellipsis;`
        }
      }
      return x
    })
  },
  comunicazioni_elenco(state) {
    return state._comunicazioni_elenco
      .filter(x => x.mittente !== null)
      .map(x => {
        x.data_lettura_formatted = x.data_lettura
          ? dayjs(x.data_lettura).format('DD MMMM YYYY - HH:mm')
          : false
        return x
      })
  },
  dettaglio(state) {
    state._dettaglio.data_lettura_formatted = state._dettaglio.data_lettura
      ? dayjs(state._dettaglio.data_lettura).format('DD MMMM YYYY - HH:mm')
      : false
    return state._dettaglio
  },
  bacheca(state) {
    return state._bacheca
  },
  gruppi(state) {
    let gruppi = []
    for (let d of state._destinatari) {
      if (gruppi.includes(d.gruppo) === false) {
        gruppi.push(d.gruppo)
      }
    }
    if ('id_destinatario' in state.risposta) {
      if (
        state._destinatari.filter(x => x.id === state.risposta.id_mittente)
          .length === 0
      ) {
        gruppi.push('Destinatari')
      }
    }
    return gruppi
  },
  destinatari(state) {
    return state._destinatari
  },
  destinatariFiltrati(state, getters) {
    let result = []
    let gruppi = getters.gruppi
    for (let gruppo of gruppi) {
      let object = {
        label: gruppo
      }
      let destinatari = state._destinatari
        .filter(item => item.gruppo === gruppo)
        .map(item => {
          return {
            label: item.descrizione,
            value: item.id
          }
        })
      object.options = destinatari
      result.push(object)
    }
    if (
      'id_destinatario' in state.risposta &&
      'destinatario' in state.risposta
    ) {
      if (
        state._destinatari.filter(x => x.id === state.risposta.id_mittente)
          .length === 0
      ) {
        // console.log('aggiungo ', state.risposta, result)
        result.map(r => {
          if (r.label === 'Destinatari') {
            r.options.push({
              label: `${state.risposta.destinatario.cognome} ${
                state.risposta.destinatario.nome
              }`,
              value: state.risposta.id_destinatario
            })
          }
        })
      }
    }
    return result
  },
  richiesta_presa_visione(state) {
    return state._comunicazioni.filter(x => x.presa_visione === 0)
  },
  comunicazioni_inviate(state) {
    return state._comunicazioni_inviate.map(x => {
      x.destinatari = []
      if (x.sottotitolo) {
        x.destinatari = x.sottotitolo.split(', ')
        x.destinatari_short = x.sottotitolo.trim().substr(0, state.short_size)
        if (x.destinatari_short.length < x.sottotitolo.trim().length) {
          x.destinatari_short = `${x.destinatari_short}&ellipsis;`
        }
      }
      return x
    })
  },
  date_params(state, getters, rootState) {
    return {
      data_inizio: dayjs(rootState.main.dataInizioAnno).format('YYYY-MM-DD'),
      data_fine: dayjs().format('YYYY-MM-DD')
    }
  }
}

const mutations = {
  buildThreadPayload(state) {
    let payload = {}
    if (state._id_thread_risposta) {
      payload['id_thread'] = state._id_thread_risposta
    } else {
      payload['destinatari'] = []
      for (let item of state.nuovo_messaggio.destinatari) {
        payload['destinatari'].push(item)
      }
      payload['titolo'] = state.nuovo_messaggio.titolo
    }
    payload['testo'] = state.nuovo_messaggio.testo
    payload['presa_visione'] = state.nuovo_messaggio.presa_visione
    if (state._attachment_ids.length) {
      payload['allegati'] = []
      for (let item of state._attachment_ids) {
        payload['allegati'].push(item)
      }
    }
    state._json_payload = payload
  },
  setFileList(state, payload) {
    state._file_list = payload
  },
  setProfileUpdate(state, payload) {
    state.profileUpdate = payload
  },
  setBacheca(state, payload) {
    state._bacheca = payload
  },
  setComunicazioniInviate(state, payload) {
    state._comunicazioni_inviate = payload
  },
  setComunicazioni(state, payload) {
    state._comunicazioni = payload
  },
  setComunicazioniElenco(state, payload) {
    state._comunicazioni_elenco = payload
  },
  setThreadInviati(state, payload) {
    state._thread_inviati = payload
  },
  setThread(state, payload) {
    state._thread = payload
  },
  setDettaglio(state, payload) {
    state._dettaglio = payload
  },
  setDettaglioThread(state, payload) {
    state._dettaglio_thread = payload
  },
  setDataInizio(state, payload) {
    state.data_inizio = payload
  },
  setDataFine(state, payload) {
    state.data_fine = payload
  },
  setUrlAllegato(state, payload) {
    state.url_allegato = payload
  },
  setDestinatari(state, payload) {
    state._destinatari = payload
  },
  setGruppoSelezionato(state, payload) {
    state.gruppo_selezionato = payload
  },
  setOggetto(state, payload) {
    state.nuovo_messaggio.titolo = payload
  },
  setTesto(state, payload) {
    state.nuovo_messaggio.testo = payload
  },
  setConfermaLettura(state, payload) {
    state.nuovo_messaggio.presa_visione = payload
  },
  setDestinatariMessaggio(state, payload) {
    state.nuovo_messaggio.destinatari = []
    for (let item of payload) {
      state.nuovo_messaggio.destinatari.push(item)
    }
  },
  setAttachment(state, payload) {
    state.attachment = payload
  },
  setAttachmentId(state, payload) {
    state._attachment_ids.push(payload.data)
  },
  setAttachmentRootName(state, payload) {
    state._attachment_ids.push(payload.data)
  },
  setIdPresaVisione(state, payload) {
    state.id_presa_visione = payload
  },
  setIdMessaggioPresaVisione(state, payload) {
    state.id_presa_visione = payload.id
    state.id_messaggio_presa_visione = payload.id_messaggio
  },
  setIdLettura(state, payload) {
    state.id_lettura = payload
  },
  setIdDettaglio(state, payload) {
    state.id_dettaglio = payload
  },
  setIdThread(state, payload) {
    state.id_dettaglio_thread = payload
  },
  setPresaVisione(state, data) {
    state._comunicazioni_elenco = state._comunicazioni_elenco.map(x => {
      if (x.id === state.id_presa_visione) {
        if (x.presa_visione === 0) {
          x.presa_visione = data.presa_visione
        }
      }
      return x
    })
  },
  setPresaVisioneThread(state, data) {
    state._thread = state._thread.map(x => {
      if (x.id === state.id_presa_visione) {
        if (x.presa_visione === 0) {
          x.presa_visione = data.result == 'ok'
        } else {
          x.sottoscrizione = data.result == 'ok'
        }
      }
      return x
    })
  },
  setLettura(state) {
    state._comunicazioni_elenco = state._comunicazioni_elenco.map(x => {
      if (x.id === state.id_lettura) {
        x.da_leggere = false
      }
      return x
    })
  },
  setLetturaThread(state, payload) {
    state._thread = state._thread.map(x => {
      if (x.id === payload) {
        x.da_leggere = false
      }
      return x
    })
  },
  setRisposta(state, payload) {
    state.risposta.id_destinatario = payload.id_mittente
    state.risposta.destinatario = payload.mittente
    state.risposta.oggetto = `RE: ${payload.titolo}`
    state.risposta.messaggio = `<p></p><blockquote>${
      payload.dettaglio
    }</blockquote>`
  },
  setRispostaThread(state, payload) {
    state._id_thread_risposta = payload[0].id
    state.risposta.id_messaggio = payload[0].id_messaggio
    state.risposta.oggetto = payload[0].titolo
    state.risposta.messaggio = `<p></p><blockquote>${
      payload[0].dettaglio
    }</blockquote>`
  },
  setAttachmentUploading(state, payload) {
    state.attachment_uploading = payload
  },
  setUploadProgressBinding(state, func) {
    state.upload_progress_callback = func
  },
  removeAttachment(state, data) {
    state._attachment_ids = state._attachment_ids.filter(x => x !== data)
  },
  clearRisposta(state) {
    state.risposta = {}
  },
  resetState(state) {
    for (const [key, value] of Object.entries(getDefaultState())) {
      state[key] = value
    }
    return true
  },
  resetAttachments(state) {
    state._attachment_ids = []
  }
}

const actions = {
  async loadComunicazioni({ commit, getters, state }, data) {
    let api = new Api(data)
    if (getters.date_params) {
      //api.addParams(getters.date_params)
    } else {
      return
    }
    if (state.profileUpdate === false) {
      api.addParams({ profile_update: 0 })
    }
    try {
      let result = await api.fetchAll('comunicazioni')
      commit('setComunicazioni', result.data)
    } catch (error) {
      throw error
    }
  },
  async loadComunicazioniElenco({ commit, getters, state }, data) {
    let api = new Api(data)
    if (getters.date_params) {
      //api.addParams(getters.date_params)
    } else {
      return
    }
    if (state.profileUpdate === false) {
      api.addParams({ profile_update: 0 })
    }
    try {
      let result = await api.fetchAll('comunicazioni_elenco')
      commit('setComunicazioniElenco', result.data)
    } catch (error) {
      throw error
    }
  },
  async loadThread({ commit, getters, state }, data) {
    let api = new Api(data)
    if (getters.date_params) {
      //api.addParams(getters.date_params)
    } else {
      return
    }
    if (state.profileUpdate === false) {
      api.addParams({ profile_update: 0 })
    }
    try {
      let result = await api.fetchAll('thread')
      commit('setThread', result.data)
    } catch (error) {
      throw error
    }
  },
  async loadDettaglio({ commit, state }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll(`comunicazioni/${state.id_dettaglio}`)
      commit('setDettaglio', result.data)
    } catch (error) {
      throw error
    }
  },
  async loadDettaglioThread({ commit, state }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll(`thread/${state.id_dettaglio_thread}`)
      commit('setDettaglioThread', result.data)
    } catch (error) {
      throw error
    }
  },
  async loadAllegato({ state }, data) {
    let api = new Api(data)
    try {
      api.setRawUrl(state.url_allegato)
      let result = await api.fetchRaw('blob')
      return result
    } catch (error) {
      throw error
    }
  },
  async loadComunicazioniInviate({ commit }, data) {
    let api = new Api(data)
    // if (getters.date_params) {
    //   api.addParams(getters.date_params)
    // } else {
    //   return
    // }
    try {
      let result = await api.fetchAll('messaggi')
      commit('setComunicazioniInviate', result.data)
    } catch (error) {
      throw error
    }
  },
  async loadThreadInviati({ commit }, data) {
    let api = new Api(data)
    // if (getters.date_params) {
    //   api.addParams(getters.date_params)
    // } else {
    //   return
    // }
    try {
      let result = await api.fetchAll('thread-inviati')
      commit('setThreadInviati', result.data)
    } catch (error) {
      throw error
    }
  },
  async loadBacheca({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('notizie')
      commit('setBacheca', result.data)
    } catch (error) {
      throw error
    }
  },
  async loadBachecaThread({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('bacheca')
      commit('setBacheca', result.data)
    } catch (error) {
      console.log('error', error)
      throw error
    }
  },
  async loadDestinatari({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('destinatari')
      commit('setDestinatari', result.data)
    } catch (error) {
      throw error
    }
  },
  async loadDestinatariThread({ commit }, data) {
    let api = new Api(data)
    try {
      let result = await api.fetchAll('destinatari-utente')
      commit('setDestinatari', result.data)
    } catch (error) {
      throw error
    }
  },
  async setUrlAllegato({ commit }, url) {
    await commit('setUrlAllegato', url)
  },
  async setIdPresaVisione({ commit }, id) {
    await commit('setIdPresaVisione', id)
    return true
  },
  async setIdPresaVisioneThread({ commit }, payload) {
    await commit('setIdMessaggioPresaVisione', payload)
    return true
  },
  async setIdLettura({ commit }, id) {
    await commit('setIdLettura', id)
  },
  async setLetturaThread({ commit }, id) {
    await commit('setLetturaThread', id)
  },
  setIdDettaglio({ commit }, id) {
    commit('setIdDettaglio', id)
  },
  setIdThread({ commit }, id) {
    commit('setIdThread', id)
  },
  async sendMessage({ state }, data) {
    let api = new Api(data)
    for (let item of state.nuovo_messaggio.destinatari) {
      api.setPostData('destinatari[]', item)
    }
    api.setPostData('titolo', state.nuovo_messaggio.titolo)
    api.setPostData('testo', state.nuovo_messaggio.testo)
    api.setPostData('presa_visione', state.nuovo_messaggio.presa_visione)
    if (state._attachment_ids.length) {
      for (let item of state._attachment_ids) {
        api.setPostData('files[]', item)
      }
    }
    try {
      let result = await api.post('messaggi')
      return result
    } catch (error) {
      throw error
    }
  },
  buildThreadPayload({ commit }) {
    commit('buildThreadPayload')
  },
  async sendThreadJson({ state }, data) {
    try {
      let api = new Api(data)
      api.setJsonData(state._json_payload)
      let result = await api.postJson('thread-json')
      return result
    } catch (error) {
      console.log('error', error)
      throw error
    }
  },
  async sendThread({ state }, data) {
    let api = new Api(data)
    let payload = {}
    if (state._id_thread_risposta) {
      payload['id_thread'] = state._id_thread_risposta
    } else {
      payload['destinatari'] = []
      for (let item of state.nuovo_messaggio.destinatari) {
        payload['destinatari'].push(item)
      }
      payload['titolo'] = state.nuovo_messaggio.titolo
    }
    payload['testo'] = state.nuovo_messaggio.testo
    payload['presa_visione'] = state.nuovo_messaggio.presa_visione
    if (state._attachment_ids.length) {
      payload['allegati'] = []
      for (let item of state._attachment_ids) {
        payload['allegati'].push(item)
      }
    }

    try {
      api.setJsonData(payload)
      let result = await api.postJson('thread-json')
      return result
    } catch (error) {
      throw error
    }
  },
  async setAttachment({ commit }, data) {
    commit('setAttachment', data)
  },
  resetMessageAttachments({ commit }) {
    commit('resetAttachments')
  },
  async postAttachment({ commit }, data) {
    let api = new Api(data.auth)
    // console.log('DATA FILE', data.file)
    // console.log('DATA onPROGRESS', data.onProgress)
    try {
      api.setMultiPart()
      api.setFormData('file', data.file)
      api.setOnUploadProgress(data.onProgress)
      let result = await api.post('messaggi/add_attachment')
      commit('setAttachmentId', result)
      return result
    } catch (error) {
      throw error
    }
  },
  setProfileUpdate({ commit }, data) {
    commit('setProfileUpdate', data)
  },
  setAttachmentId({ commit }, data) {
    commit('setAttachmentId', data)
  },
  setAttachmentRootName({ commit }, data) {
    commit('setAttachmentRootName', data)
  },
  removeAttachment({ commit }, data) {
    console.log(data)
    commit('removeAttachment', data)
  },
  setFileList({ commit }, data) {
    commit('setFileList', data)
  },
  setUploadProgressBinding({ commit }, func) {
    commit('setUploadProgressBinding', func)
  },
  async inviaPresaVisione({ commit, state }, data) {
    let api = new Api(data)
    try {
      api.setPostData('scope', 'presa_visione')
      let result = await api.put(`comunicazioni/${state.id_presa_visione}`)
      commit('setPresaVisione', result.data[0])
      return result.data[0].presa_visione
    } catch (error) {
      throw error
    }
  },

  async inviaPresaVisioneThread({ commit, state }, data) {
    let api = new Api(data)
    try {
      // api.setPostData('scope', 'presa_visione')
      let result = await api.put(
        `thread/${state.id_presa_visione}/${state.id_messaggio_presa_visione}`
      )
      commit('setPresaVisioneThread', result.data)
      return result.data.result == 'ok'
    } catch (error) {
      throw error
    }
  },

  async inviaLettura({ commit, state }, data) {
    let api = new Api(data)
    try {
      api.setPostData('scope', 'lettura')
      let result = await api.put(`comunicazioni/${state.id_lettura}`)
      commit('setLettura')
      return result
    } catch (error) {
      throw error
    }
  },
  setRisposta({ commit }, data) {
    commit('setRisposta', data)
  },
  setRispostaThread({ commit }, data) {
    commit('setRispostaThread', data)
  },
  clearRisposta({ commit }) {
    commit('clearRisposta')
  },
  setDataInizio(context, data) {
    context.commit('setDataInizio', data)
  },

  setDataFine(context, data) {
    context.commit('setDataFine', data)
  },

  setGruppoSelezionato(context, data) {
    context.commit('setGruppoSelezionato', data)
  },

  setDestinatariMessaggio({ commit }, data) {
    commit('setDestinatariMessaggio', data)
  },

  setOggetto({ commit }, data) {
    commit('setOggetto', data)
  },

  setTesto({ commit }, data) {
    commit('setTesto', data)
  },

  setConfermaLettura({ commit }, data) {
    commit('setConfermaLettura', data)
  },

  setAttachmentUploading({ commit }, data) {
    commit('setAttachmentUploading', data)
  },

  reset({ commit }) {
    commit('resetState')
  },
  refreshReset() {
    return getDefaultState()
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
