import { Api } from '@/data/api'

const getDefaultState = () => {
  return {
    _all: [],
    id_presa_visione: null,
    solo_novita: false
  }
}
const state = getDefaultState()

const actions = {
  loadNoteDisciplinari(context, data) {
    return new Promise((resolve, reject) => {
      let api = new Api(data)
      api
        .fetchAll('note_plain')
        .then(value => {
          context.commit('set', { result: value })
          resolve(true)
        })
        .catch(error => {
          reject(error.response)
        })
    })
  },
  setSoloNovita(context, data) {
    context.commit('setSoloNovita', data)
  },
  setPresaVisione(context, data) {
    context.commit('setPresaVisione', data)
  },
  async inviaPresaVisione({ state }, data) {
    try {
      let api = new Api(data)
      let result = await api.put(`presa_visione_note/${state.id_presa_visione}`)
      return result
    } catch (error) {
      throw error
    }
  },
  reset({ commit }) {
    commit('resetState')
  },
  refreshReset() {
    return getDefaultState()
  }
}

const mutations = {
  set(state, payload) {
    state._all = payload.result.data
  },
  resetState(state) {
    for (const [key, value] of Object.entries(getDefaultState())) {
      state[key] = value
    }
    return true
  },
  setSoloNovita(state, payload) {
    state.solo_novita = payload
  },
  setPresaVisione(state, payload) {
    state.id_presa_visione = payload
  }
}

const getters = {
  all(state) {
    return state._all.map(item => {
      if (item.presa_visione == true && item.data_presa_visione > 0) {
        item.confirmed = true
        item.confirm_text = item.data_presa_visione_tradotta
        let [a, b] = item.data_presa_visione_tradotta.split(' ')
        item.confirm_date = a
        item.confirm_time = b
      } else {
        item.confirmed = false
        item.confirm_text = ''
      }
      return item
    })
  },
  tags(state) {
    const key = 'tag'
    const tKey = 'codice'
    const sKey = 'ordinamento'

    return [
      ...new Map(state._all.map(item => [item[key][tKey], item[key]])).values()
    ].sort((a, b) => {
      if (a[sKey] > b[sKey]) {
        return 1
      }
      if (a[sKey] < b[sKey]) {
        return -1
      }
    })
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
