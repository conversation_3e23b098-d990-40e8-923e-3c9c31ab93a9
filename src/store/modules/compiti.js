import dayjs from 'dayjs'
import { Api } from '@/data/api'
import { getMateriaDetails } from '@/utils'

const getDefaultState = () => {
  return {
    _all: [],
    data_inizio: null,
    data_fine: null,
    solo_novita: false,
    show_all: false
  }
}
const state = getDefaultState()

const actions = {
  loadCompiti({ commit, getters }, data) {
    return new Promise((resolve, reject) => {
      let api = new Api(data)
      if (getters.date_params) {
        api.addParams(getters.date_params)
      }
      api.fetchAll('compiti_plain').then(
        value => {
          commit('set', { result: value })
          resolve(true)
        },
        error => {
          reject(error.response)
        }
      )
    })
  },
  setDataInizio(context, data) {
    context.commit('setDataInizio', data)
  },

  setDataFine(context, data) {
    context.commit('setDataFine', data)
  },

  setSoloNovita(context, data) {
    context.commit('setSoloNovita', data)
  },

  setShowAll(context, data) {
    context.commit('setShowAll', data)
  },

  clearCompiti({ commit }) {
    commit('clear')
  },

  reset({ commit }) {
    commit('resetState')
  },
  refreshReset() {
    return getDefaultState()
  }
}

const mutations = {
  set(state, payload) {
    state._all = payload.result.data
  },
  resetState(state) {
    for (const [key, value] of Object.entries(getDefaultState())) {
      state[key] = value
    }
    return true
  },
  clear(state) {
    state._all = []
  },
  setDataInizio(state, payload) {
    state.data_inizio = payload
  },
  setDataFine(state, payload) {
    state.data_fine = payload
  },
  setSoloNovita(state, payload) {
    state.solo_novita = payload
  },
  setShowAll(state, payload) {
    state.show_all = payload
  }
}

const getters = {
  date_params(state) {
    if (state.data_inizio && state.data_fine) {
      return {
        data_inizio: state.data_inizio,
        data_fine: state.data_fine
      }
    }
    return null
  },
  all(state) {
    let res = state._all.map((x, i) => {
      x.id = i
      x = { ...x, ...getMateriaDetails(x.id_materia) }
      return x
    })
    if (state.show_all != true) {
      res = res.filter(x => dayjs(x.data) >= dayjs())
    }
    return res.sort((a, b) =>
      dayjs(a.data).isBefore(dayjs(b.data))
        ? -1
        : dayjs(a.data).isAfter(dayjs(b.data))
        ? 1
        : 0
    )
  },
  showSoloNovita(state) {
    return state.solo_novita
  },
  showAll(state) {
    return state.show_all
  }
}

export default {
  namespaced: true,
  state,
  getters,
  mutations,
  actions
}
