import Vue from 'vue'
import Vuex from 'vuex'

// modules
import app from './modules/app'
import auth from './modules/auth' // manager dei processi di autenticazione, validazione e logout utente
import main from './modules/main'
import apiErrorHandler from './modules/apiErrorHandler'
// import scuole from './modules/scuole' // valori globali della webapp
// import studenti from './modules/studenti' // valori studenti
import materie from './modules/materie'
import agenda from './modules/agenda'
import aggiornamenti from './modules/aggiornamenti'
import argomenti from './modules/argomenti' // manager sezione argomenti
import annotazioni from './modules/annotazioni'
import assenze from './modules/assenze'
import compiti from './modules/compiti'
import noteDisciplinari from './modules/noteDisciplinari'
import voti from './modules/voti'
import orario from './modules/orario'
import pagelle from './modules/pagelle'
import comunicazioni from './modules/comunicazioni'
import messageEditor from './modules/messageEditor'
import alternanza from './modules/alternanza'
import pagamenti from './modules/pagamenti'
import mense from './modules/mense'
import colloqui from './modules/colloqui'
import elezioni from './modules/elezioni'
import documenti from './modules/documenti'
import materialeDidattico from './modules/materialeDidattico'
import serviziGiornalieri from './modules/serviziGiornalieri'
import noteTutor from './modules/noteTutor'
import tag from './modules/tag'
import ade from './modules/ade'
import verifiche from './modules/verifiche'

Vue.use(Vuex)

const store = new Vuex.Store({
  modules: {
    app,
    ade,
    auth,
    main,
    apiErrorHandler,
    // scuole,
    // studenti,
    materie,
    aggiornamenti,
    agenda,
    annotazioni,
    argomenti,
    assenze,
    compiti,
    noteDisciplinari,
    orario,
    pagelle,
    voti,
    comunicazioni,
    messageEditor,
    alternanza,
    pagamenti,
    colloqui,
    mense,
    elezioni,
    documenti,
    materialeDidattico,
    serviziGiornalieri,
    tag,
    noteTutor,
    verifiche
  },
  plugins: []
})

export default store
