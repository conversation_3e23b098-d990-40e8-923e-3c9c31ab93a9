<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Add to Cart Animation</title>
    <style>
        body {
            font-family: 'Ubuntu', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        .test-buttons {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-family: inherit;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        
        .btn-primary {
            background: #2c5aa0;
            color: white;
        }
        
        .btn-primary:hover {
            background: #1e3d6f;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #545b62;
        }
        
        .product-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }
        
        .product-modal.show {
            display: flex;
        }
        
        .product-modal-content {
            background: white;
            border-radius: 8px;
            width: 95%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }
        
        .modal-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #f8f9fa;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .modal-title {
            font-weight: 600;
            font-size: 1.3rem;
            margin: 0;
            color: #333;
        }
        
        .close-button {
            background: none;
            border: none;
            font-size: 1.5rem;
            cursor: pointer;
            color: #666;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background-color 0.2s;
        }
        
        .close-button:hover {
            background-color: #e9ecef;
        }
        
        .modal-body {
            padding: 20px;
        }
        
        .animation-demo {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .animation-demo h4 {
            margin-top: 0;
            color: #2c5aa0;
        }
        
        .animation-list {
            list-style-type: none;
            padding: 0;
        }
        
        .animation-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .animation-list li:last-child {
            border-bottom: none;
        }
        
        .animation-list li::before {
            content: "✨ ";
            margin-right: 8px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>Add to Cart Animation Test</h1>
        
        <div class="test-section">
            <h3>Animation Features</h3>
            <div class="animation-demo">
                <h4>🛒 Cart Animations</h4>
                <ul class="animation-list">
                    <li><strong>Cart Icon Bounce:</strong> Cart icon bounces and wiggles when items are added</li>
                    <li><strong>Color Change:</strong> Cart temporarily changes to green to indicate success</li>
                    <li><strong>Scale Effect:</strong> Cart grows slightly and then returns to normal size</li>
                    <li><strong>Hover Effects:</strong> Smooth hover animations with elevation</li>
                </ul>
            </div>
            
            <div class="animation-demo">
                <h4>🛍️ Add to Cart Button Animations</h4>
                <ul class="animation-list">
                    <li><strong>Button Pulse:</strong> Button scales down then up when clicked</li>
                    <li><strong>Success Color:</strong> Button briefly turns green to show success</li>
                    <li><strong>Ripple Effect:</strong> White ripple expands from center when clicked</li>
                    <li><strong>Smooth Transitions:</strong> All state changes are smoothly animated</li>
                </ul>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Test Product Detail Modal</h3>
            <p>Click the button below to open a product detail modal and test the "Add to Cart" button animation.</p>
            
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="openProductModal()">
                    Open Product Detail Modal
                </button>
                <button class="btn btn-secondary" onclick="clearCart()">
                    Clear Cart
                </button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>Quick Add Tests</h3>
            <p>Use these buttons to quickly test cart animations without opening the modal.</p>
            
            <div class="test-buttons">
                <button class="btn btn-primary" onclick="addTestProduct1()">
                    Add T-Shirt
                </button>
                <button class="btn btn-primary" onclick="addTestProduct2()">
                    Add Notebook
                </button>
                <button class="btn btn-primary" onclick="addTestProduct3()">
                    Add Pen
                </button>
            </div>
        </div>
    </div>

    <!-- Product Modal -->
    <div id="productModal" class="product-modal" onclick="closeProductModal(event)">
        <div class="product-modal-content">
            <div class="modal-header">
                <h3 class="modal-title">School T-Shirt</h3>
                <button class="close-button" onclick="closeProductModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div id="productDetailContainer"></div>
            </div>
        </div>
    </div>

    <!-- Import the components -->
    <script type="module">
        // Import the components
        import './src/components/WebComponents/CarrelloWebComponent.js';
        import './src/components/WebComponents/ProductDetailWebComponent.js';
        
        // Create and add the cart component to the page
        const cartComponent = document.createElement('carrello-web-component');
        document.body.appendChild(cartComponent);
        
        // Store references for testing
        window.testCartComponent = cartComponent;
        
        // Sample product data
        window.sampleProduct = {
            id: 1,
            name: 'School T-Shirt',
            description: 'High-quality cotton t-shirt with school logo. Perfect for daily wear.',
            category: 'Clothing',
            price: 15.00,
            available: true,
            immagini: [],
            characteristics: {
                required: true,
                options: [
                    { id: 'size-s', name: 'Small', available: 10 },
                    { id: 'size-m', name: 'Medium', available: 15 },
                    { id: 'size-l', name: 'Large', available: 8 }
                ]
            }
        };
        
        // Test functions
        window.openProductModal = function() {
            const modal = document.getElementById('productModal');
            const container = document.getElementById('productDetailContainer');
            
            // Create product detail component
            const productDetail = document.createElement('product-detail-web-component');
            productDetail.setProduct(window.sampleProduct);
            
            // Handle add to cart from product detail
            productDetail.addEventListener('add-to-cart', event => {
                const cartItem = event.detail;
                // Transform the cart item to match expected format
                cartComponent.addItem({
                    productId: cartItem.product.id,
                    productName: cartItem.product.name,
                    quantity: cartItem.quantity,
                    selectedOptions: cartItem.selectedOptions,
                    price: cartItem.product.price
                });
                closeProductModal();
            });
            
            // Clear container and add product detail
            container.innerHTML = '';
            container.appendChild(productDetail);
            
            // Show modal
            modal.classList.add('show');
        };
        
        window.closeProductModal = function(event) {
            if (event && event.target !== event.currentTarget && !event.target.classList.contains('close-button')) {
                return;
            }
            const modal = document.getElementById('productModal');
            modal.classList.remove('show');
        };
        
        window.addTestProduct1 = function() {
            cartComponent.addItem({
                productId: 1,
                productName: 'School T-Shirt',
                quantity: 1,
                selectedOptions: { size: 'M', color: 'Blue' },
                price: 15.00
            });
        };
        
        window.addTestProduct2 = function() {
            cartComponent.addItem({
                productId: 2,
                productName: 'Exercise Notebook',
                quantity: 2,
                selectedOptions: {},
                price: 3.50
            });
        };
        
        window.addTestProduct3 = function() {
            cartComponent.addItem({
                productId: 3,
                productName: 'School Pen',
                quantity: 1,
                selectedOptions: { color: 'Blue', type: 'Ballpoint' },
                price: 2.00
            });
        };
        
        window.clearCart = function() {
            const items = [...cartComponent.state.cartItems];
            items.forEach(item => {
                cartComponent.removeItem(item.id);
            });
        };
        
        // Listen for cart events
        cartComponent.addEventListener('cart-updated', function(event) {
            console.log('Cart updated:', event.detail);
        });
    </script>
</body>
</html>
