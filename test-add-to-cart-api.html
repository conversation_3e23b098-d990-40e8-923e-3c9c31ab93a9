<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Add to Cart API</title>
    <style>
        body {
            font-family: 'Ubuntu', sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #2c5aa0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: 'Ubuntu', sans-serif;
        }
        .btn {
            background: #2c5aa0;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-family: 'Ubuntu', sans-serif;
            margin-right: 10px;
        }
        .btn:hover {
            background: #1e3d6f;
        }
        .btn.secondary {
            background: #6c757d;
        }
        .btn.secondary:hover {
            background: #545b62;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }
        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .modal-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow-y: auto;
        }
        .close-btn {
            float: right;
            font-size: 24px;
            cursor: pointer;
            color: #999;
        }
        .close-btn:hover {
            color: #333;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Add to Cart API Integration</h1>
        <p>This page tests the ProductDetailWebComponent with real API integration for adding products to cart.</p>

        <div class="test-section">
            <h3>Authentication Parameters</h3>
            <div class="form-group">
                <label for="authToken">Auth Token:</label>
                <input type="text" id="authToken" placeholder="Enter JWT token">
            </div>
            <div class="form-group">
                <label for="studentId">Student ID:</label>
                <input type="text" id="studentId" placeholder="e.g., 123" value="123">
            </div>
            <div class="form-group">
                <label for="schoolYear">School Year:</label>
                <input type="text" id="schoolYear" placeholder="e.g., 2024_2025" value="2024_2025">
            </div>
            <div class="form-group">
                <label for="nextApiUrl">Next API URL (for mobile, leave empty for desktop):</label>
                <input type="text" id="nextApiUrl" placeholder="e.g., https://demo4.registroelettronico.com">
            </div>
            <button class="btn" onclick="updateAuthParams()">Update Auth Parameters</button>
        </div>

        <div class="test-section">
            <h3>Product Detail with Add to Cart</h3>
            <p>Click the button below to open a product detail modal with add to cart functionality.</p>
            <button class="btn" onclick="openProductModal()">Open Product Detail Modal</button>
            <div id="status"></div>
        </div>

        <div class="test-section">
            <h3>Test Results</h3>
            <div id="testResults">
                <p>No tests run yet.</p>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <div id="productModal" class="modal">
        <div class="modal-content">
            <span class="close-btn" onclick="closeProductModal()">&times;</span>
            <h2 id="modalTitle">Product Detail</h2>
            <div id="productDetailContainer"></div>
        </div>
    </div>

    <!-- Import the components -->
    <script type="module">
        // Import the components
        import './src/components/WebComponents/ProductDetailWebComponent.js';
        
        // Sample product data for testing
        window.sampleProduct = {
            id: 1,
            name: "Test Product",
            description: "This is a test product for add to cart functionality",
            category: "Test Category",
            price: 15.99,
            originalPrice: 19.99,
            discountActive: true,
            available: true,
            stock: 10,
            multipleOrderAllowed: true,
            immagini: [],
            characteristics: {
                description: "Size",
                required: true,
                options: [
                    { label: "Small", available: 5 },
                    { label: "Medium", available: 3 },
                    { label: "Large", available: 2 }
                ]
            }
        };

        // Global functions for testing
        window.updateAuthParams = function() {
            const authToken = document.getElementById('authToken').value;
            const studentId = document.getElementById('studentId').value;
            const schoolYear = document.getElementById('schoolYear').value;
            const nextApiUrl = document.getElementById('nextApiUrl').value;

            window.testAuthParams = {
                authToken,
                apiParams: {
                    studentId,
                    schoolYear,
                    nextApiUrl: nextApiUrl || null
                }
            };

            document.getElementById('status').innerHTML = 
                '<div class="status success">Authentication parameters updated!</div>';
        };

        window.openProductModal = function() {
            if (!window.testAuthParams) {
                document.getElementById('status').innerHTML = 
                    '<div class="status error">Please set authentication parameters first!</div>';
                return;
            }

            const modal = document.getElementById('productModal');
            const container = document.getElementById('productDetailContainer');
            
            // Create product detail component
            const productDetail = document.createElement('product-detail-web-component');
            productDetail.setProduct(window.sampleProduct);
            
            // Set authentication parameters
            if (window.testAuthParams.authToken) {
                productDetail.setAttribute('auth-token', window.testAuthParams.authToken);
            }
            if (window.testAuthParams.apiParams) {
                productDetail.setAttribute('api-params', JSON.stringify(window.testAuthParams.apiParams));
            }

            // Handle add to cart success
            productDetail.addEventListener('add-to-cart-success', event => {
                console.log('Add to cart success:', event.detail);
                document.getElementById('testResults').innerHTML = `
                    <div class="status success">
                        <strong>Add to Cart Success!</strong><br>
                        Product: ${event.detail.product.name}<br>
                        Quantity: ${event.detail.quantity}<br>
                        API Response: ${JSON.stringify(event.detail.apiResponse, null, 2)}
                    </div>
                `;
            });

            // Handle add to cart error
            productDetail.addEventListener('add-to-cart-error', event => {
                console.error('Add to cart error:', event.detail);
                document.getElementById('testResults').innerHTML = `
                    <div class="status error">
                        <strong>Add to Cart Error!</strong><br>
                        Product: ${event.detail.product.name}<br>
                        Error: ${event.detail.message}<br>
                        Details: ${JSON.stringify(event.detail.error, null, 2)}
                    </div>
                `;
            });

            // Clear container and add product detail
            container.innerHTML = '';
            container.appendChild(productDetail);

            // Show modal
            modal.classList.add('show');
        };

        window.closeProductModal = function() {
            const modal = document.getElementById('productModal');
            modal.classList.remove('show');
        };

        // Close modal when clicking outside
        document.getElementById('productModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeProductModal();
            }
        });

        // Initialize with default auth params
        window.updateAuthParams();
    </script>
</body>
</html>
